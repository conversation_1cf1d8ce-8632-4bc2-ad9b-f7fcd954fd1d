<div align="center">
    <br>
        <img 
            src="./app/assets/images/logo.png" 
            alt="Saayam"  
            height="100%" 
            width="40%" 
            style="
                flot: left;
                border: 2.5px solid #4AD7E6;
                border-radius: 20px;
                padding: 15px;
            "
        />
    <br>
</div>

# Babyauto App

Babyauto App : Babyauto has been fully dedicated to the manufacture of high quality child safety seats and accessories. Pioneers in the improvement of driving protection technology, we collaborate with the University of Zaragoza and Motorland’s Impact Laboratory to carry out exhaustive crash tests, prototyping, fitting in different cars, etc.

> **Note**: We have been working every day for more than 25 years to increase protection of children in the car. That’s why you can travel with peace of mind and they can travel with all the guarantees and comfort they deserve.

```bash
# Babyauto - Design, safety and comfort.
  Support the little ones do not run unnecessary risks on the road.
```

## Getting Started

> **Note**: Make sure you have completed the [React Native - Environment Setup](https://reactnative.dev/docs/environment-setup) instructions till "Creating a new application" step, before proceeding.

### For Setup

- Use this all command in CMD for windows user and terminal for Linux users

### Instructions

1.  For running this app you must install all above Prerequisites and set all
    environment paths correctly.

    ### Setting up the development environment

        (https://reactnative.dev/docs/environment-setup)

2.  For debugging app you must run `yarn start` in the project directory and chrome
    browser opens this link [debugger](http://localhost:8081/debugger-ui/) for debugging your app and also you can refer this link for [steps](https://reactnative.dev/docs/debugging)

### Installation of dependencies

```bash
# Clone with SSH
<NAME_EMAIL>:chillbaby/chillbaby-rn73-app.git

# Run yarn command will install all modules listed as dependencies in package.json.
yarn

# Run yarn start command will start the metro server on your computer.
yarn start
```

#### Buildinig an APK

1. Go to the project directory and type `yarn`.

2. Now you can build a debug or release APK file by running scrpits like `yarn debug` or `yarn release` or follow the next steps to make manually.

3. Now type `gradlew assembledebug` or `gradlew assemblerelease` for windows user. For linux user `sudo ./gradlew assembledebug` or `sudo ./gradlew assemblerelease`.

4. Now type `gradlew installdebug` or `gradlew installrelease` for windows user. For linux user `sudo ./gradlew installdebug` or `sudo ./gradlew installrelease`.

#### Testing Credentials:

```
Phone: *************
Password: 123456
```

## README AUTHOR

Dinesh Prajapati
Senior Developer, Groovy Technoweb Pvt. Ltd

#### Groovy Technolabs Pvt. Ltd. - [<EMAIL>](https://www.groovyweb.co)

---
