# CarPlay Integration for ChillBaby App

This document describes the CarPlay integration implemented for the ChillBaby baby monitoring app, focusing on notification and alert functionality.

## Overview

The CarPlay integration allows users to receive baby monitoring notifications and alerts directly on their car's CarPlay display, ensuring they stay informed about their baby's status while driving safely.

## Features Implemented

### 1. Notification System
- **Baby Monitor Alerts**: Temperature, humidity, and device status notifications
- **Firebase Notifications**: Integration with existing Firebase messaging system
- **Priority Levels**: Info, warning, and critical alert types
- **Queue Management**: Notifications are queued when CarPlay is not connected

### 2. Alert System
- **Interactive Alerts**: Dialog-style alerts with action buttons
- **Baby-Specific Alerts**: Specialized alerts for temperature and humidity thresholds
- **Emergency Actions**: Quick access to emergency calling functionality
- **Device Status**: Connection and battery level monitoring

### 3. CarPlay Templates
- **List Template**: Main interface showing recent alerts and device status
- **Alert Template**: Modal alerts for critical notifications
- **Banner Notifications**: Non-intrusive notifications for general updates

## Files Added/Modified

### New Files
- `app/services/CarPlayService.js` - Main CarPlay service handling all CarPlay interactions
- `app/components/CarPlayIntegration.js` - React component integrating CarPlay with Redux store
- `app/components/CarPlayTestButton.js` - Test component for development and debugging

### Modified Files
- `ios/chillbaby/chillbaby.entitlements` - Added CarPlay entitlements
- `ios/Podfile` - Added react-native-carplay dependency
- `app/navigation/index.js` - Integrated CarPlay component
- `app/components/Common/RemotePushController.js` - Added CarPlay notification forwarding

## Setup Instructions

### 1. iOS Configuration
The following entitlements have been added to `ios/chillbaby/chillbaby.entitlements`:
```xml
<key>com.apple.developer.carplay-driving-task</key>
<true/>
<key>com.apple.developer.carplay-communication</key>
<true/>
```

### 2. Dependencies
- `react-native-carplay` package has been installed and configured
- iOS pods have been updated to include CarPlay framework

### 3. Integration Points
- CarPlay service automatically initializes when the app starts on iOS
- Notifications from Firebase are automatically forwarded to CarPlay
- Redux store changes trigger CarPlay updates for device status and sensor data

## Testing Instructions

### Prerequisites
1. iOS device (iPhone) with iOS 12.0 or later
2. CarPlay-enabled vehicle OR Xcode CarPlay Simulator
3. ChillBaby app installed and running

### Using CarPlay Simulator (Development)
1. Open Xcode
2. Go to `Window > Devices and Simulators`
3. Select your connected iOS device
4. Click "Use for Development"
5. In Xcode, go to `Hardware > External Displays > CarPlay`
6. Connect your device and the CarPlay simulator will appear

### Using Physical CarPlay
1. Connect your iOS device to a CarPlay-enabled vehicle via USB or wireless
2. Launch the ChillBaby app
3. The app should appear in the CarPlay interface

### Testing Functionality

#### 1. Basic Connection Test
```javascript
// Check if CarPlay is connected
CarPlayService.isCarPlayConnected()
```

#### 2. Test Notifications
Use the `CarPlayTestButton` component (add it to any screen for testing):
- Test basic notifications
- Test alert dialogs
- Test baby-specific alerts
- Check CarPlay status

#### 3. Test Real Scenarios
1. **Temperature Alert**: Simulate high/low temperature readings
2. **Device Disconnection**: Disconnect baby monitor device
3. **Firebase Notifications**: Send test notifications through Firebase
4. **Battery Alerts**: Simulate low battery conditions

## Usage Examples

### Showing a Basic Notification
```javascript
import CarPlayService from '../services/CarPlayService';

CarPlayService.showNotification({
  title: 'Baby Monitor',
  message: 'Temperature is normal',
  type: 'info'
});
```

### Showing a Critical Alert
```javascript
CarPlayService.showBabyAlert({
  type: 'temperature_high',
  temperature: 38.5,
  timestamp: new Date().toISOString()
});
```

### Checking CarPlay Status
```javascript
const isConnected = CarPlayService.isCarPlayConnected();
console.log('CarPlay connected:', isConnected);
```

## Notification Types

### Baby Monitor Alerts
- `temperature_high` - High temperature warning
- `temperature_low` - Low temperature warning  
- `humidity_alert` - Humidity level warning
- `device_disconnected` - Device connection lost

### General Notifications
- `info` - General information
- `warning` - Warning messages
- `critical` - Critical alerts requiring immediate attention

## Safety Considerations

1. **Driver Distraction**: Notifications are designed to be glanceable and non-intrusive
2. **Emergency Access**: Critical alerts include quick access to emergency calling
3. **Voice Integration**: Future enhancement could include Siri integration
4. **Automatic Dismissal**: Alerts automatically dismiss to prevent screen clutter

## Troubleshooting

### CarPlay Not Connecting
1. Ensure device is properly connected to CarPlay system
2. Check that CarPlay is enabled in iOS Settings > General > CarPlay
3. Verify app has CarPlay entitlements

### Notifications Not Appearing
1. Check CarPlay connection status
2. Verify notification permissions are granted
3. Check console logs for error messages

### Development Issues
1. Use Xcode CarPlay Simulator for testing
2. Check device logs for CarPlay-related errors
3. Ensure proper code signing and entitlements

## Future Enhancements

1. **Siri Integration**: Voice commands for checking baby status
2. **Quick Actions**: Shortcuts for common tasks
3. **Historical Data**: View temperature/humidity trends
4. **Multiple Device Support**: Support for multiple baby monitors
5. **Customizable Alerts**: User-configurable alert thresholds

## Support

For issues related to CarPlay integration:
1. Check device compatibility
2. Verify CarPlay system compatibility
3. Review console logs for error messages
4. Test with CarPlay Simulator first

## Dependencies

- `react-native-carplay`: ^2.4.1-beta.0
- iOS 12.0+
- CarPlay-enabled vehicle or simulator
