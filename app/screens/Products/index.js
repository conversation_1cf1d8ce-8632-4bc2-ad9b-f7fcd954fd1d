/* eslint-disable quotes */
import { useTheme } from "@react-navigation/native";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Dimensions,
  FlatList,
  Image,
  Modal,
  RefreshControl,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import FAIcon from "react-native-vector-icons/FontAwesome";
import Toast from "react-native-simple-toast";
import { useSelector } from "react-redux";
import { flattenDeep, isArray, isEmpty } from "lodash";
import CButton from "../../components/CButton";
import CHeader from "../../components/CHeader";
import DropDown from "../../components/DropDown";
import GradientBack from "../../components/gradientBack";
import BaseSetting from "../../config/setting";
import { FontFamily } from "../../config/typography";
import { translate } from "../../lang/Translate";
import { getApiData } from "../../utils/apiHelper";
import styles from "./styles";
import {
  enableAnimateInEaseOut,
  sendErrorReport,
} from "../../utils/commonFunction";

/**
 *
 *@module Product
 *
 */
export default function Products({ navigation, route }) {
  const catalouge = route?.params?.catalouge;

  const colors = useTheme();
  const BaseColor = colors.colors;

  const token = useSelector((state) => state.auth.accessToken);
  const brandToken = useSelector((state) => state.auth.brandToken);

  const [filterModal, setfilterModal] = useState(false);
  const [selectedCat, setselectedCat] = useState("");
  const [selectedFilter, setselectedFilter] = useState("");
  const [selectedChara, setselectedChara] = useState("");
  const [filterData, setFilterData] = useState("");
  const [sortingObj, setSortingObj] = useState({});

  const [productList, setproductList] = useState([]);
  const [refreshing, setRefreshing] = useState(false);

  const [pageLoad, setPageLoad] = useState(true);
  const [page, setPage] = useState(1);
  const [nextPage, setNextPage] = useState(false);
  const [nextLoading, setNextLoading] = useState(false);
  const [categoryList, setCategoryList] = useState([]);
  const [ecomProductList, setEcomProductList] = useState([]);
  const [inventoryQuantity, setInventoryQuantity] = useState(0);
  const [relInfo, setRel] = useState("");
  const [pageInfo, setPageInfo] = useState("");
  const [filterListF, setFilterList] = useState([]);

  const characterstics = [
    // { id: 1, name: "Best Seller" },
    { id: 1, name: "Alphabetically - A Z" },
    { id: 2, name: "Alphabeticall - Z A" },
    // { id: 4, name: "Price, low to high" },
    // { id: 5, name: "Price, high to low" },
    { id: 3, name: "Date - Old to recent" },
    { id: 4, name: "Date - Recent to old" },
  ];
  useEffect(() => {
    setPage(1);
    // setproductList([]);
    setEcomProductList([]);
    // getProductList();
    getEComProductList();
    getProductCategories();
  }, []);

  /** this function for get Product List
   * @function getProductList
   * @param {object} data token, per_page, page, category_id
   */
  const getProductList = (type) => {
    const headers = {
      "Content-Type": "application/json",
      authorization: token ? `Bearer ${token}` : "",
    };

    // console.log(page);

    const data = {
      token: brandToken,
      per_page: 50,
      page,
      category_id: type === "reset" ? null : selectedCat?.id || null,
    };
    console.log("getProductList -> data", data);

    getApiData(BaseSetting.endpoints.productList, "POST", data, headers)
      .then((response) => {
        console.log(".then -> response", response);
        if (response.success) {
          const tempPArr = flattenDeep([
            type === "filter" || type === "reset" ? [] : productList,
            response.data,
          ]);
          setproductList(tempPArr);
          setPageLoad(false);
          if (response?.next_enable === 1) {
            setNextPage(true);
          } else {
            setNextPage(false);
          }
          setNextLoading(false);
        } else {
          Toast.show(response.message);
        }
      })
      .catch((err) => {
        console.log("ERRR", err);
        Toast.show("Something went wrong while getting product list");
        sendErrorReport(err, "get_product_list");
      });
  };

  /** this function for get Product List
   * @function getEComProductList
   * @param {object} data token, per_page, page, category_id
   */
  const getEComProductList = (type) => {
    const headers = {
      "Content-Type": "application/json",
      authorization: token ? `Bearer ${token}` : "",
    };
    setPageLoad(true);
    // console.log("--------relInfo, pageInfo", sortingObj, relInfo);
    // const data = {};
    // if (type === "filter" || type === "reset") {
    //   setRel("");
    // }
    console.log("--------relInfo,", relInfo);
    if (relInfo !== "privious") {
      const data = {
        collection_id: catalouge.collection_id,
        limit: 50,
        rel: relInfo,
        page_info: pageInfo,
        per_page: 50,
        page,
        // filter: type === "reset" ? null : filterData || null,
        product_tag: type === "reset" ? "" : filterData || "",
        sort_obj: type === "reset" ? "" : sortingObj || "",
        platform: "app",
      };
      console.log("getEComProductList -> data-----", data);

      getApiData(BaseSetting.endpoints.ecomProductList, "POST", data, headers)
        .then((response) => {
          if (response.success) {
            console.log("ecom RESSP---length-", response.data.length);
            // response.data.map((p) => {
            // console.log("obj----------foreach-", p.title);

            // if (getSoldOut(p)) {
            // const index = response.data.indexOf(p);
            // if (index > -1) {
            //   // only splice array when item is found
            //   response.data.splice(index, 1); // 2nd parameter means remove one item only
            // }
            // }
            // });

            const tempPArr = flattenDeep([
              type === "filter" || type === "reset" ? [] : ecomProductList,
              response.data,
            ]);

            setTimeout(() => {
              setEcomProductList(tempPArr);
            }, 1000);

            if (response.page_info_next) {
              console.log("page_info -------", response.page_info_next);
              setPageInfo(response.page_info_next);
            }
            if (response.rel) {
              console.log("rel------response-", response.rel);
              setRel(response.rel);
            }
            if (response.filter_for) {
              if (response.filter_for.length > 0) {
                console.log("-==-> filter for---", response.filter_for);
                if (filterData === "") {
                  const tempPArrFilter = flattenDeep([
                    filterListF,
                    response.filter_for,
                  ]);
                  setFilterList(tempPArrFilter);
                }
              }
            }

            console.log("------ecom p list", ecomProductList);
            // setproductList(tempPArr);
            console.log("-==-> next_enable for", response?.next_enable);
            if (response?.next_enable === 1) {
              setNextPage(true);
            } else {
              setNextPage(false);
            }
            setNextLoading(false);
            setPageLoad(false);
          } else {
            setPageLoad(false);
            Toast.show(response.message);
          }
        })
        .catch((err) => {
          setPageLoad(false);
          console.log("ERRR---------", err);
          Toast.show("Something went wrong while getting ECOM product list");
          sendErrorReport(err, "get_product_list");
        });
    }
  };

  // this function for get product categories
  /** this function for get product categories
   * @function getProductCategories
   * @param {object} data {}
   */
  async function getProductCategories() {
    const headers = {
      "Content-Type": "application/json",
      authorization: token ? `Bearer ${token}` : "",
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.getProductCategories,
        "POST",
        {},
        headers
      );

      if (response.success) {
        if (isArray(response.data)) {
          setCategoryList(response.data);
        }
      } else {
        Toast.show(response.message);
      }
    } catch (error) {
      console.log("error ===", error);
      sendErrorReport(error, "get_prod_cat");
    }
  }

  // this function is used when list data reached to limit while scrolling
  const onEndReached = () => {
    setNextLoading(true);
    if (pageInfo !== "") {
      getEComProductList();
    }
    // getEComProductList();
    console.log(".then -> on end called -------");
    if (nextPage) {
      const tempPage = page + 1;
      console.log(".then -> on end -------", nextPage);
      setPage(tempPage);
      // getEComProductList();
      // getProductList();
    }
  };

  // this function is used when list is refresh from top
  const onRefresh = React.useCallback(() => {
    // setproductList([]);
    // getProductList();
    setRel("");
    setPageInfo("");
    getEComProductList();
  }, []);

  /** this function for get Product View
   * @function getProductView
   * @param {object} data product_id, type
   */
  async function getProductView(id, type) {
    const headers = {
      "Content-Type": "application/json",
      authorization: token ? `Bearer ${token}` : "",
    };
    try {
      const response = await getApiData(
        BaseSetting.endpoints.addproductaction,
        "POST",
        {
          product_id: id,
          type,
        },
        headers
      );

      if (response.success) {
        // setPostLike(response.data);
        // Toast.show(type);
      } else {
        Toast.show(response.message);
      }
    } catch (error) {
      sendErrorReport(error, "get_feed_post");
      console.log("feed post error ===", error);
    }
  }

  /** this function for get Product View
   * @function getEcomProductView
   * @param {object} data product_id, type
   */
  async function getEcomProductView(id, type) {
    const headers = {
      "Content-Type": "application/json",
      authorization: token ? `Bearer ${token}` : "",
    };
    try {
      const response = await getApiData(
        BaseSetting.endpoints.addEcomProductAction,
        "POST",
        {
          product_id: id,
          type,
        },
        headers
      );

      if (response.success) {
        console.log("ecom action----", response);
        // setPostLike(response.data);
        // Toast.show(type);
      } else {
        Toast.show(response.message);
      }
    } catch (error) {
      sendErrorReport(error, "get_feed_post");
      console.log("feed post error ===", error);
    }
  }

  const renderListFooter = () => {
    if (!nextPage) {
      return (
        <Text
          style={{
            width: "100%",
            textAlign: "center",
            textAlignVertical: "center",
            height: 30,
            color: BaseColor.whiteColor,
          }}
        >
          No more products
        </Text>
      );
    }
    if (nextLoading) {
      return (
        <ActivityIndicator
          style={{ color: BaseColor.whiteColor, height: 60 }}
          color={BaseColor.whiteColor}
        />
      );
    }
    // return <ActivityIndicator style={{ color: BaseColor.whiteColor }} />;
    return null;
  };

  // this function is used to find discount from product list
  const getDiscount = (item) => {
    let discount = 0;
    if (item?.variants) {
      item?.variants.map((i) => {
        if (i?.compare_at_price !== null) {
          if (i?.compare_at_price > 0) {
            if (i?.inventory_quantity > 0) {
              discount = Math.round(
                (100 * (i?.compare_at_price - i?.price)) / i?.compare_at_price
              ).toString();
            }
          }
        }
      });
    }
    return discount;
  };

  // get Price
  const getPrice = (item) => {
    let price = 0;
    if (item?.variants) {
      item?.variants.map((i) => {
        if (i?.inventory_quantity > 0) {
          if (i?.price) {
            price = i?.price;
          }
        }
      });
    }
    return price;
  };

  // get Price
  const getCompareAtPrice = (item) => {
    let cprice = 0;
    if (item?.variants) {
      item?.variants.map((i) => {
        if (i?.inventory_quantity > 0) {
          if (i?.compare_at_price) {
            cprice = i?.compare_at_price;
          }
        }
      });
    }
    return cprice;
  };

  // this function is used to fimd soldou product from list
  const getSoldOut = (item) => {
    let soldOut = false;
    var variantsArr = [];
    var inzero = [];
    if (item?.variants) {
      item?.variants.map((i) => {
        if (i.inventory_quantity !== 0) {
          variantsArr.push(i);
        } else {
          inzero.push(i);
        }
      });
      console.log("-------variantsArr---------", variantsArr);
      console.log("=------inzero-----", inzero);
    }
    return soldOut;
  };

  // this function is used to render products listing design
  const renderProducts = ({ item, index }) => (
    // getSoldOut(item) ? null : (
    <TouchableOpacity
      style={[
        {
          backgroundColor: BaseColor.whiteColor,
          paddingBottom: 20,
          width: Dimensions.get("window").width / 2,
        },
      ]}
      onPress={() => {
        getEcomProductView(item.id, "viewed");
        navigation.navigate("ProductDetail", {
          productDetail: item,
          collectionTitle: catalouge.handle,
        });
      }}
    >
      <View style={{ marginStart: 15 }}>
        <View>
          <View style={{ width: Dimensions.get("window").width / 2.4 }}>
            <Image
              source={
                item?.image
                  ? { uri: item?.image.src }
                  : require("../../assets/images/logo.png")
              }
              style={[
                {
                  borderColor: BaseColor.black60,
                  height: Dimensions.get("window").height / 5,
                  width: Dimensions.get("window").width / 2.4,
                  resizeMode: "cover",
                },
              ]}
            />
            {getDiscount(item) !== 0 ? (
              <View
                style={{
                  backgroundColor: BaseColor.orange,
                  paddingVertical: 5,
                  paddingHorizontal: 5,
                  position: "absolute",
                  right: 0,
                  top: 0,
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <Text style={{ color: BaseColor.whiteColor, fontSize: 10 }}>
                  {getDiscount(item) !== 0 ? `Save ${getDiscount(item)}%` : ""}
                </Text>
              </View>
            ) : null}
          </View>
          <View style={{ justifyContent: "space-around" }}>
            <View style={{ paddingTop: 5 }}>
              <Text style={[styles.nameStyle, { color: BaseColor.blackColor }]}>
                {item?.title}
              </Text>
            </View>
          </View>
          <View
            style={{
              ...styles.rowStyle,
              alignItems: "center",
              paddingVertical: 5,
            }}
          >
            {getCompareAtPrice(item) > 0 ? (
              <>
                <Text
                  style={{
                    ...styles.valueStyle,
                    color: BaseColor.blackColor,
                    textDecorationLine: "line-through",
                    paddingEnd: 5,
                  }}
                >
                  €{getCompareAtPrice(item)}
                </Text>
              </>
            ) : null}
            <View style={{ ...styles.rowStyle }}>
              <Text
                style={{ ...styles.valueStyle, color: BaseColor.blackColor }}
              >
                €{getPrice(item)}
              </Text>
            </View>
          </View>
          {/* <View
              style={{
                ...styles.rowStyle,
                alignItems: "center",
                alignContent: "center",
              }}
            >
              {getStars(5)}
              <Text style={{ paddingLeft: 5, fontSize: 10 }}>6 reviews</Text>
            </View> */}
        </View>
      </View>
    </TouchableOpacity>
  );
  // );

  // <TouchableOpacity
  //   style={[styles.cardRoot, { backgroundColor: BaseColor.whiteColor }]}
  //   onPress={() => {
  //     getProductView(item.id, "viewed");
  //     navigation.navigate("ProductDetail", { productDetail: item });
  //   }}
  // >
  //   <View style={styles.rowStyle}>
  //     <Image
  //       source={
  //         item?.product_image
  //           ? { uri: item?.product_image }
  //           : require("../../assets/images/logo.png")
  //       }
  //       style={[styles.imgStyle, { borderColor: BaseColor.black60 }]}
  //     />
  //     <View
  //       style={{ marginStart: 12, justifyContent: "space-around", flex: 1 }}
  //     >
  //       <Text style={[styles.nameStyle, { color: BaseColor.blackColor }]}>
  //         {item?.product_name}
  //       </Text>
  //       <Text style={[styles.catStyle, { color: BaseColor.black70 }]}>
  //         {item?.category_name}
  //       </Text>
  //     </View>
  //     <FAIcon
  //       name="angle-right"
  //       size={24}
  //       color={BaseColor.blackColor}
  //       style={{ alignSelf: "center" }}
  //     />
  //   </View>
  //   <View style={[styles.divider, { backgroundColor: BaseColor.black40 }]} />
  //   <View style={{ ...styles.rowStyle, justifyContent: "space-between" }}>
  //     <View style={{ ...styles.rowStyle }}>
  //       <Text style={[styles.subTitle, { color: BaseColor.blackColor }]}>
  //         SKU :
  //       </Text>
  //       <Text style={[styles.valueStyle, { color: BaseColor.blackColor }]}>
  //         {item?.sku_code}
  //       </Text>
  //     </View>
  //     <View style={{ ...styles.rowStyle, alignItems: "center" }}>
  //       <FAIcon
  //         name="dollar"
  //         size={14}
  //         color={BaseColor.green}
  //         style={{ marginEnd: 4 }}
  //       />
  //       <Text style={{ ...styles.valueStyle, color: BaseColor.green }}>
  //         {item?.sell_price}
  //       </Text>
  //     </View>
  //   </View>
  // </TouchableOpacity>

  enableAnimateInEaseOut();

  return (
    <View style={[styles.root, { backgroundColor: BaseColor.whiteColor }]}>
      {/* <GradientBack /> */}
      <View style={styles.root}>
        <CHeader
          title={catalouge.title}
          backBtn
          onLeftPress={() => {
            navigation.goBack();
          }}
          rightIconName="filter"
          onRightPress={() => {
            setfilterModal(true);
          }}
        />
        {isEmpty(ecomProductList) ? <Text>{ecomProductList}</Text> : null}
        <FlatList
          keyExtractor={(item, index) => index}
          // data={productList}
          numColumns={2}
          data={ecomProductList}
          renderItem={renderProducts}
          contentContainerStyle={{ flexGrow: 1, paddingBottom: 24 }}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          onEndReachedThreshold={0.4}
          onEndReached={onEndReached}
          style={{ marginTop: 15 }}
          // ListFooterComponent={renderListFooter}
          ListEmptyComponent={() => (
            <View style={styles.emptyComponent}>
              {pageLoad ? (
                <ActivityIndicator color={BaseColor.blackColor} />
              ) : (
                <Text
                  style={{
                    // padding: 8,
                    // width: "100%",
                    fontSize: 16,
                    color: BaseColor.blackColor,
                    textAlign: "center",
                  }}
                >
                  {translate("noProducts")}
                </Text>
              )}
            </View>
          )}
        />
      </View>
      <Modal
        style={{ flex: 1 }}
        visible={filterModal}
        transparent
        animationType="slide"
        onRequestClose={() => {
          setfilterModal(false);
        }}
      >
        <TouchableOpacity
          style={{
            flex: 1,
            backgroundColor: BaseColor.black40,
            justifyContent: "flex-end",
          }}
          onPress={() => {
            setfilterModal(false);
          }}
        >
          <View
            style={{
              backgroundColor: BaseColor.blueDark,
              padding: 24,
              borderTopEndRadius: 16,
              borderTopStartRadius: 16,
            }}
          >
            <Text
              style={{
                color: BaseColor.whiteColor,
                fontSize: 16,
                marginVertical: 8,
                fontFamily: FontFamily.default,
                fontWeight: "bold",
              }}
            >
              FILTER
            </Text>
            {/* <DropDown
              placeholder="Select Category"
              data={categoryList}
              style={{ borderRadius: 12, marginEnd: 4 }}
              valueProp="category_name"
              onSelect={(val) => {
                // setHeight(val);
                setselectedCat(val);
              }}
              selectedObject={selectedCat}
            /> */}
            <DropDown
              placeholder="Sorting"
              data={characterstics}
              style={{ borderRadius: 12, marginEnd: 4 }}
              valueProp="name"
              onSelect={(val) => {
                setselectedChara(val);
                console.log("---------on select--", val, val.name);
                let obj = {};
                if (val.name === "Alphabetically - A Z") {
                  obj = { sort_value: "title", sort_type: "ascend" };
                } else if (val.name === "Alphabeticall - Z A") {
                  obj = { sort_value: "title", sort_type: "desc" };
                } else if (val.name === "Date - Old to recent") {
                  obj = {
                    sort_value: "created_at",
                    sort_type: "ascend",
                  };
                } else if (val.name === "Date - Recent to old") {
                  obj = { sort_value: "created_at", sort_type: "desc" };
                }
                setSortingObj(obj);
                console.log("---------filter---val--", obj, sortingObj);
              }}
              selectedObject={selectedChara}
            />
            <View style={{ marginTop: 16 }}>
              <DropDown
                placeholder="Filter"
                data={filterListF}
                style={{ borderRadius: 12, marginEnd: 4 }}
                valueProp="product_tag"
                disable
                onSelect={(val) => {
                  setPageInfo("");
                  setselectedFilter(val);
                  setFilterData(val.product_tag);
                }}
                selectedObject={selectedFilter}
              />
            </View>
            <View style={{ flexDirection: "row", marginBottom: 16 }}>
              <CButton
                title="SEARCH"
                style={{
                  backgroundColor: BaseColor.whiteColor,
                  borderRadius: 8,
                  marginTop: 16,
                  marginEnd: 4,
                  flex: 1,
                }}
                titleStyle={{
                  color: BaseColor.blackColor,
                  fontWeight: "bold",
                }}
                onPress={() => {
                  console.log("-------in search-");
                  // navigation.navigate('Login');
                  setfilterModal(false);
                  // setproductList([]);
                  setEcomProductList([]);
                  setPage(1);
                  // setPageInfo("");
                  setPageLoad(true);
                  // getProductList("filter");
                  getEComProductList("filter");
                }}
              />
              <CButton
                title="RESET"
                style={{
                  backgroundColor: BaseColor.orange,
                  borderRadius: 8,
                  marginTop: 16,
                  marginStart: 4,
                  flex: 1,
                }}
                titleStyle={{
                  color: BaseColor.whiteColor,
                  fontWeight: "bold",
                }}
                onPress={() => {
                  setselectedCat({});
                  setPage(1);
                  setRel("");
                  setPageInfo("");
                  getEComProductList("reset");
                  // getProductList("reset");
                  // setfilterModal(false);
                  // navigation.navigate('Login');
                }}
              />
            </View>
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
}
