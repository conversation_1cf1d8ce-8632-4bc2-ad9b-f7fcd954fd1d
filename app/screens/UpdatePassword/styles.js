import { Dimensions, StyleSheet } from 'react-native';
import BaseColor from '../../config/colors';
import { FontFamily } from '../../config/typography';

const { height: dHeight, width: dWidth } = Dimensions.get('window');

const styles = StyleSheet.create({
  root: {
    flex: 1,
  },
  mainContainer: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  closeBtn: {
    height: 40,
    width: 40,
    borderRadius: 20,
    alignSelf: 'flex-end',
  },
  loginBtn: {
    alignSelf: 'center',
    fontFamily: FontFamily.default,
    borderColor: BaseColor.blackColor,
    borderWidth: 1
  },
  mainInputStyle: {
    // flex: 1,
    justifyContent: 'center',
  },
  inputWrapper: {
    marginVertical: 7,
  },
  loginText: {
    fontSize: 20,
    color: BaseColor.whiteColor,
    fontWeight: '700',
    letterSpacing: 0.8,
    fontFamily: FontFamily.default,
  },
  loginTextView: {
    // marginTop: 40,
    marginBottom: 20,
    alignItems: 'center',
    fontFamily: FontFamily.default,
  },
  rememberText: {
    fontSize: 15,
    color: BaseColor.whiteColor,
    fontWeight: '600',
    textAlign: 'left',
  },
  associatedTest: {
    fontFamily: FontFamily.default,
    color: BaseColor.whiteColor,
    fontSize: 15,
    textAlign: 'center',
    paddingTop: 18,
  },
  lockIconStyle: {
    backgroundColor: BaseColor.whiteColor,
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    width: 80,
    height: 80,
    // borderRadius: 40,
    // elevation: 5,
    marginBottom: 20,
  },
});

export default styles;
