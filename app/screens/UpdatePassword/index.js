import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  BackHandler,
  KeyboardAvoidingView,
  ScrollView,
  Platform,
  Keyboard,
} from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useSelector} from 'react-redux';
import Toast from 'react-native-simple-toast';
import FAIcon from 'react-native-vector-icons/FontAwesome';
import styles from './styles';
import BaseColor from '../../config/colors';
import CButton from '../../components/CButton';
import CInput from '../../components/CInput';
import {translate} from '../../lang/Translate';
import {
  enableAnimateInEaseOut,
  sendErrorReport,
} from '../../utils/commonFunction';
import {getApiData} from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import GradientBack from '../../components/gradientBack';
let backPressed = 0;

/**
 *
 *@module UpdatePassword
 *
 */
function UpdatePassword({navigation}) {
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  const confirmPass = useRef();

  const [newPasswordError, setNewPasswordError] = useState(false);
  const [confirmPasswordError, setConfirmPasswordError] = useState(false);
  const [newPasswordErrorTxt, setNewPasswordErrorTxt] = useState('');
  const [confirmPasswordErrorTxt, setConfirmPasswordErrorTxt] = useState('');

  const [loader, setloader] = useState(false);
  const [done, setdone] = useState(false);
  const [anim, setanim] = useState(false);
  const [backAnim, setBackAnim] = useState(false);
  const [hideShow, setHideShow] = useState(true);
  const userId = useSelector(state => state.auth.user_id);
  const [isUpperCase, setIsUpperCase] = useState(false);
  const [isLowerCase, setIsLowerCase] = useState(false);
  const [isNu, setIsNumber] = useState(false);
  const [isSpecial, setIsSp] = useState(false);
  const [isTwelve, setIsTwelve] = useState(false);

  const [isUpperCase2, setIsUpperCase2] = useState(false);
  const [isLowerCase2, setIsLowerCase2] = useState(false);
  const [isNu2, setIsNumber2] = useState(false);
  const [isSpecial2, setIsSp2] = useState(false);
  const [isTwelve2, setIsTwelve2] = useState(false);

  const [showCheckList, setShowCheckList] = useState(false);
  const [showCheckList2, setShowCheckList2] = useState(false);
  const validation = () => {
    const passVal =
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,15}$/;
    enableAnimateInEaseOut();

    if (newPassword == '') {
      allErrorFalse();
      setNewPasswordError(true);
      setNewPasswordErrorTxt('Please enter New Password');
    } else if (!passVal.test(String(newPassword))) {
      allErrorFalse();
      setNewPasswordError(true);
      setNewPasswordErrorTxt(
        'Password must contain 8-15 characters, 1 x Upper case, 1 x Lower case, 1 x number and 1 x special character such as !,?,&',
      );
    } else if (newPassword != confirmPassword) {
      allErrorFalse();
      setConfirmPasswordError(true);
      setConfirmPasswordErrorTxt(
        'Confirm Password and New Password cannot match',
      );
    } else {
      allErrorFalse();
      updatePassword();
    }
  };

  const allErrorFalse = () => {
    setNewPasswordError(false);
    setConfirmPasswordError(false);
  };

  /** this function for update Password
   * @function updatePassword
   * @param {object} data user_id, new_password
   */
  const updatePassword = () => {
    setloader(true);
    setanim(true);
    setBackAnim(false);
    const data = {
      user_id: userId,
      new_password: newPassword,
    };

    getApiData(BaseSetting.endpoints.updatePassword, 'POST', data)
      .then(response => {
        if (response.success) {
          setTimeout(() => {
            setloader(false);
            setdone(true);
          }, 2000);
          setTimeout(() => {
            navigation.navigate('Login');
          }, 3000);
        } else {
          setanim(false);
          setBackAnim(true);
          setdone(false);
          setloader(false);
        }
      })
      .catch(err => {
        Toast.show('Something went wrong while changing password');
        sendErrorReport(err, 'changing_pass');
        setanim(false);
        setBackAnim(true);
        setdone(false);
        setloader(false);
        console.log('ERRR', err);
      });
  };

  function handleBackButtonClick() {
    if (backPressed > 0) {
      BackHandler.exitApp();
      backPressed = 0;
    } else {
      backPressed++;
      Toast.show('Press Again To Exit');
      setTimeout(() => {
        backPressed = 0;
      }, 2000);
      return true;
    }
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  function isUpper(str) {
    return /^(?=.*?[A-Z])/.test(str);
  }
  function isLower(str) {
    return /^(?=.*?[a-z])/.test(str);
  }
  function isNumericCheck(str) {
    return /^(?=.*?[0-9])/.test(str);
  }
  function isSpCheck(str) {
    return /[ `!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/.test(str);
  }
  function isTw(str) {
    if (str.length >= 12) {
      return true;
    } else {
      return false;
    }
  }

  return (
    <View style={styles.root}>
      {/* <GradientBack /> */}
      <KeyboardAvoidingView
        style={{flex: 1, justifyContent: 'center'}}
        behavior={Platform.OS === 'ios' ? 'height' : null}>
        <ScrollView
          contentContainerStyle={styles.mainContainer}
          bounces={false}>
          <View style={styles.loginTextView}>
            <View>
              <View style={styles.lockIconStyle}>
                <MaterialCommunityIcons
                  name="key-outline"
                  color={BaseColor.orange}
                  size={40}
                />
              </View>
              <Text style={[styles.loginText, {color: BaseColor.blackColor}]}>
                {translate('updatePasswordScreen')}
              </Text>
            </View>
          </View>
          <View style={styles.inputWrapper}>
            <CInput
              placeholder={translate('newPassword')}
              secureTextEntry={hideShow}
              value={newPassword}
              onChangeText={val => {
                setNewPassword(val);
                const lower = isLower(val);
                if (lower) {
                  setIsLowerCase(true);
                } else {
                  setIsLowerCase(false);
                }
                const upper = isUpper(val);
                if (upper) {
                  setIsUpperCase(true);
                } else {
                  setIsUpperCase(false);
                }
                const num = isNumericCheck(val);
                if (num) {
                  setIsNumber(true);
                } else {
                  setIsNumber(false);
                }

                const sp = isSpCheck(val);
                if (sp) {
                  setIsSp(true);
                } else {
                  setIsSp(false);
                }
                const tw = isTw(val);
                if (tw) {
                  setIsTwelve(true);
                } else {
                  setIsTwelve(false);
                }

                if (val.length <= 0) {
                  setIsUpperCase(false);
                  setIsLowerCase(false);
                  setIsNumber(false);
                  setIsTwelve(false);
                  setIsSp(false);
                }
                if (
                  isUpper(val) &&
                  isLower(val) &&
                  isNumericCheck(val) &&
                  isSpCheck(val) &&
                  isTw(val)
                ) {
                  console.log('treuuuuu');
                  setShowCheckList(false);
                } else {
                  setShowCheckList(true);
                }
              }}
              onFocus={() => {
                console.log('on focus');
                setShowCheckList(true);
              }}
              onBlur={() => {
                console.log(' on blue');
                setShowCheckList(false);
              }}
              onSubmitEditing={() => {
                confirmPass.current.focus();
              }}
              placeholderTextColor={BaseColor.blackColor}
              // iconName="unlocked-padlock"
              showError={newPasswordError}
              errorMsg={newPasswordErrorTxt}
              onShowPasswordpress={() => {
                setHideShow(!hideShow);
              }}
              hideLeftIcon
              hideRightIcon={false}
              iconName={hideShow ? 'eye-slash' : 'eye'}
            />
          </View>
          {showCheckList && (
            <View
              style={{
                marginHorizontal: 10,
                padding: 20,
                borderRadius: 10,
                shadowColor: 'purple',
                height: 150,
                width: '90%',
                backgroundColor: 'white',
                justifyContent: 'center',
                elevation: 10,
                shadowOffset: {width: 1, height: 1},
                shadowRadius: 3,
                shadowOpacity: 0.5,
              }}>
              <Text style={{fontSize: 16, fontWeight: 'bold'}}>
                {translate('pswdReq')}
              </Text>
              <View style={{flexDirection: 'row'}}>
                <FAIcon
                  name={isLowerCase ? 'check' : 'remove'}
                  size={18}
                  color={isLowerCase ? BaseColor.green : BaseColor.alertRed}
                />
                <Text style={{marginLeft: 5}}>{translate('oneLowerCase')}</Text>
              </View>

              <View style={{flexDirection: 'row'}}>
                <FAIcon
                  name={isUpperCase ? 'check' : 'remove'}
                  size={18}
                  color={isUpperCase ? BaseColor.green : BaseColor.alertRed}
                />
                <Text style={{marginLeft: 5}}>{translate('oneUpperCase')}</Text>
              </View>

              <View style={{flexDirection: 'row'}}>
                <FAIcon
                  name={isNu ? 'check' : 'remove'}
                  size={18}
                  color={isNu ? BaseColor.green : BaseColor.alertRed}
                />
                <Text style={{marginLeft: 5}}>{translate('oneNumber')}</Text>
              </View>

              <View style={{flexDirection: 'row'}}>
                <FAIcon
                  name={isSpecial ? 'check' : 'remove'}
                  size={18}
                  color={isSpecial ? BaseColor.green : BaseColor.alertRed}
                />
                <Text style={{marginLeft: 5}}>{translate('oneSp')}</Text>
              </View>
              <View style={{flexDirection: 'row'}}>
                <FAIcon
                  name={isTwelve ? 'check' : 'remove'}
                  size={18}
                  color={isTwelve ? BaseColor.green : BaseColor.alertRed}
                />
                <Text style={{marginLeft: 5}}>{translate('twLong')}</Text>
              </View>
            </View>
          )}
          <View style={styles.inputWrapper}>
            <CInput
              onSubmitEditing={() => {
                Keyboard.dismiss();
              }}
              ref={confirmPass}
              placeholder={translate('confirmPassword')}
              secureTextEntry={hideShow}
              onShowPasswordpress={() => {
                setHideShow(!hideShow);
              }}
              hideLeftIcon
              hideRightIcon={false}
              iconName={hideShow ? 'eye-slash' : 'eye'}
              value={confirmPassword}
              onChangeText={val => {
                setConfirmPassword(val);
                const lower = isLower(val);
                if (lower) {
                  setIsLowerCase2(true);
                } else {
                  setIsLowerCase2(false);
                }
                const upper = isUpper(val);
                if (upper) {
                  setIsUpperCase2(true);
                } else {
                  setIsUpperCase2(false);
                }
                const num = isNumericCheck(val);
                if (num) {
                  setIsNumber2(true);
                } else {
                  setIsNumber2(false);
                }

                const sp = isSpCheck(val);
                if (sp) {
                  setIsSp2(true);
                } else {
                  setIsSp2(false);
                }
                const tw = isTw(val);
                if (tw) {
                  setIsTwelve2(true);
                } else {
                  setIsTwelve2(false);
                }

                if (val.length <= 0) {
                  setIsUpperCase2(false);
                  setIsLowerCase2(false);
                  setIsNumber2(false);
                  setIsTwelve2(false);
                  setIsSp2(false);
                }
                if (
                  isUpper(val) &&
                  isLower(val) &&
                  isNumericCheck(val) &&
                  isSpCheck(val) &&
                  isTw(val)
                ) {
                  setShowCheckList2(false);
                } else {
                  setShowCheckList2(true);
                }
              }}
              onFocus={() => {
                setShowCheckList2(true);
              }}
              onBlur={() => {
                setShowCheckList2(false);
              }}
              placeholderTextColor={BaseColor.blackColor}
              // iconName="unlocked-padlock"
              showError={confirmPasswordError}
              errorMsg={confirmPasswordErrorTxt}
            />
            {showCheckList2 && (
              <View
                style={{
                  marginHorizontal: 10,
                  padding: 20,
                  borderRadius: 10,
                  shadowColor: 'purple',
                  height: 150,
                  width: '90%',
                  backgroundColor: 'white',
                  justifyContent: 'center',
                  elevation: 10,
                  shadowOffset: {width: 1, height: 1},
                  shadowRadius: 3,
                  shadowOpacity: 0.5,
                }}>
                <Text style={{fontSize: 16, fontWeight: 'bold'}}>
                  {translate('pswdReq')}
                </Text>
                <View style={{flexDirection: 'row'}}>
                  <FAIcon
                    name={isLowerCase2 ? 'check' : 'remove'}
                    size={18}
                    color={isLowerCase2 ? BaseColor.green : BaseColor.alertRed}
                  />
                  <Text style={{marginLeft: 5}}>
                    {translate('oneLowerCase')}
                  </Text>
                </View>

                <View style={{flexDirection: 'row'}}>
                  <FAIcon
                    name={isUpperCase2 ? 'check' : 'remove'}
                    size={18}
                    color={isUpperCase2 ? BaseColor.green : BaseColor.alertRed}
                  />
                  <Text style={{marginLeft: 5}}>
                    {translate('oneUpperCase')}
                  </Text>
                </View>

                <View style={{flexDirection: 'row'}}>
                  <FAIcon
                    name={isNu2 ? 'check' : 'remove'}
                    size={18}
                    color={isNu2 ? BaseColor.green : BaseColor.alertRed}
                  />
                  <Text style={{marginLeft: 5}}>{translate('oneNumber')}</Text>
                </View>

                <View style={{flexDirection: 'row'}}>
                  <FAIcon
                    name={isSpecial2 ? 'check' : 'remove'}
                    size={18}
                    color={isSpecial2 ? BaseColor.green : BaseColor.alertRed}
                  />
                  <Text style={{marginLeft: 5}}>{translate('oneSp')}</Text>
                </View>
                <View style={{flexDirection: 'row'}}>
                  <FAIcon
                    name={isTwelve2 ? 'check' : 'remove'}
                    size={18}
                    color={isTwelve2 ? BaseColor.green : BaseColor.alertRed}
                  />
                  <Text style={{marginLeft: 5}}>{translate('twLong')}</Text>
                </View>
              </View>
            )}
          </View>
          <View style={styles.inputWrapper}>
            <CButton
              style={styles.loginBtn}
              title={translate('forgotBtn')}
              anim
              playAnimation={anim}
              backAnim={backAnim}
              onPress={() => {
                validation();
              }}
              loader={loader}
              done={done}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

export default UpdatePassword;
