/* eslint-disable max-len */
/* eslint-disable arrow-body-style */
/* eslint-disable eqeqeq */
/* eslint-disable global-require */
/* eslint-disable operator-linebreak */
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  Text,
  View,
  Image,
  TouchableOpacity,
  StatusBar,
  SafeAreaView,
  ScrollView,
  Modal,
  ActivityIndicator,
  BackHandler,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
} from 'react-native';
import ImagePicker from 'react-native-image-crop-picker';
import { launchImageLibrary } from 'react-native-image-picker';
import Toast from 'react-native-simple-toast';
import { useDispatch, useSelector } from 'react-redux';
import RNFetchBlob from 'rn-fetch-blob';
import Swiper from 'react-native-deck-swiper';
import { findIndex, isEmpty, isObject } from 'lodash';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
} from 'react-native-reanimated';
import { useFocusEffect, useTheme } from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import CountryPicker from 'react-native-country-picker-modal';
import FAIcon from 'react-native-vector-icons/FontAwesome';
import {
  openSettings,
  check,
  PERMISSIONS,
  requestMultiple,
  request,
} from 'react-native-permissions';
import { Header } from '@react-navigation/stack';
import CInput from '../../components/CInput';
import GradientBack from '../../components/gradientBack';
import styles from './styles';
import { CustomIcon } from '../../config/LoadIcons';
import CHeader from '../../components/CHeader';
import DropDown from '../../components/DropDown';
import { translate } from '../../lang/Translate';
import { getApiData } from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import {
  enableAnimateInEaseOut,
  sendErrorReport,
} from '../../utils/commonFunction';
import BluetoothAction from '../../redux/reducers/bluetooth/actions';
import AuthAction from '../../redux/reducers/auth/actions';
import BaseColors from '../../config/colors';
import ChildProfilePopup from '../../components/ChildProfilePopup';
import StepPopup from '../../components/StepPopup';
import { store } from '../../redux/store/configureStore';
/**
 *
 *@module ChildDetail
 *
 */
const ChildInfo = ({ navigation, route }) => {
  const childID = route?.params?.item?.id;
  // console.log('🚀 ~ ChildInfo ~ childID:', childID);
  const dispatch = useDispatch();
  const { characteristicID, serviceID, connectedDeviceDetails } =
    store?.getState()?.bluetooth;
  const headerHeight = Header.HEIGHT;
  const { languageData } = store?.getState()?.language;
  const { setDeviceSettingOpen, setStep7, setOnboardingDone } = AuthAction;
  const { userData, step7ChildInfo, closeOnboarding, accessToken } =
    store?.getState()?.auth;

  const colors = useTheme();
  const BaseColor = colors.colors;

  const imageAnim = useSharedValue(0);
  const infoAnim = useSharedValue(0);
  const opacityAnim = useSharedValue(0);

  const imageAnimationStyle = useAnimatedStyle(() => ({
    transform: [
      {
        scale: withTiming(imageAnim.value, {
          duration: 1000,
        }),
      },
    ],
  }));

  const infoStyleAnim = useAnimatedStyle(() => ({
    transform: [
      {
        translateY: withTiming(infoAnim.value, {
          duration: 1000,
        }),
      },
    ],
    opacity: withTiming(opacityAnim.value, {
      duration: 1000,
    }),
  }));

  useEffect(() => {
    imageAnim.value = 1;
    opacityAnim.value = 1;
    infoAnim.value = -300;
  }, []);

  const token = accessToken;

  const type = route?.params?.type;
  // console.log("ChildInfo -> type", type);

  const [isImage, setIsImage] = useState(false);

  const [state, setstate] = useState({
    isImage: '',
    nickName: '',
    height: '',
    weight: '',
    dob: '',
    id: null,
    conatactname: '',
    conatactnumber: '',
    secondConatactnumber: '',
    selectedCountry: userData?.country_code || 'US',
    secondSelectedCountry: userData?.country_code || 'US',
    countryCode: userData?.phone_code || '1',
    secondCountryCode: userData?.phone_code || '1',
    imageBase64: '',
  });

  const stateRef = useRef(null);
  stateRef.current = state;

  const [cardData, setCardData] = useState([
    {
      type: 'add',
    },
  ]);

  const [nickNameError, setNickNameError] = useState(false);
  const [dobError, setDobError] = useState(false);
  const [heightError, setHeightError] = useState(false);
  const [weightError, setWeightError] = useState(false);
  const [photoLoading, setphotoLoading] = useState(false);
  const [conatactnameError, setconatactnameError] = useState(false);
  const [conatactnumberError, setconatactnumberError] = useState(false);

  const [nickNameErrorTxt, setNickNameErrorTxt] = useState('');
  const [conatactnameErrorTxt, setconatactnameErrorTxt] = useState('');
  const [conatactnumberErrorTxt, setconatactnumberErrorTxt] = useState('');
  const [dobErrorTxt, setDobErrorTxt] = useState('');
  const [loader, setloader] = useState(false);
  const [currentIndex, setcurrentIndex] = useState(0);
  const [currentChild, setcurrentChild] = useState({});

  const nameRef = useRef();
  const contactnameRef = useRef();
  const contactnumberRef = useRef();
  const secondContactnumberRef = useRef();
  const [onbordingPopup, setonbordingPopup] = useState(false);
  const [showStep7, setShowStep7] = useState(false);
  const heightData = [];
  const weigthData = [];

  for (let i = 40; i <= 150; i++) {
    heightData.push({
      value: i,
    });
  }

  for (let i = 3; i <= 30; i++) {
    weigthData.push({
      value: i,
    });
  }

  const gender = [
    {
      value: 'MALE',
      id: 1,
      icon: 'avatar',
    },
    {
      value: 'FEMALE',
      id: 2,
      icon: 'woman',
    },
  ];

  const [selectedGender, setselectedGender] = useState({
    value: 'MALE',
    id: 1,
    icon: 'avatar',
  });

  useEffect(() => {
    if (
      type === 'connect' &&
      !step7ChildInfo &&
      !closeOnboarding &&
      isEmpty(connectedDeviceDetails)
    ) {
      sendErrorReport('step7', 'step7_child_info');
      setShowStep7(true); // m
    }
  }, []);
  useEffect(() => {
    if (type === 'Otp') {
      setonbordingPopup(true);
    } else if (
      type === 'connect' &&
      !step7ChildInfo &&
      !closeOnboarding &&
      isEmpty(connectedDeviceDetails)
    ) {
      sendErrorReport('step7', 'step7_child_info2');
      setShowStep7(true); // m
    }
  }, []);
  const Validation = () => {
    enableAnimateInEaseOut();
    const numVal = /^[0-9]+$/;
    if (isEmpty(state.nickName)) {
      allErrorFalse();
      setNickNameError(true);
      // setNickNameErrorTxt('Please enter Nickname');
      setNickNameErrorTxt(translate('enterNickname'));
    } else if (isEmpty(state.dob)) {
      allErrorFalse();
      setDobError(true);
      // setDobErrorTxt('Please Select DOB');
      setDobErrorTxt(translate('selectDOB'));
    } else if (isEmpty(state.height)) {
      allErrorFalse();
      // Toast.show('Please select Height', Toast.SHORT);
      Toast.show(translate('selectHeight'), Toast.SHORT);
    } else if (isEmpty(state.weight)) {
      allErrorFalse();
      Toast.show(translate('selectWeight'), Toast.SHORT);
      // Toast.show('Please select Weight', Toast.SHORT);
    } else if (isEmpty(state.conatactname)) {
      allErrorFalse();
      setconatactnameError(true);
      // setconatactnameErrorTxt('Please enter contact name');
      setconatactnameErrorTxt(translate('enterContactName'));
    } else if (isEmpty(state.conatactnumber)) {
      allErrorFalse();
      setconatactnumberError(true);
      // setconatactnumberErrorTxt('Please enter contact number');
      setconatactnumberErrorTxt(translate('enterContactNumber'));
    } else if (
      !numVal.test(String(state.conatactnumber)) ||
      state.conatactnumber.length < 6 ||
      state.conatactnumber.length > 15
    ) {
      allErrorFalse();
      setconatactnumberError(true);
      // setconatactnumberErrorTxt('Please enter valid contact number');
      setconatactnumberErrorTxt(translate('entervalidContactNumber'));
      // } else if (isEmpty(state.isImage)) {
      // allErrorFalse();
      // Toast.show(translate("addImage"), Toast.SHORT);
    } else {
      allErrorFalse();
      childProfile();
    }
  };

  const allErrorFalse = () => {
    setIsImage(false);
    setNickNameError(false);
    setHeightError(false);
    setWeightError(false);
    setDobError(false);
  };

  // this function for add use product
  /** this function for add use product
   * @function addUserProduct
   * @param {object} data child_id, device_id, product_id, service_id, characteristic_id, type, platform
   */
  async function addUserProduct(data) {
    const obj = {
      child_id: data?.id,
      device_id: route?.params?.device_id,
      product_id: route?.params?.product_id,
      device_data: route?.params?.device_data,
      device_ssid: route?.params?.device_ssid,
      service_id: serviceID,
      characteristic_id: characteristicID,
      type: 'type 1',
      platform: Platform.OS === 'ios' ? 'IOS' : 'ANDROID',
    };

    const headers = {
      'Content-Type': 'application/json',
      authorization: token ? `Bearer ${token}` : '',
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.addUserProduct,
        'POST',
        obj,
        headers,
      );

      if (response.success) {
        setstate({ id: null });
        console.log('kkkaaak11');
        dispatch(setOnboardingDone(true));
        navigation.navigate('DrawerNav');
      }
    } catch (error) {
      console.log('error ===', error);
      sendErrorReport(error, 'add_user_prod');
    }
  }
  const checkPermission = () => {
    if (Platform.OS === 'android' && Platform.Version >= 31) {
      sendErrorReport(Platform.Version, 'platform version4');
      check(PERMISSIONS.ANDROID.CAMERA)
        .then(res => {
          console.log('CAMERA--q-', res);
          if (res === 'granted') {
            // setBPermission(true);
          }
        })
        .catch(e => {
          console.log('bluetooth_', e);
        });
      request(PERMISSIONS.ANDROID.CAMERA)
        .then(result => {
          console.log('CAMERA----1', result);
        })
        .then(statuses => {
          console.log('CAMERA--2', statuses[PERMISSIONS.ANDROID.CAMERA]);
        });
    }
  };

  /** this function for add/update child Profile
   * @function childProfile
   * @param {object} data nick_name, date_of_birth, height, weight, gender, child_profile, emergency_name, emergency_phone, emergency_phone_code, country
   */
  const childProfile = () => {
    setloader(true);
    const data = {
      nick_name: state.nickName,
      date_of_birth: state.dob,
      height: state.height.value,
      weight: state.weight.value,
      gender: selectedGender.value.toLowerCase(),
      emergency_name: state.conatactname,
      emergency_phone: state.conatactnumber,
      emergency_phone_second: state.secondConatactnumber,
      emergency_phone_code: state?.countryCode?.includes('+')
        ? state.countryCode
        : `+${state.countryCode}`,
      emergency_phone_code_second: state?.secondCountryCode?.includes('+')
        ? state?.secondCountryCode
        : `+${state?.secondCountryCode}`,
      country: state.selectedCountry || 'US',
      second_country: state?.secondSelectedCountry || 'US',
      child_profile: state.imageBase64,
      platform: Platform.OS === 'ios' ? 'IOS' : 'ANDROID',
      lang_code: languageData?.languageData || 'es',
      brand_name: 'Babyauto',
      app_name: 'babyauto',
    };

    if (state?.id) {
      data.child_id = state?.id;
    }

    if (route?.params?.device_id) {
      data.device_id = route?.params?.device_id;
    }
    if (route?.params?.product_id) {
      data.product_id = route?.params?.product_id;
    }
    if (route?.params?.device_data) {
      data.device_data = route?.params?.device_data;
    }

    if (route?.params?.device_ssid) {
      data.device_ssid = route?.params?.device_ssid;
    }

    const headers = {
      'Content-Type': 'application/json',
      authorization: token ? `Bearer ${token}` : '',
    };

    let url = BaseSetting.endpoints.childProfile;
    if (state?.id) {
      url = BaseSetting.endpoints.updateChild;
    }
    getApiData(url, 'POST', data, headers)
      .then(async response => {
        if (response.success) {
          setstate({
            isImage: '',
            nickName: '',
            age: '',
            about: '',
            height: '',
            weight: '',
            imageBase64: '',
            conatactname: '',
            conatactnumber: '',
            secondConatactnumber: '',
            selectedCountry: userData?.country_code || 'US',
            secondSelectedCountry: userData?.country_code || 'US',
            countryCode: userData?.phone_code || '1',
            secondCountryCode: userData?.phone_code || '1',
          });
          if (isObject(response.data) && !isEmpty(response.data)) {
            dispatch(BluetoothAction.setActiveChildDetail(response.data));
          }

          console.log('response data child profile', response);

          if (
            route?.params?.product_id &&
            // state?.id === null &&
            type === 'connect'
          ) {
            if (isObject(response.data) && !isEmpty(response.data)) {
              await addUserProduct(response.data);
              // dispatch(BluetoothAction.setActiveDeviceId(response.data));
            }
          } else {
            if (type === 'Otp') {
              dispatch(setDeviceSettingOpen(true));
            }
            // navigation.navigate("DrawerNav");
            console.log('typeeee', type);
            checkPermission();
            setTimeout(() => {
              if (type === 'edit') {
                navigation.navigate('DrawerNav');
              } else if (cardData?.length >= 2) {
                navigation.navigate('DrawerNav');
              } else {
                navigation.navigate('QRScanner', {
                  fromChildInfo: true,
                  childObj: data,
                  child_id: response.data?.id,
                }); //changed as per clients requrements
              }
            }, 300);
          }
        } else {
          Toast.show(response.message);
        }
        setloader(false);
      })
      .catch(err => {
        console.log('ERRR', err);
        Toast.show('Something went wrong! Unable to save child profile');
        sendErrorReport(err, 'add_update_child_profile_1');
        setloader(false);
      });
  };

  const getChildInfo = async () => {
    console.log('getChildInfo funct called in CHILDINFO file.....');
    setphotoLoading(true);
    const headers = {
      'Content-Type': 'application/json',
      authorization: token ? `Bearer ${token}` : '',
    };

    setCardData([
      {
        type: 'add',
      },
    ]);

    try {
      const response = await getApiData(
        BaseSetting.endpoints.getUserChild,
        'POST',
        {
          platform: Platform.OS === 'ios' ? 'IOS' : 'ANDROID',
        },
        headers,
      );

      console.log(
        'response getChildInfo funct called in CHILDINFO file``````>>>>>>',
        response,
      );

      if (response.success) {
        const tempArr = [
          {
            type: 'add',
          },
        ];

        console.log('Ab me child profile wali screen me hu success hu....');
        const childArr = response.data;

        childArr.map(item => {
          tempArr.push(item);
        });

        setCardData(tempArr);
      } else {
        Toast.show(response.message);
      }
    } catch (err) {
      console.log('error user child', err);
      Toast.show('Something went wrong while getting child information');
      sendErrorReport(err, 'get_child_profile');
    } finally {
      setphotoLoading(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      const fetchData = async () => {
        setCardData([
          {
            type: 'add',
          },
        ]);
        await getChildInfo();
      };

      fetchData();
    }, []),
  );

  /** this function for get Child detail
   * @function getChildInfo
   * @param {object} data {}
   */
  // const getChildInfo = async () => {
  //   console.log('getChildInfo funct called in CHILDINFO file.....');
  //   setphotoLoading(true);
  //   const headers = {
  //     'Content-Type': 'application/json',
  //     authorization: token ? `Bearer ${token}` : '',
  //   };

  //   setCardData([
  //     {
  //       type: 'add',
  //     },
  //   ]);

  //   getApiData(
  //     BaseSetting.endpoints.getUserChild,
  //     'POST',
  //     {
  //       platform: Platform.OS === 'ios' ? 'IOS' : 'ANDROID',
  //     },
  //     headers,
  //   )
  //     .then(response => {
  //       console.log(
  //         'response getChildInfo funct called in CHILDINFO file``````>>>>>>',
  //         response,
  //       );
  //       if (response.success) {
  //         const tempArr = [
  //           {
  //             type: 'add',
  //           },
  //         ];
  //         console.log('Ab me child profile wali screen me hu success hu....');
  //         const childArr = response.data;

  //         childArr.map(item => {
  //           tempArr.push(item);
  //         });

  //         setCardData(tempArr);
  //       } else {
  //         Toast.show(response.message);
  //       }
  //       setphotoLoading(false);
  //     })
  //     .catch(err => {
  //       console.log('error user child', err);
  //       Toast.show('Something went wrong while getting child information');
  //       sendErrorReport(err, 'get_child_profile');
  //       setphotoLoading(false);
  //     });
  // };

  // console.log("CARD+===>> ", cardData);
  console.log(
    'cardData photoLoading----->>>>>',
    cardData,
    'photoLoading',
    photoLoading,
  );

  const image = async () => {
    console.log('image function running.....');
    setphotoLoading(true);
    const options = {
      mediaType: 'photo',
      includeBase64: true,
      maxHeight: 250,
      maxWidth: 250,
      quality: 0.8,
    };
    console.log('🚀 ~ image ~ options:', options);

    launchImageLibrary(options, response => {
      console.log('🚀 ~ image ~ response:', response);

      if (response.didCancel) {
        console.log('User cancelled image picker');
        setphotoLoading(false);
        return;
      }

      if (response.errorMessage) {
        console.log('ImagePicker Error: ', response.errorMessage);
        Toast.show('Error selecting image', Toast.SHORT);
        setphotoLoading(false);
        return;
      }

      if (response.assets && response.assets.length > 0) {
        const asset = response.assets[0];
        console.log('Selected asset:', asset);

        // Ensure the URI is properly formatted
        let imageUri = asset.uri;
        if (imageUri && !imageUri.startsWith('file://')) {
          imageUri = `file://${imageUri}`;
        }

        const newstate = {
          ...stateRef?.current,
          isImage: imageUri,
          imageBase64: asset.base64
            ? `data:image/png;base64,${asset.base64}`
            : '',
        };

        console.log('Setting new state with image:', imageUri);
        setstate(newstate);
        console.log('Image selected successfully');
        setphotoLoading(false);
      }
    });
  };

  function handleBackButtonClick() {
    if (type !== 'Otp') {
      navigation.navigate('DrawerNav');
    }
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  const arrayIndex = findIndex(cardData, lt => lt?.id === childID);

  useEffect(() => {
    setstate({
      ...state,
      isImage: cardData[arrayIndex]?.child_profile || '',
      nickName: cardData[arrayIndex]?.nick_name || '',
      dob: cardData[arrayIndex]?.date_of_birth || '',
      weight: cardData[arrayIndex]?.weight
        ? {
            value: cardData[arrayIndex]?.weight,
          }
        : '',
      height: cardData[arrayIndex]?.height
        ? {
            value: cardData[arrayIndex]?.height,
          }
        : '',
      id: cardData[arrayIndex]?.id || null,
      conatactname: cardData[arrayIndex]?.emergency_name || '',
      conatactnumber: cardData[arrayIndex]?.emergency_phone || '',
      secondConatactnumber: cardData[arrayIndex]?.emergency_phone_second || '',
      selectedCountry:
        cardData[arrayIndex]?.country || userData?.country_code || 'US',
      secondSelectedCountry:
        cardData[arrayIndex]?.second_country || userData?.country_code || 'US',
      imageBase64: cardData[arrayIndex]?.child_profile || '',
      countryCode:
        cardData[arrayIndex]?.emergency_phone_code ||
        userData?.phone_code ||
        '1',
      secondCountryCode:
        cardData[arrayIndex]?.emergency_phone_code_second ||
        userData?.phone_code ||
        '1',
    });
    if (cardData[arrayIndex]?.gender === 'female') {
      setselectedGender({
        value: 'FEMALE',
        id: 2,
        icon: 'woman',
      });
    } else {
      setselectedGender({
        value: 'MALE',
        id: 1,
        icon: 'avatar',
      });
    }
  }, [arrayIndex]);

  return (
    <View style={{ flex: 1, backgroundColor: BaseColor.whiteColor }}>
      {/* <GradientBack /> */}
      <CHeader
        title={
          route?.params?.type === 'edit'
            ? translate('editProfile')
            : translate('childProfileScreen')
        }
        tit
        rightIconName="check"
        // backBtn={type !== 'Otp'}
        leftIconName="left-arrow"
        onLeftPress={() => {
          navigation.navigate('DrawerNav');
        }}
        onRightPress={() => {
          Keyboard.dismiss();
          // console.log("type==>>>", type);
          if (state.id) {
            childProfile();
          } else {
            Validation();
          }
          // if (type === "connect") {
          //   Validation();
          //   // navigation.navigate("Dashboard");
          // } else {
          //   Validation();
          // }
        }}
      />
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : null}>
        <ScrollView
          contentContainerStyle={[
            {
              flexGrow: 1,
            },
          ]}
          bounces={false}
          showsVerticalScrollIndicator={false}>
          <SafeAreaView style={[styles.root]}>
            <StatusBar backgroundColor="transparent" barStyle="dark-content" />
            <Animated.View
              style={{ paddingBottom: 24, ...imageAnimationStyle }}>
              {route?.params?.type === 'edit' ? null : (
                <View
                  style={{
                    flex: 1,
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                    paddingBottom: 20,
                  }}>
                  <FAIcon
                    name="angle-left"
                    size={20}
                    color={BaseColor.blackColor}
                  />
                  <Text
                    style={[
                      styles.chooseProfile,
                      { color: BaseColor.blackColor, paddingHorizontal: 15 },
                    ]}>
                    {translate('chooseProfileText')}
                  </Text>
                  <FAIcon
                    name="angle-right"
                    size={20}
                    color={BaseColor.blackColor}
                  />
                </View>
              )}
              <View
                style={[
                  styles.imageLastShadow,
                  { backgroundColor: BaseColor.card2 },
                ]}>
                <View
                  style={[
                    styles.imagemiddleShadow,
                    { backgroundColor: BaseColor.card1 },
                  ]}>
                  <View style={{ marginBottom: 20 }}>
                    <View
                      activeOpacity={0.9}
                      // onPress={image}
                      style={[
                        styles.imageView,
                        { backgroundColor: BaseColor.whiteColor },
                      ]}>
                      {!photoLoading ? (
                        <Swiper
                          cards={cardData}
                          keyExtractor={data => data.id}
                          renderCard={item => {
                            return (
                              <View style={{ width: 250, height: 250 }}>
                                {item?.type ||
                                currentChild.type === 'add' ||
                                route?.params?.type === 'edit' ? (
                                  <TouchableOpacity
                                    activeOpacity={1}
                                    style={{
                                      justifyContent: 'center',
                                      alignItems: 'center',
                                      backgroundColor: BaseColor.whiteColor,
                                      flex: 1,
                                      borderRadius: 20,
                                      borderColor: BaseColor.textGrey,
                                      borderWidth: 1,
                                    }}
                                    onPress={image}>
                                    {state.isImage != '' ? (
                                      <View>
                                        <Image
                                          style={styles.selectedImage}
                                          source={{ uri: state.isImage }}
                                        />
                                      </View>
                                    ) : (
                                      <View>
                                        <CustomIcon
                                          name="image-2"
                                          size={36}
                                          color={BaseColor.blueDark}
                                          style={{ textAlign: 'center' }}
                                        />
                                        <Text
                                          style={[
                                            styles.imageText,
                                            { color: BaseColor.blueDark },
                                          ]}>
                                          {translate('addNew')}
                                        </Text>
                                        <Text
                                          style={[
                                            styles.imageText1,
                                            { color: BaseColor.textGrey },
                                          ]}>
                                          {translate('tapToAddText')}
                                        </Text>
                                      </View>
                                    )}
                                  </TouchableOpacity>
                                ) : (
                                  <View style={{ flex: 1 }}>
                                    <Image
                                      style={styles.selectedImage}
                                      source={
                                        item?.child_profile
                                          ? {
                                              uri: item?.child_profile,
                                            }
                                          : require('../../assets/images/childProPlaceholder.png')
                                      }
                                    />
                                  </View>
                                )}
                              </View>
                            );
                          }}
                          containerStyle={[
                            styles.containerStyle,
                            { backgroundColor: BaseColor.whiteColor },
                          ]}
                          disableRightSwipe={route?.params?.type === 'edit'}
                          onSwiped={cardIndex => {
                            // console.log("index", cardIndex);
                            if (cardIndex === cardData.length - 1) {
                              setcurrentIndex(0);
                              setcurrentChild(cardData[0]);
                              setstate({
                                ...state,
                                nickName: '',
                                dob: '',
                                weight: '',
                                height: '',
                                id: null,
                                conatactname: '',
                                conatactnumber: '',
                                secondConatactnumber: '',
                                selectedCountry: userData?.country_code || 'US',
                                secondSelectedCountry:
                                  userData?.country_code || 'US',
                                imageBase64: '',
                                countryCode: userData?.phone_code || '1',
                                secondCountryCode: userData?.phone_code || '1',
                              });
                              setselectedGender({
                                value: 'MALE',
                                id: 1,
                                icon: 'avatar',
                              });
                            } else {
                              setcurrentIndex(cardIndex + 1);
                              setcurrentChild(cardData[cardIndex + 1]);
                              setstate({
                                ...state,
                                isImage: '',
                                nickName: cardData[cardIndex + 1].nick_name,
                                dob: cardData[cardIndex + 1]?.date_of_birth,
                                weight: {
                                  value: cardData[cardIndex + 1]?.weight,
                                },
                                height: {
                                  value: cardData[cardIndex + 1]?.height,
                                },
                                id: cardData[cardIndex + 1]?.id || null,
                                conatactname:
                                  cardData[cardIndex + 1]?.emergency_name,
                                conatactnumber:
                                  cardData[cardIndex + 1]?.emergency_phone,
                                secondConatactnumber:
                                  cardIndex[cardIndex + 1]
                                    ?.emergency_phone_second,
                                selectedCountry:
                                  cardData[cardIndex + 1]?.country || 'US',
                                secondSelectedCountry:
                                  cardData[cardIndex + 1]?.second_country ||
                                  'US',
                                imageBase64:
                                  cardData[cardIndex + 1]?.child_profile,
                                countryCode:
                                  cardData[cardIndex + 1]?.emergency_phone_code,
                                secondCountryCode:
                                  cardIndex[cardIndex + 1]
                                    ?.emergency_phone_code_second,
                              });
                              if (cardData[cardIndex + 1]?.gender === 'male') {
                                setselectedGender({
                                  value: 'MALE',
                                  id: 1,
                                  icon: 'avatar',
                                });
                              } else {
                                setselectedGender({
                                  value: 'FEMALE',
                                  id: 2,
                                  icon: 'woman',
                                });
                              }
                              // console.log(
                              //   "asdasad???,",
                              //   cardData[cardIndex + 1]?.weight,
                              //   typeof cardData[cardIndex + 1]?.weight
                              // );
                            }
                          }}
                          disableLeftSwipe
                          verticalSwipe={false}
                          infinite
                          cardVerticalMargin={0}
                          cardHorizontalMargin={0}
                          onSwipedAll={() => {
                            console.log('onSwipedAll');
                          }}
                          cardIndex={0}
                          backgroundColor={BaseColor.whiteColor}
                          stackSize={route?.params?.type === 'edit' ? 1 : 3}
                          cardStyle={[
                            styles.cardStyle,
                            { backgroundColor: BaseColor.whiteColor },
                          ]}
                          swipeAnimationDuration={100}
                          animateCardOpacity
                          stackAnimationTension={10}
                          stackScale={20}
                          stackAnimationFriction={0}
                          stackSeparation={0}
                        />
                      ) : (
                        <ActivityIndicator
                          size="large"
                          color={BaseColor.blueDark}
                          style={{
                            flex: 1,
                            width: '100%',
                            justifyContent: 'center',
                            alignItems: 'center',
                          }}
                        />
                      )}
                    </View>
                  </View>
                </View>
              </View>
            </Animated.View>

            <Animated.View
              style={{
                flex: 1,
                justifyContent: 'flex-end',
              }}>
              <View
                style={[
                  styles.infoView,
                  { backgroundColor: BaseColor.infoView },
                  // infoStyleAnim,
                ]}>
                <View
                  style={[
                    styles.horizontalLine,
                    { backgroundColor: BaseColor.black30 },
                  ]}
                />
                <Text
                  style={[styles.infoText, { color: BaseColor.blackColor }]}>
                  {translate('infoTitle')}
                </Text>
                <View style={styles.textInputView}>
                  <View style={{ flex: 1 }}>
                    <CInput
                      ref={nameRef}
                      placeholder={translate('nickName')}
                      hideLeftIcon
                      placeholderTextColor={BaseColor.textGrey}
                      textInputWrapper={{
                        borderRadius: 12,
                        marginEnd: 4,
                        borderColor: BaseColor.whiteColor,
                      }}
                      value={state.nickName}
                      onChangeText={val => {
                        setstate({ ...state, nickName: val });
                      }}
                      onSubmitEditing={() => {
                        Keyboard.dismiss();
                      }}
                      showError={nickNameError}
                      errorMsg={nickNameErrorTxt}
                    />
                  </View>
                  <View
                    style={{
                      flex: 1,
                      paddingBottom: nickNameError ? 22 : 0,
                    }}>
                    <CInput
                      placeholder={translate('dob')}
                      hideLeftIcon
                      placeholderTextColor={BaseColor.textGrey}
                      textInputWrapper={{
                        borderRadius: 12,
                        marginStart: 4,
                        borderColor: BaseColor.whiteColor,
                      }}
                      value={state.dob}
                      datePicker
                      setstate={setstate}
                      state={state}
                      onDateChange={val => {
                        // console.log("DOB???", val, typeof val);
                        setstate({ ...state, dob: val });
                      }}
                      showError={dobError}
                      errorMsg={dobErrorTxt}
                    />
                  </View>
                </View>
                <View style={styles.textInputView}>
                  <DropDown
                    placeholder={translate('height')}
                    data={heightData}
                    style={{ borderRadius: 12, flex: 1, marginEnd: 4 }}
                    valueProp="value"
                    onSelect={val => {
                      // setHeight(val);
                      setstate({ ...state, height: val });
                    }}
                    selectedObject={state.height}
                    showError={heightError}
                  />
                  <DropDown
                    placeholder={translate('weight')}
                    data={weigthData}
                    style={{ borderRadius: 12, flex: 1, marginStart: 4 }}
                    valueProp="value"
                    onSelect={val => {
                      // setWeight(val);
                      setstate({ ...state, weight: val });
                    }}
                    selectedObject={state.weight}
                    showError={weightError}
                  />
                </View>
                <View
                  style={{
                    height: 120,
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}>
                  {gender.map(item => (
                    <View
                      style={{
                        flex: 1,
                        marginHorizontal: 8,
                      }}>
                      <TouchableOpacity
                        style={[styles.iconView]}
                        onPress={() => {
                          setselectedGender(item);
                        }}>
                        <View
                          style={{
                            ...styles.genderIcon,
                            backgroundColor:
                              item.id === selectedGender.id
                                ? BaseColor.transparentWhite
                                : BaseColor.whiteColor,
                            borderColor: BaseColor.whiteColor,
                          }}>
                          <CustomIcon
                            name={item.icon}
                            size={48}
                            color={
                              item.id === selectedGender.id
                                ? BaseColor.whiteColor
                                : BaseColor.infoView
                            }
                          />
                        </View>

                        {item.id === selectedGender.id ? (
                          <View
                            style={{
                              ...styles.selectedCheck,
                              backgroundColor: BaseColor.org,
                              borderColor: BaseColor.whiteColor,
                            }}>
                            <CustomIcon
                              name="check"
                              size={12}
                              color={BaseColor.whiteColor}
                            />
                          </View>
                        ) : null}

                        <Text
                          style={[
                            styles.genderName,
                            { color: BaseColor.textGrey },
                          ]}>
                          {item.value === 'MALE'
                            ? translate('genderMale')
                            : translate('genderFemale')}
                        </Text>
                      </TouchableOpacity>
                    </View>
                  ))}
                </View>
                <Text
                  style={[
                    styles.infoText,
                    { color: BaseColor.blackColor, paddingTop: 24 },
                  ]}>
                  {translate('emergencycontact')}
                </Text>
                <View style={{ ...styles.textInputView, marginTop: 5 }}>
                  <View style={{ flex: 1 }}>
                    <CInput
                      ref={contactnameRef}
                      placeholder={translate('contactname')}
                      hideLeftIcon
                      placeholderTextColor={BaseColor.textGrey}
                      textInputWrapper={{
                        borderRadius: 12,
                        marginEnd: 4,
                        borderColor: BaseColor.whiteColor,
                      }}
                      value={state.conatactname}
                      onChangeText={val => {
                        setstate({ ...state, conatactname: val });
                      }}
                      onSubmitEditing={() => {
                        contactnumberRef.current.focus();
                      }}
                      showError={conatactnameError}
                      errorMsg={conatactnameErrorTxt}
                    />
                  </View>
                </View>
                <View style={{ ...styles.textInputView, marginBottom: 8 }}>
                  <View
                    style={{
                      position: 'absolute',
                      flexDirection: 'row',
                      alignItems: 'center',
                      zIndex: 1,
                      top: 12,
                      left: 10,
                    }}>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        top: 10,
                      }}>
                      <CountryPicker
                        {...{
                          countryCode: state.selectedCountry || 'GB',
                          withFilter: true,
                          withFlag: true,
                          withAlphaFilter: true,
                          withCallingCode: true,
                          withEmoji: true,
                          disabled: true,
                          onSelect: val => {
                            setstate({
                              ...state,
                              selectedCountry: val.cca2,
                              countryCode: val.callingCode[0],
                            });
                          },
                          theme: {
                            fontSize: 16,
                            onBackgroundTextColor: BaseColor.whiteColor,
                            primaryColor: BaseColor.alertRed,
                            backgroundColor: BaseColor.blueDark,
                            filterPlaceholderTextColor: BaseColor.white80,
                          },
                          // onOpen: null,
                          // onClose: () => {
                          //   // onClose();
                          // },
                        }}
                        visible={state.countryCode}
                      />
                      <CustomIcon
                        name="expand-button"
                        size={14}
                        color={BaseColor.textGrey}
                      />
                    </View>
                  </View>
                  <View style={{ flex: 1 }}>
                    <CInput
                      ref={contactnumberRef}
                      placeholder={translate('contactnumber')}
                      hideLeftIcon
                      placeholderTextColor={BaseColor.textGrey}
                      keyboardType="number-pad"
                      textInputWrapper={{
                        borderRadius: 12,
                        marginEnd: 4,
                        paddingLeft: 50,
                        borderColor: BaseColor.whiteColor,
                      }}
                      value={state.conatactnumber}
                      onChangeText={val => {
                        setstate({ ...state, conatactnumber: val });
                      }}
                      onSubmitEditing={() => {
                        secondContactnumberRef.current.focus();
                      }}
                      showError={conatactnumberError}
                      errorMsg={conatactnumberErrorTxt}
                    />
                  </View>
                </View>
                <View
                  style={{
                    ...styles.textInputView,
                    marginBottom: 20,
                    // borderWidth: 2,
                  }}>
                  <View
                    style={{
                      position: 'absolute',
                      flexDirection: 'row',
                      alignItems: 'center',
                      zIndex: 1,
                      top: 12,
                      left: 10,
                    }}>
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        top: 10,
                      }}>
                      <CountryPicker
                        {...{
                          countryCode: state.secondSelectedCountry || 'GB',
                          withFilter: true,
                          withFlag: true,
                          withAlphaFilter: true,
                          withCallingCode: true,
                          withEmoji: true,
                          disabled: true,
                          onSelect: val => {
                            setstate({
                              ...state,
                              secondSelectedCountry: val.cca2,
                              secondCountryCode: val.callingCode[0],
                            });
                          },
                          theme: {
                            fontSize: 16,
                            onBackgroundTextColor: BaseColor.whiteColor,
                            primaryColor: BaseColor.alertRed,
                            backgroundColor: BaseColor.blueDark,
                            filterPlaceholderTextColor: BaseColor.white80,
                          },
                          // onOpen: null,
                          // onClose: () => {
                          //   // onClose();
                          // },
                        }}
                        visible={state.countryCode}
                      />
                      <CustomIcon
                        name="expand-button"
                        size={14}
                        color={BaseColor.textGrey}
                      />
                    </View>
                  </View>
                  <View style={{ width: '100%' }}>
                    <CInput
                      ref={secondContactnumberRef}
                      placeholder={translate('contactnumber')}
                      hideLeftIcon
                      placeholderTextColor={BaseColor.textGrey}
                      keyboardType="number-pad"
                      textInputWrapper={{
                        borderRadius: 12,
                        marginEnd: 4,
                        paddingLeft: 50,
                        borderColor: BaseColor.whiteColor,
                      }}
                      value={state.secondConatactnumber}
                      onChangeText={val => {
                        setstate({ ...state, secondConatactnumber: val });
                      }}
                      onSubmitEditing={() => {
                        Keyboard.dismiss();
                      }}
                      // showError={conatactnumberError}
                      // errorMsg={conatactnumberErrorTxt}
                    />
                  </View>
                  <View style={{ flex: 1 }} />
                </View>
              </View>
            </Animated.View>
          </SafeAreaView>
        </ScrollView>
      </KeyboardAvoidingView>

      <Modal
        visible={loader}
        transparent
        style={{
          flex: 1,
        }}>
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: BaseColor.black30,
          }}>
          <View
            style={{
              backgroundColor: BaseColor.whiteColor,
              justifyContent: 'center',
              alignItems: 'center',
              padding: 24,
              paddingHorizontal: 32,
              borderRadius: 24,
            }}>
            <ActivityIndicator size={24} color={BaseColor.blueDark} />
            <Text
              style={{
                color: BaseColor.blackColor,
                fontWeight: 'bold',
                marginTop: 8,
              }}>
              Loading
            </Text>
          </View>
        </View>
      </Modal>
      <ChildProfilePopup
        visible={onbordingPopup}
        image={require('../../assets/images/childPopup.png')}
        onClose={() => {
          setonbordingPopup(false);
        }}
      />
      <StepPopup
        visible={showStep7}
        descriptionText={translate('step7')}
        image={require('../../assets/images/step7.png')}
        onNext={() => {
          setShowStep7(false);
          dispatch(setStep7(true));
        }}
        step7
      />
    </View>
  );
};

export default ChildInfo;
