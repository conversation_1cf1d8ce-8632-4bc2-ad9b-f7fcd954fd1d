/* eslint-disable eqeqeq */
/* eslint-disable no-param-reassign */
/* eslint-disable no-unused-vars */
/* eslint-disable no-fallthrough */
/* eslint-disable max-len */
/* eslint-disable no-console */
/* eslint-disable quotes */
/**
 * Sample BLE React Native App
 *
 * @format
 * @flow strict-local
 */

import React, {useEffect} from 'react';
import {View, BackHandler, Text, TouchableOpacity} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import _BackgroundTimer from 'react-native-background-timer';
import styles from './styles';
import CHeader from '../../components/CHeader';
import {translate} from '../../lang/Translate';
import BluetoothAction from '../../redux/reducers/bluetooth/actions';
import BaseColor from '../../config/colors';

/**
 *
 *@module Troubleshoot
 *
 */
const Troubleshoot = ({navigation}) => {
  const dispatch = useDispatch();

  function handleBackButtonClick() {
    navigation.navigate(translate('home'));
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  return (
    <View style={styles.root}>
      {/* <GradientBack /> */}
      <CHeader
        title={translate('connectToYourBabyAuto')}
        // backBtn
        leftIconName="left-arrow"
        onLeftPress={() => {
          navigation.navigate(translate('home'));
          dispatch(BluetoothAction.setClickAddQr(false));
        }}
      />
      <View style={{margin: 20}}>
        <View style={{marginHorizontal: 20}}>
          <Text style={{fontWeight: 'bold', lineHeight: 20}}>
            {translate('belowIssues')}
          </Text>

          <Text style={{lineHeight: 30}}>
            <Text style={{fontWeight: 'bold'}}>1.</Text> {translate('issue1')}
          </Text>
          <Text style={{lineHeight: 30}}>
            <Text style={{fontWeight: 'bold'}}>2.</Text> {translate('issue2')}
          </Text>
          <Text style={{lineHeight: 30}}>
            <Text style={{fontWeight: 'bold'}}>3.</Text> {translate('issue3')}
          </Text>
          <Text style={{lineHeight: 30}}>
            <Text style={{fontWeight: 'bold'}}>4.</Text> {translate('issue4')}
          </Text>
        </View>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-evenly',
            margin: 20,
          }}>
          <TouchableOpacity
            style={{
              backgroundColor: BaseColor.blueDark,
              width: '45%',
              padding: 10,
              borderWidth: 0.5,
              borderRadius: 10,
              borderColor: BaseColor.blueDark,
              justifyContent: 'center',
            }}
            activeOpacity={0.7}
            onPress={() => {
              navigation.navigate('QRScanner');
            }}>
            <Text
              style={{
                color: BaseColor.whiteColor,
                textAlign: 'center',
              }}>
              {translate('tryagain')}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={{
              width: '45%',
              padding: 10,
              borderWidth: 0.5,
              borderRadius: 10,
              borderColor: BaseColor.blueDark,
            }}
            activeOpacity={0.7}
            onPress={() => {
              navigation.navigate('ChatScreen');
            }}>
            <Text
              style={{
                color: BaseColor.blueDark,
                textAlign: 'center',
              }}>
              {translate('contactus')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

export default Troubleshoot;
