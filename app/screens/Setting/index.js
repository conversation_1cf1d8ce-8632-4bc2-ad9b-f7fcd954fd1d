/* eslint-disable quotes */
import React, { useEffect, useState } from 'react';
import {
  Text,
  View,
  TouchableOpacity,
  FlatList,
  ScrollView,
  BackHandler,
  Linking,
  Modal,
  ActivityIndicator,
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import ToggleSwitch from 'toggle-switch-react-native';
import FAIcon from 'react-native-vector-icons/FontAwesome5';
import { findIndex, isEmpty, isObject } from 'lodash';
import { useDispatch, useSelector } from 'react-redux';
import { useTheme } from '@react-navigation/native';
import { EventRegister } from 'react-native-event-listeners';
import Toast from 'react-native-simple-toast';
import DeviceInfo from 'react-native-device-info';
import { SvgXml } from 'react-native-svg';
import styles from './styles';
import { CustomIcon } from '../../config/LoadIcons';
import CHeader from '../../components/CHeader';
import GradientBack from '../../components/gradientBack';
import { translate, initTranslate } from '../../lang/Translate';
import AuthAction from '../../redux/reducers/auth/actions';
import BluetoothAction from '../../redux/reducers/bluetooth/actions';
import BaseSetting from '../../config/setting';
import CAlert from '../../components/CAlert';
import { getApiData } from '../../utils/apiHelper';
import { sendErrorReport } from '../../utils/commonFunction';
import { FontFamily } from '../../config/typography';
import langArr from '../../assets/flagSvg/flags';
import languageActions from '../../redux/reducers/language/actions';
import { store } from '../../redux/store/configureStore';
import { SafeAreaView } from 'react-native-safe-area-context';

// import BaseColor, { DarkBaseColor } from '../../config/colors';

/**
 *@module Setting
 */
const Data = [
  {
    id: 1,
    icon: 'thermometer-three-quarters',
    title: translate('tempText'),
    temp: 'F',
  },
  {
    id: 2,
    Cicon: 'notifications-bell-button',
    title: translate('pushText'),
    mode: translate('modeOff'),
    modeOn: translate('modeOn'),
  },
  {
    id: 4,
    icon: 'comment-dots',
    title: translate('customerText'),
    icon1: 'chevron-right',
    badge: true,
  },
  {
    id: 5,
    Cicon: 'shopping_cart',
    title: translate('products'),
    icon1: 'chevron-right',
    size: 22,
  },
  {
    id: 6,
    Cicon: 'qrcode',
    title: translate('MyQRcode'),
    icon1: 'chevron-right',
  },
  {
    id: 7,
    Cicon: 'info1',
    title: translate('userManual'),
    icon1: 'chevron-right',
    size: 26,
  },
];

const Setting = ({ navigation }) => {
  const {
    setAccessToken,
    setUserData,
    setUserId,
    setUUid,
    setBaseColor,
    setIsFarenheit,
    // setDarkmode,
  } = AuthAction;

  const { setLastDeviceId, setIsConvert } = BluetoothAction;

  const dispatch = useDispatch();

  const colors = useTheme();
  const BaseColor = colors.colors;

  const {
    uuid,
    accessToken,
    darkmode,
    userData,
    isFarenheit,
    notificationCount,
  } = useSelector(state => state.auth);
  const { isConvert } = useSelector(state => state.bluetooth);
  const languageData = useSelector(state => state.language.languageData);
  const { setLanguage } = languageActions;

  const socketData = useSelector(state => state.socket.socketData);
  // const logoAnimT = useSharedValue(0);
  // const logoAnimO = useSharedValue(0);
  // const textAnimT = useSharedValue(0);
  // const btnAnimT = useSharedValue(0);
  // const langAnimT = useSharedValue(0);

  // const [isPush, setIsPush] = useState(userData?.push_notification_sent);
  const isPush = userData?.push_notification_sent;
  // const [isDarkMode, setIsDarkMode] = useState(darkmode);
  const flagsArr = langArr;
  const selectedLang = flagsArr.filter(item => item.code === languageData);
  const [selectedLanguage, setselectedLanguage] = useState(selectedLang[0]);
  const [langModal, setlangModal] = useState(false);
  const [refresh, setRefresh] = useState(false);

  const [btnLoader, setBtnLoader] = useState(false);
  const [AlerModal, setAlerModal] = useState({
    visible: false,
    title: '',
    message: '',
  });
  const changeLanguage = (code, name) => {
    dispatch(setLanguage(code, name));
    setTimeout(() => {
      initTranslate(store, true);
    }, 100);

    setTimeout(() => {
      setRefresh(false);
      // logoAnimT.value = -200;
      // textAnimT.value = -100;
      // btnAnimT.value = -50;
      // logoAnimO.value = 1;
      // langAnimT.value = -50;
    }, 500);
    // dispatch(setLanguage(code, name));
  };
  const [AlerModal1, setAlerModal1] = useState({
    visible: false,
    title: '',
    message: '',
  });
  // this function for update notification status
  /** this function for update notification status
   * @function updateNotificationStatus
   * @param {object} data {}
   */
  async function updateNotificationStatus() {
    const headers = {
      'Content-Type': 'application/json',
      authorization: accessToken ? `Bearer ${accessToken}` : '',
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.notificationStatus,
        'POST',
        {},
        headers,
      );

      if (response.success) {
        // setIsPush(!isPush);
        if (isObject(response.data) && !isEmpty(response.data)) {
          dispatch(setUserData(response.data));
        }
      } else {
        Toast.show(response.message);
      }
    } catch (error) {
      console.log('notification error ===', error);
      sendErrorReport(error, 'update_noti_status');
    }
  }

  const check = async item1 => {
    const data = [...Data];
    const obj = findIndex(data, item => item.id === item1.id);
    console.log('check -> obj', obj);
    // const darkN = !darkmode;
    switch (obj) {
      case 0:
        dispatch(setIsFarenheit(!isFarenheit));
        dispatch(setIsConvert(!isConvert));
        break;
      case 1:
        updateNotificationStatus();
        break;
      // case 2:
      // setIsDarkMode(darkN);
      // EventRegister.emit("changeAppTheme", darkN);
      // dispatch(setDarkmode(darkN));
      // setTimeout(() => {
      //   ReactNativeRestart.Restart();
      // }, 100);
      // break;
      case 2:
        navigation.navigate('ChatScreen');
        break;
      case 3:
        navigation.navigate('ProductCatalouge');
        break;
      case 4:
        navigation.navigate('MyQRcode');
        break;
      case 5:
        navigation.navigate('UserManual');
        break;
      default:
        break;
    }
  };

  const render = ({ item, index }) => (
    <View
      style={[
        styles.settingContent,
        { borderBottomWidth: index === Data.length - 1 ? 0 : 0.5 },
      ]}>
      <View
        style={[styles.settingIcon, { backgroundColor: BaseColor.whiteColor }]}>
        {item.Cicon ? (
          <CustomIcon
            name={item.Cicon}
            size={item?.size ? item?.size : 20}
            color={BaseColor.org}
          />
        ) : (
          <FAIcon name={item.icon} size={20} color={BaseColor.org} />
        )}
      </View>

      <TouchableOpacity
        activeOpacity={0.7}
        onPress={() => {
          check(item);
        }}
        style={styles.settingName}>
        <View style={{ flexDirection: 'column' }}>
          <Text style={[styles.infoText, { color: BaseColor.blackColor }]}>
            {item.id === 1 && translate('tempText')}
            {item.id === 2 && translate('pushText')}
            {item.id === 4 && translate('customerText')}
            {item.id === 5 && translate('products')}
            {item.id === 6 && translate('MyQRcode')}
            {item.id === 7 && translate('userManual')}
          </Text>
          {item.mode ? (
            <Text
              style={{
                ...styles.infoText,
                fontSize: 14,
                color: item.id === 2 && isPush ? BaseColor.blueDark : 'red',
              }}>
              {item.id === 2 && isPush
                ? translate('modeOn')
                : translate('modeOff')}
            </Text>
          ) : null}
        </View>

        {item.temp ? (
          <View
            style={[
              styles.tempC,
              { backgroundColor: isFarenheit ? '#dd2c00' : '#cddc39' },
            ]}>
            <Text style={[styles.tempTxt, { color: BaseColor.whiteColor }]}>
              {!isFarenheit ? translate('tempC') : translate('tempF')}
            </Text>
          </View>
        ) : null}

        {item.mode ? (
          <ToggleSwitch
            size="medium"
            isOn={
              (item.id === 2 && isPush) || (item.id === 3 && darkmode) || false
            }
            onToggle={val => {
              if (item.id === 2) {
                updateNotificationStatus();
              } else {
                // setIsDarkMode(val);
                // EventRegister.emit("changeAppTheme", val);
                // dispatch(setDarkmode(val));
              }
              console.log(val);
            }}
            onColor={BaseColor.whiteColor}
            thumbOnStyle={{ backgroundColor: BaseColor.blueDark }}
            thumbOffStyle={{ backgroundColor: BaseColor.whiteColor }}
            trackOnStyle={{
              borderWidth: 1,
              borderColor: BaseColor.blueLight,
            }}
            trackOffStyle={{ borderWidth: 1, borderColor: '#ecf0f1' }}
          />
        ) : null}

        {item.badge && notificationCount.notification_count > 0 ? (
          <View
            style={{
              backgroundColor: BaseColor.blueLight,
              width: 30,
              height: 30,
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: 30,
            }}>
            <Text style={{ color: '#fff' }}>
              {notificationCount.notification_count}
            </Text>
          </View>
        ) : null}

        {item.icon1 ? (
          <FAIcon
            name="chevron-right"
            size={20}
            color={BaseColor.textGrey}
            style={{ marginRight: 0 }}
          />
        ) : null}
      </TouchableOpacity>
    </View>
  );

  function handleBackButtonClick() {
    navigation.closeDrawer();
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  /** this function for logout
   * @function removeToken
   * @param {object} data token, socket_id, user_id
   */
  async function removeToken(token) {
    setBtnLoader(true);
    const data = {
      token,
      socket_id: socketData?.socket_id,
      user_id: userData.id,
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.deleteToken,
        'POST',
        data,
        {
          'Content-Type': 'application/json',
          authorization: accessToken ? `Bearer ${accessToken}` : '',
        },
      );
      setBtnLoader(false);
      dispatch(setAccessToken(''));
      dispatch(setUUid(''));
      navigation.navigate('RedirectLS');
      navigation.closeDrawer();
      console.log('fcm token removed ==>', response);
    } catch (err) {
      console.log('ERRR==', err);
    }
  }

  async function deleteAccount() {
    setBtnLoader(true);
    try {
      const response = await getApiData(
        BaseSetting.endpoints.deleteAccount,
        'POST',
        {},
        {
          'Content-Type': 'application/json',
          authorization: accessToken ? `Bearer ${accessToken}` : '',
        },
      );
      if (response.success) {
        console.log('account removed ==>', response);
        navigation.navigate('RedirectLS');
        navigation.closeDrawer();
        setBtnLoader(false);
        dispatch(setAccessToken(''));
        dispatch(setUUid(''));
        Toast.show(response?.message);
        setAlerModal1({
          ...AlerModal1,
          visible: false,
        });
      }
    } catch (err) {
      setBtnLoader(false);
      console.log('ERRR==', err);
    }
  }
  //  notification_count //eralier it was chat_count);
  return (
    <View style={{ flex: 1 }}>
      <CHeader
        title={translate('settingScreen')}
        backBtn
        leftIconName
        onLeftPress={() => {
          navigation.closeDrawer();
        }}
      />
      <ScrollView contentContainerStyle={{ flexGrow: 1 }} bounces={false}>
        <View style={styles.root}>
          {/* <GradientBack /> */}
          <View
            style={[
              styles.flatListView,
              { backgroundColor: BaseColor.whiteColor },
            ]}>
            <FlatList
              data={Data}
              renderItem={render}
              keyExtractor={item => item.id}
              bounces={false}
            />
          </View>
          <View style={{ paddingHorizontal: 16 }}>
            <TouchableOpacity
              activeOpacity={0.7}
              onPress={() => {
                Linking.openURL('https://www.babyauto.com/es');
              }}>
              <Text style={[styles.aboutText, { color: BaseColor.textGrey }]}>
                {translate('about')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              activeOpacity={0.7}
              onPress={() => {
                navigation.navigate('FAQScreen');
              }}>
              <Text style={[styles.aboutText, { color: BaseColor.textGrey }]}>
                {translate('support')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              activeOpacity={0.7}
              onPress={() => {
                Linking.openURL('https://www.babyautogroup.com/es/privacidad');
              }}>
              <Text style={[styles.aboutText, { color: BaseColor.textGrey }]}>
                {translate('privacypolicy')}
              </Text>
            </TouchableOpacity>
            {/* <TouchableOpacity
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                }}
                activeOpacity={0.7}
                onPress={() => {
                  setAlerModal({
                    visible: true,
                    title: "LOGOUT",
                    message: "Are you sure you want to logout?",
                  });
                  const { setLeadId } = AuthAction;
                  dispatch(setLeadId(null));
                }}
              >
                <Text style={[styles.aboutText, { color: BaseColor.textGrey }]}>
                  {translate("logout")}
                </Text>
                <Text
                  style={{
                    ...styles.aboutText,
                    fontSize: 10,
                    color: BaseColor.textGrey,
                    top: 5,
                  }}
                >
                  Version
                  {' '}
                  {DeviceInfo.getVersion()}
                </Text>
              </TouchableOpacity> */}

            <TouchableOpacity
              activeOpacity={0.7}
              onPress={() => {
                setAlerModal1({
                  visible: true,
                  title: 'Delete Account',
                  message: 'Are you sure you want to delete your account?',
                });
                const { setLeadId } = AuthAction;
                dispatch(setLeadId(null));
              }}>
              <Text style={[styles.aboutText, { color: BaseColor.textGrey }]}>
                {translate('deleteAccount')}
              </Text>
            </TouchableOpacity>
            <View style={[{ borderBottomWidth: 0.5, marginVertical: 5 }]} />

            <TouchableOpacity
              style={styles.langBtn}
              activeOpacity={0.7}
              onPress={() => {
                setlangModal(true);
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  paddingTop: 10,
                  alignItems: 'center',
                }}>
                <View
                  style={{
                    borderRadius: 15,
                    backgroundColor: BaseColor.blackColor,
                    overflow: 'hidden',
                    height: 30,
                    width: 30,
                    justifyContent: 'center',
                    alignContent: 'center',
                    alignItems: 'center',
                  }}>
                  <SvgXml xml={selectedLanguage.svg} width={45} height={45} />
                </View>

                <Text
                  style={{
                    ...styles.aboutText,
                    color: BaseColor.blackColor,
                    marginLeft: 10,
                    paddingTop: 5,
                    fontFamily: FontFamily.default,
                  }}>
                  {selectedLanguage.title}
                </Text>
                {/* <CustomIcon name="expand-button" color={BaseColor.whiteColor} /> */}
              </View>
              {/* <CustomIcon name="expand-button" color={BaseColor.whiteColor} /> */}
            </TouchableOpacity>
            <TouchableOpacity
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                marginVertical: 10,
                marginBottom: 40,
              }}
              activeOpacity={0.7}
              onPress={() => {
                setAlerModal({
                  visible: true,
                  title: 'LOGOUT',
                  message: 'Are you sure you want to logout?',
                });
                const { setLeadId } = AuthAction;
                dispatch(setLeadId(null));
              }}>
              <Text style={[styles.aboutText, { color: BaseColor.textGrey }]}>
                {translate('logout')}
              </Text>
              <Text
                style={{
                  ...styles.aboutText,
                  fontSize: 10,
                  color: BaseColor.textGrey,
                  top: 5,
                }}>
                Version {DeviceInfo.getVersion()}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
      <CAlert
        visible={AlerModal.visible}
        onRequestClose={() =>
          setAlerModal({
            ...AlerModal,
            visible: false,
          })
        }
        onCancelPress={() =>
          setAlerModal({
            ...AlerModal,
            visible: false,
          })
        }
        loader={btnLoader}
        onOkPress={() => {
          removeToken(uuid);
          // navigation.navigate("RedirectLS");
        }}
        alertTitle={AlerModal.title}
        alertMessage={AlerModal.message}
        agreeTxt={translate('agree')}
      />
      <CAlert
        visible={AlerModal1.visible}
        onRequestClose={() =>
          setAlerModal1({
            ...AlerModal1,
            visible: false,
          })
        }
        onCancelPress={() =>
          setAlerModal1({
            ...AlerModal1,
            visible: false,
          })
        }
        loader={btnLoader}
        onOkPress={async () => {
          await deleteAccount();
          dispatch(setUserData({}));
          dispatch(setUserId(''));
          dispatch(setLastDeviceId(''));
        }}
        alertTitle={AlerModal1.title}
        alertMessage={AlerModal1.message}
        agreeTxt={translate('delete')}
      />
      <Modal
        transparent
        style={{ flex: 1 }}
        visible={langModal}
        animationType="fade">
        <TouchableOpacity
          style={{ flex: 1 }}
          onPress={() => {
            setlangModal(false);
          }}>
          <View style={styles.modalCont}>
            {flagsArr.map((item, index) => (
              <TouchableOpacity
                key={`${index}`}
                style={styles.langBtn}
                activeOpacity={0.7}
                onPress={() => {
                  setlangModal(false);
                  // logoAnimT.value = 0;
                  // textAnimT.value = 0;
                  // btnAnimT.value = 0;
                  // logoAnimO.value = 0;
                  // langAnimT.value = 0;
                  setTimeout(() => {
                    setRefresh(true);
                  }, 500);
                  setTimeout(() => {
                    changeLanguage(item.code, item.title);
                  }, 2000);
                  setselectedLanguage(item);
                }}>
                <View style={styles.flagDesign}>
                  <SvgXml xml={item.svg} width={20} height={20} />
                  <Text
                    style={{
                      color: BaseColor.textGrey,
                      marginHorizontal: 8,
                      fontFamily: FontFamily.default,
                    }}>
                    {item.title}
                  </Text>
                </View>
                {/* <CustomIcon
                    name="expand-button"
                    color={BaseColor.blackColor}
                  /> */}
              </TouchableOpacity>
            ))}
          </View>
        </TouchableOpacity>
      </Modal>
      <Modal
        transparent
        style={{
          flex: 1,
        }}
        visible={refresh}
        animationType="fade">
        <TouchableOpacity
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: BaseColor.white50,
          }}
          onPress={() => setRefresh(false)}>
          <View
            style={{
              padding: 12,
              backgroundColor: BaseColor.whiteColor,
              borderRadius: 12,
              borderWidth: 0.5,
              borderColor: BaseColor.textGrey,
            }}>
            <Text
              style={{
                fontFamily: FontFamily.default,
                fontWeight: 'bold',
                marginBottom: 12,
              }}>
              Changing language
            </Text>
            <ActivityIndicator size="small" color={BaseColor.blueDark} />
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

export default Setting;
