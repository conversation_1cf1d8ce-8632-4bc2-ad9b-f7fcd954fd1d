/* eslint-disable quotes */
import { Dimensions, StyleSheet } from 'react-native';
import BaseColor from '../../config/colors';
import { FontFamily } from '../../config/typography';

const { height: dHeight, width: dWidth } = Dimensions.get('window');

const styles = StyleSheet.create({
  root: {
    flex: 1,
  },
  backImg: {
    flex: 1,
    height: dHeight,
    width: dWidth,
  },
  mainContainer: {
    flex: 1,
    height: dHeight,
    width: dWidth,
    padding: 16,
  },
  container: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  txtStyle: {
    marginTop: 60,
    color: BaseColor.whiteColor,
    fontSize: 32,
    // fontWeight: "100",
    fontFamily: FontFamily.default,
    marginEnd: 64,
  },
  row: {
    flexDirection: 'row',
    width: '100%',
  },
  loginBtn: {
    flex: 1,
    marginEnd: 8,
    borderWidth: 1,
    borderColor: BaseColor.whiteColor,
    backgroundColor: 'transparent',
  },
  signupBtn: {
    flex: 1,
    marginStart: 8,
    borderWidth: 1,
    backgroundColor: BaseColor.blueDark,
  },
  loginTxt: {
    color: BaseColor.whiteColor,
  },
  langBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'center',
    marginTop: 8,
  },
  modalCont: {
    backgroundColor: BaseColor.whiteColor,
    position: 'absolute',
    // bottom: 24,
    top: 30,
    alignSelf: 'flex-start',
    borderRadius: 16,
    padding: 12,
    margin: 25,
  },
  flagDesign: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    minWidth: 140,
  },
});

export default styles;
