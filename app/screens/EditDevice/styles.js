import { StyleSheet } from 'react-native';
import BaseColor from '../../config/colors';
import { FontFamily } from '../../config/typography';

const styles = StyleSheet.create({
  root: {
    flex: 1,
  },
  chooseProfile: {
    paddingBottom: 20,
    fontSize: 12,
    color: BaseColor.whiteColor,
    fontFamily: FontFamily.default,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  imageLastShadow: {
    marginTop: 10,
    backgroundColor: BaseColor.white20,
    justifyContent: 'center',
    width: 172,
    height: 268,
    alignSelf: 'center',
    borderRadius: 20,
  },
  imagemiddleShadow: {
    marginBottom: 20,
    backgroundColor: BaseColor.transparentWhite,
    justifyContent: 'center',
    width: 212,
    height: 260,
    alignSelf: 'center',
    borderRadius: 20,
  },
  imageView: {
    backgroundColor: BaseColor.whiteColor,
    width: 250,
    height: 250,
    alignSelf: 'center',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
  },
  selectedImage: {
    width: 250,
    height: 250,
    borderRadius: 20,
  },
  infoView: {
    backgroundColor: BaseColor.blueDark,
    borderTopLeftRadius: 22,
    borderTopRightRadius: 22,
    paddingHorizontal: 20,
  },
  horizontalLine: {
    width: 40,
    height: 4,
    backgroundColor: BaseColor.black30,
    position: 'absolute',
    alignSelf: 'center',
    top: 18,
    borderRadius: 3,
  },
  infoText: {
    color: BaseColor.whiteColor,
    fontSize: 18,
    fontWeight: 'bold',
    paddingTop: 38,
    marginBottom: 18,
  },
  textInputView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
});

export default styles;
