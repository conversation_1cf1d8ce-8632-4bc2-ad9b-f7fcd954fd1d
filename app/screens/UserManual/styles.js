/* eslint-disable quotes */
import { StyleSheet } from 'react-native';
import BaseColor from '../../config/colors';
import { FontFamily } from '../../config/typography';

const styles = StyleSheet.create({
  root: {
    flex: 1,
  },
  cardRoot: {
    backgroundColor: BaseColor.lightgray,
    marginVertical: 6,
    marginHorizontal: 25,
    // margin: 17,
    borderRadius: 8,
    padding: 12,
    // paddingHorizontal: 16,
    // borderWidth: 0.5,
    // borderColor: BaseColor.textGrey
  },
  imgStyle: {
    height: 200,
    width: 200,
    borderRadius: 25,
    borderWidth: 1,
    borderColor: BaseColor.black60,
    justifyContent: 'center',
    alignSelf: 'center',
  },
  childImgStyle: {
    height: 50,
    width: 50,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: BaseColor.black60,
  },
  childNameStyle: {
    fontFamily: FontFamily.default,
    fontWeight: '700',
    fontSize: 14,
    paddingLeft: 10,
    width: '100%',
    color: BaseColor.blackColor,
    textAlign: 'center',
    alignSelf: 'center',
    paddingVertical: 10,
    maxHeight: 40,
  },
  divider: {
    height: 1,
    backgroundColor: BaseColor.black40,
    marginVertical: 12,
  },
  rowStyle: {
    // flexDirection: "row",
  },
  nameStyle: {
    // fontFamily: FontFamily.default,
    // fontWeight: "bold",
    // fontSize: 16,
    color: BaseColor.blackColor,
    fontFamily: FontFamily.default,
    fontSize: 12,
  },
  catStyle: {
    color: BaseColor.black70,
    fontFamily: FontFamily.default,
    fontSize: 14,
  },
  subTitle: {
    fontFamily: FontFamily.default,
    fontSize: 14,
    fontWeight: 'bold',
  },
  valueStyle: {
    fontFamily: FontFamily.default,
    fontSize: 14,
  },
  emptyComponent: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loaderContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default styles;
