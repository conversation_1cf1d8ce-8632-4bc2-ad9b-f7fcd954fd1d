import { Dimensions, StyleSheet } from 'react-native';
import BaseColor from '../../config/colors';
import { FontFamily } from '../../config/typography';

const { height: dHeight, width: dWidth } = Dimensions.get('window');

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: BaseColor.whiteColor
  },
  mainContainer: {
    flex: 1,
    padding: 16,
  },
  closeBtn: {
    height: 40,
    width: 40,
    borderRadius: 20,
    alignSelf: 'flex-end',
    position: 'absolute',
    borderColor: BaseColor.textGrey,
    borderWidth: 0.5,
    right: 12,
    top: 34,
  },
  mainInputStyle: {
    // flex: 1,
    justifyContent: 'center',
  },
  inputWrapper: {
    marginVertical: 7,
  },
  loginText: {
    fontSize: 28,
    color: BaseColor.whiteColor,
    // fontWeight: '700',
    fontFamily: FontFamily.default,
    alignSelf: 'center',
    paddingVertical: 12,
    marginTop: 24,
    marginBottom: 12,
  },
  loginTextView: {
    marginTop: 40,
    marginBottom: 20,
    alignItems: 'center',
    fontFamily: FontFamily.default,
  },
  rememberText: {
    fontSize: 14,
    color: BaseColor.whiteColor,
    fontWeight: '600',
    textAlign: 'left',
    marginStart: 8,
  },
  logoImg: {
    height: 60,
    width: 60,
    alignSelf: 'center',
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 7,
    // },
    // shadowOpacity: 0.41,
    // shadowRadius: 9.11,
    // elevation: 14,
  },
  loginBtn: {
    marginTop: 15,
    alignSelf: 'center',
    borderColor: BaseColor.blackColor,
    borderWidth: 1,

  },
});

export default styles;
