/* eslint-disable eqeqeq */
/* eslint-disable quotes */
import React, { useEffect, useRef, useState } from 'react';
import {
  Text,
  View,
  Image,
  TouchableOpacity,
  BackHandler,
  KeyboardAvoidingView,
  ScrollView,
  Platform,
} from 'react-native';
import Toast from 'react-native-simple-toast';
// import Bugsnag from '@bugsnag/react-native';
import DeviceInfo from 'react-native-device-info';
import { useTheme } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import CButton from '../../components/CButton';
import CInput from '../../components/CInput';
import GradientBack from '../../components/gradientBack';
import styles from './styles';
import { FontFamily } from '../../config/typography';
import { translate } from '../../lang/Translate';
import { getApiData } from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import BaseColors from '../../config/colors';

import {
  enableAnimateInEaseOut,
  sendErrorReport,
} from '../../utils/commonFunction';
import AuthAction from '../../redux/reducers/auth/actions';
import { SafeAreaView } from 'react-native-safe-area-context';

/**
 *
 *@module Login
 *
 */
const Login = ({ navigation }) => {
  const colors = useTheme();
  const BaseColor = colors.colors;

  const dispatch = useDispatch();
  const { setUserData, setAccessToken, setDeviceSettingOpen, setIsFarenheit } =
    AuthAction;
  const brandToken = useSelector(state => state.auth.brandToken);
  const [state, setstate] = useState({
    email: '',
    password: '',
    rememberMe: false,
  });
  const [loader, setloader] = useState(false);
  const [done, setdone] = useState(false);
  const [anim, setanim] = useState(false);
  const [backAnim, setBackAnim] = useState(false);

  const [emailError, setEmailError] = useState(false);
  const [passwordError, setPasswordError] = useState(false);

  const [emailErrorTxt, setEmailErrorTxt] = useState('');
  const [passwordErrorTxt, setPasswordErrorTxt] = useState('');
  const [hideShow, setHideShow] = useState(true);
  const userRef = useRef();
  const passwordRef = useRef();

  function handleBackButtonClick() {
    navigation.navigate('RedirectLS');
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  const Validation = () => {
    // const passVal = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;

    enableAnimateInEaseOut();

    if (state.email == '') {
      allErrorFalse();
      setEmailError(true);
      setEmailErrorTxt('Please enter User Name');
    } else if (state.password == '') {
      allErrorFalse();
      setPasswordError(true);
      setPasswordErrorTxt('Please enter Password');
    }
    // else if (!passVal.test(String(state.password))) {
    //   allErrorFalse();
    //   setPasswordError(true);
    //   setPasswordErrorTxt('Please enter valid Password');
    // }
    else {
      allErrorFalse();
      userLogin();
    }
  };

  const allErrorFalse = () => {
    setEmailError(false);
    setPasswordError(false);
  };

  /** this function for user login
   * @function userLogin
   * @param {object} data username, password, token
   */
  const userLogin = () => {
    setloader(true);
    setanim(true);
    setBackAnim(false);
    const data = {
      username: state.email,
      password: state.password,
      token: brandToken,
    };
    console.log(BaseSetting.endpoints.login);
    // Toast.show("End Point", BaseSetting.endpoints.login);
    // alert(BaseSetting.endpoints.login);
    getApiData(BaseSetting.endpoints.login, 'POST', data)
      .then(response => {
        // alert(response);
        // Toast.show(response);
        // Toast.show("response", response);
        console.log('.then -> response', response);
        if (response.success) {
          dispatch(setIsFarenheit(false));
          dispatch(setUserData(response?.data?.user));
          dispatch(setAccessToken(response?.data?.token));
          // dispatch(setUserId(response?.data?.user?.id));
          // setTimeout(() => {
          //   setTimeout(() => {
          //     setloader(false);
          //     setdone(true);
          //   }, 2000);
          //   navigation.navigate("DrawerNav");
          // }, 3000);

          if (response?.data?.user?.status === 'inactive') {
            sendOTP(response?.data?.user?.id);
          } else {
            setTimeout(() => {
              setTimeout(() => {
                setloader(false);
                setdone(true);
                dispatch(setDeviceSettingOpen(true));
              }, 2000);
              navigation.navigate('DrawerNav');
            }, 3000);
          }
        } else {
          Toast.show(response.message || 'Something went wrong');
          // BUGSNAG TEST
          // Bugsnag.notify(response.message, report => {
          //   report.metadata = {
          //     data: {
          //       error: response.message,
          //     },
          //   };
          // });
          setanim(false);
          setBackAnim(true);
          setdone(false);
          setloader(false);
        }
      })
      .catch(err => {
        // alert(err);
        Toast.show('Something went wrong while trying to login');
        sendErrorReport(err, 'login_api');
        setanim(false);
        setBackAnim(true);
        setdone(false);
        setloader(false);
        console.log('ERRR', err);
      });
  };

  /** this function for send otp
   * @function sendOTP
   * @param {object} data user_id
   */
  const sendOTP = userID => {
    console.log('sendOTP -> userID', userID);
    setloader(true);

    const data = {
      user_id: userID,
    };

    getApiData(BaseSetting.endpoints.sendOtp, 'POST', data)
      .then(response => {
        if (response.success) {
          setTimeout(() => {
            setloader(false);
            setdone(true);
          }, 2000);
          setTimeout(() => {
            navigation.navigate('Otp', {
              type: 'LoginInactive',
              userId: userID,
            });
          }, 3000);
        } else {
          Toast.show(response.message);
          setanim(false);
          setBackAnim(true);
          setdone(false);
          setloader(false);
        }
      })
      .catch(err => {
        Toast.show('Something went wrong while sending otp request');
        sendErrorReport(err, 'send_otp');
        setanim(false);
        setBackAnim(true);
        setdone(false);
        setloader(false);
        console.log('ERRR', err);
      });
  };

  return (
    <>
      {/* <GradientBack /> */}
      {/* <View style={styles.mainContainer}> */}
      <SafeAreaView style={styles.root}>
        <KeyboardAvoidingView
          behavior={Platform.OS == 'ios' ? 'padding' : null}
          style={{
            justifyContent: 'center',
            flex: 1,
          }}>
          <ScrollView
            contentContainerStyle={{ flexGrow: 1, padding: 16 }}
            showsVerticalScrollIndicator={false}
            bounces={false}>
            <View style={styles.mainInputStyle}>
              <View style={styles.logoImg}>
                <Image
                  source={require('../../assets/images/logo.png')}
                  style={{ height: '100%', width: '100%' }}
                />
              </View>
              <Text style={[styles.loginText, { color: BaseColor.blackColor }]}>
                {translate('loginscreen')}
              </Text>
              <CInput
                ref={userRef}
                placeholder={translate('loginUser')}
                value={state.email}
                onChangeText={val => {
                  setstate({ ...state, email: val });
                }}
                placeholderTextColor={BaseColors.textGrey}
                iconName="user2"
                onSubmitEditing={() => {
                  passwordRef.current.focus();
                }}
                showError={emailError}
                errorMsg={emailErrorTxt}
                rightIcon
              />
              <CInput
                ref={passwordRef}
                placeholder={translate('loginPassword')}
                value={state.password}
                secureTextEntry={hideShow}
                onChangeText={val => {
                  setstate({ ...state, password: val });
                }}
                placeholderTextColor={BaseColors.textGrey}
                iconName="lock2"
                textInputWrapper={{
                  marginTop: 12,
                }}
                showError={passwordError}
                errorMsg={passwordErrorTxt}
                onSubmitEditing={() => {
                  Validation();
                }}
                // hideLeftIcon
                rightIcon={true}
                showRightIcon={true}
                rightIconName={hideShow ? 'eye-slash' : 'eye'}
                // iconName={hideShow ? 'eye-slash' : 'eye'}
                onShowPasswordpress={() => {
                  setHideShow(!hideShow);
                }}
              />
            </View>
            {/* <TouchableOpacity
              style={{
                marginTop: 12,
                flexDirection: 'row',
                alignItems: 'center',
                paddingHorizontal: 26,
              }}
              activeOpacity={0.7}
              onPress={() => {
                setstate({ ...state, rememberMe: !state.rememberMe });
              }}
            >
              <FAIcon
                name={state.rememberMe ? 'circle' : 'circle-o'}
                size={18}
                color={BaseColor.whiteColor}
              />
              <Text style={[styles.rememberText, {color: BaseColor.whiteColor,}]}>
                {translate('loginRemember')}
              </Text>
            </TouchableOpacity> */}
            <CButton
              style={styles.loginBtn}
              anim
              playAnimation={anim}
              backAnim={backAnim}
              onPress={() => {
                Validation();
              }}
              title={translate('loginBtn')}
              loader={loader}
              done={done}
            />
            <TouchableOpacity
              onPress={() => {
                navigation.navigate('ForgotPassword');
              }}
              style={{ alignItems: 'center', marginTop: 24 }}
              activeOpacity={0.7}>
              <Text
                style={{
                  fontSize: 15,
                  color: BaseColor.textGrey,
                  fontWeight: '600',
                  textAlign: 'left',
                }}>
                {translate('loginForgot')}
              </Text>
            </TouchableOpacity>

            <View style={{ marginTop: 36, alignItems: 'center' }}>
              <Text
                style={{
                  fontSize: 14,
                  color: BaseColor.textGrey,
                  textAlign: 'left',
                  letterSpacing: 0.8,
                  fontFamily: FontFamily.default,
                }}>
                {translate('loginAccount')}
              </Text>
            </View>
            <TouchableOpacity
              onPress={() => {
                navigation.navigate('Signup');
              }}
              style={{ paddingVertical: 5, alignItems: 'center' }}>
              <Text
                style={{
                  fontSize: 14,
                  color: BaseColor.textGrey,
                  fontWeight: 'bold',
                  textAlign: 'left',
                  letterSpacing: 1,
                  borderBottomWidth: 1.5,
                  fontFamily: FontFamily.default,
                  borderBottomColor: BaseColor.whiteColor,
                }}>
                {translate('loginToSignup')}
              </Text>
            </TouchableOpacity>
            <View style={{ alignItems: 'center' }}>
              <Text style={{ fontSize: 10, color: 'black' }}>
                Version {DeviceInfo.getVersion()}
              </Text>
            </View>
          </ScrollView>
        </KeyboardAvoidingView>

        <CButton
          iconname="cancel"
          iconsize={10}
          iconColor={BaseColor.blackColor}
          style={[styles.closeBtn, { backgroundColor: BaseColor.whiteColor }]}
          onPress={() => {
            navigation.navigate('RedirectLS');
          }}
        />
      </SafeAreaView>
      {/* </View> */}
    </>
  );
};

export default Login;
