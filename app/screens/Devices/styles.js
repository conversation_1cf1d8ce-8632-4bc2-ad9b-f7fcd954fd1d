import {Dimensions, StyleSheet} from 'react-native';
import BaseColor from '../../config/colors';
import {FontFamily} from '../../config/typography';

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: BaseColor.whiteColor,
  },
  flatlistView: {
    marginTop: 30,
    marginBottom: 10,
  },
  addNewProfile: {
    height: 90,

    backgroundColor: BaseColor.whiteColor,
    justifyContent: 'center',
    alignSelf: 'center',
    marginRight: 16,
    marginLeft: 54,
    marginTop: 8,
    flex: 1,
    borderColor: BaseColor.textGrey,
    borderWidth: 0.5,
    borderRadius: 20,
  },
  addText: {
    textAlign: 'center',
    fontSize: 20,
    color: BaseColor.blueDark,
    fontFamily: FontFamily.default,
  },
  tapText: {
    textAlign: 'center',
    fontSize: 12,
    paddingVertical: 4,
  },
  renderView: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    margin: 10,
  },
  checkIcon: {
    backgroundColor: BaseColor.whiteColor,
    width: 28,
    height: 28,
    borderRadius: 14,
    textAlign: 'center',
    textAlignVertical: 'center',
    marginRight: 10,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: BaseColor.textGrey,
    borderWidth: 0.5,
  },
  touchableContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: BaseColor.whiteColor,
    // marginVertical: 8,
    // borderRadius: 10,
    borderColor: BaseColor.textGrey,
    borderWidth: 0.5,
    borderRadius: 20,
  },
  image: {
    width: 89,
    height: 89,
    borderRadius: 20,
  },
  title: {
    color: BaseColor.blackColor,
    fontSize: 20,
    // fontWeight: 'bold',
    fontFamily: FontFamily.default,
    width: Dimensions.get('screen').width / 2 - 60,
  },
  connectedDevice: {
    color: BaseColor.textGrey,
    fontSize: 12,
    paddingVertical: 2,
    width: Dimensions.get('screen').width / 2 - 60,
  },
  contentTextView: {
    paddingHorizontal: 10,
  },
});

export default styles;
