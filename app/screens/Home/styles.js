/* eslint-disable quotes */
import {StyleSheet} from 'react-native';
import BaseColor from '../../config/colors';
import {FontFamily} from '../../config/typography';

const styles = StyleSheet.create({
  root: {
    flex: 1,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
  },
  deviceCard: {
    height: 140,
    width: 110,
    backgroundColor: BaseColor.whiteColor,
    // padding: 8,
    overflow: 'hidden',
    borderRadius: 16,
    marginEnd: 12,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: BaseColor.blackColor,
    borderWidth: 0.5,
  },
  smartDeviceView: {
    marginTop: 36,
  },
  guideView: {
    marginTop: 36,
  },
  guideRootView: {
    backgroundColor: BaseColor.whiteColor,
    borderRadius: 8,
    overflow: 'hidden',
    marginVertical: 12,
    display: 'flex',
  },
  videoView: {
    marginTop: 24,
    flex: 1,
    backgroundColor: BaseColor.lightgray,
  },
  closeBtn: {
    height: 40,
    width: 40,
    borderRadius: 20,
    alignSelf: 'flex-end',
    position: 'absolute',
    right: 12,
    top: 34,
  },
  emptyComponent: {
    minHeight: 450,
    alignItems: 'center',
    justifyContent: 'center',
  },
  childImgStyle: {
    height: 25,
    width: 25,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: BaseColor.black60,
  },
  childNameStyle: {
    fontFamily: FontFamily.default,
    fontWeight: '600',
    fontSize: 8,
    paddingLeft: 10,
    width: 50,
  },
});

export default styles;
