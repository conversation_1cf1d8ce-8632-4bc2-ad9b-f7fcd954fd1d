/* eslint-disable quotes */
import {Dimensions, StyleSheet} from 'react-native';
import BaseColor from '../../config/colors';
import {FontFamily} from '../../config/typography';

const {width: dWidth} = Dimensions.get('window');

const styles = StyleSheet.create({
  root: {
    flex: 1,
  },
  cardStyle: {
    flexWrap: 'wrap',
    overflow: 'hidden',
    width: dWidth - 52,
    height: dWidth - 92,
    borderRadius: 20,
    backgroundColor: BaseColor.whiteColor,
  },
  imageLastShadow: {
    // marginTop: 0,
    backgroundColor: BaseColor.white20,
    // justifyContent: "center",
    width: dWidth - 122,
    height: dWidth - 65,
    alignSelf: 'center',
    borderRadius: 20,
    marginTop: 10,
  },
  imagemiddleShadow: {
    backgroundColor: BaseColor.transparentWhite,
    justifyContent: 'center',
    width: dWidth - 80,
    height: dWidth - 80,
    alignSelf: 'center',
    borderRadius: 20,
  },
  imageView: {
    backgroundColor: BaseColor.whiteColor,
    width: dWidth - 52,
    height: dWidth - 92,
    alignSelf: 'center',
    justifyContent: 'center',
    borderRadius: 20,
  },
  selectedImage: {
    width: dWidth - 52,
    height: dWidth - 92,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imgTxt: {
    position: 'absolute',
    zIndex: 10,
    left: 16,
    bottom: 16,
  },
  childName: {
    fontWeight: 'bold',
    fontSize: 22,
    color: BaseColor.blackColor,
  },
  deviceMonit: {
    fontWeight: 'bold',
  },
  shape: {
    width: 279,
    // flex: 1,
    // backgroundColor: 'red',
    // height: 132,
    // alignSelf: "center",
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    position: 'absolute',
    // left: 0,
    // right: 0
  },
  shapeTitle: {
    width: '92%',
    alignSelf: 'center',
    padding: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingEnd: 64,
    paddingStart: 54,
  },
  alertView: {
    alignItems: 'center',
    marginTop: -5,
  },
  alertTxt: {
    color: BaseColor.blackColor,
    fontFamily: FontFamily.default,
    // fontWeight: '700',
    fontSize: 12,
  },
  alertcircle: {
    backgroundColor: BaseColor.whiteColor,
    padding: 6,
    borderRadius: 50,
    marginTop: 10,
    // marginTop: 0,
  },
  card: {
    backgroundColor: BaseColor.alertRed,
    width: dWidth - 32,
    height: dWidth - 32,
  },
  label: {
    fontSize: 24,
  },
  containerStyle: {
    backgroundColor: BaseColor.whiteColor,
    borderRadius: 20,
  },
  batteryContainer: {
    borderWidth: 1,
    borderColor: BaseColor.blackColor,
    borderRadius: 3,
    padding: 1,
    width: 35,
    height: 20,
  },
  batteryStatus: {
    backgroundColor: 'green',
    width: '80%',
    height: '100%',
    borderRadius: 3,
  },
  batteryDot: {
    height: 7,
    width: 6,
    backgroundColor: '#000',
    position: 'absolute',
    right: -6,
    top: '34%',
    borderTopRightRadius: 3,
    borderBottomRightRadius: 3,
  },
  batteryDisplay: {
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
    paddingHorizontal: 40,
    position: 'absolute',
    right: -20,
    top: 15,
  },
  disconnectContainer: {
    position: 'absolute',
    top: -8,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'red',
    padding: 7,
    borderRadius: 28,
    zIndex: 999999,
  },
  pulseViewStyle: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 100,
    alignItems: 'center',
    justifyContent: 'center',
  },
  ripleStyle: {
    position: 'absolute',
    bottom: -0,
    right: 0,
    height: 80,
    width: 80,
    borderRadius: 50,
    opacity: 0.4,
  },
  childImgStyle: {
    height: 45,
    width: 45,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: BaseColor.black60,
  },
  childNameStyle: {
    fontFamily: FontFamily.default,
    fontWeight: '600',
    fontSize: 12,
    paddingLeft: 10,
    width: 100,
  },
});

export default styles;
