/* eslint-disable max-len */
/* eslint-disable no-unused-expressions */
/* eslint-disable indent */
/* eslint-disable no-nested-ternary */
/* eslint-disable implicit-arrow-linebreak */
/* eslint-disable no-console */
/* eslint-disable global-require */
/* eslint-disable quotes */
import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {
  ActivityIndicator,
  BackHandler,
  Image,
  // NativeEventEmitter,
  // NativeModules,
  // PermissionsAndroid,
  Platform,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
  Animated,
} from 'react-native';
import {SvgXml} from 'react-native-svg';
import Toast from 'react-native-simple-toast';
import {
  findIndex,
  isArray,
  isEmpty,
  isObject,
  isUndefined,
  parseInt,
} from 'lodash';
import {check, PERMISSIONS, request} from 'react-native-permissions';
import {useFocusEffect, useTheme} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';
import BleManager from 'react-native-ble-manager';
import BluetoothStateManager from 'react-native-bluetooth-state-manager';
// import BackgroundTimer from "react-native-background-timer";
// import { bytesToString } from "convert-string";
import Icon from 'react-native-vector-icons/FontAwesome';
import Swiper from 'react-native-deck-swiper';
import CHeader from '../../components/CHeader';
import {CustomIcon} from '../../config/LoadIcons';
import {FontFamily} from '../../config/typography';
import {translate} from '../../lang/Translate';
import styles from './styles';
import BluetoothAction from '../../redux/reducers/bluetooth/actions';
import bluetoothActions from '../../redux/reducers/bluetooth/actions';
import CAlert from '../../components/CAlert';
import {getSWVal, sendErrorReport} from '../../utils/commonFunction';
import BaseSetting from '../../config/setting';
import {getApiData} from '../../utils/apiHelper';
// import { getApiData } from "../../utils/apiHelper";
// import BaseSetting from "../../config/setting";
import AuthAction from '../../redux/reducers/auth/actions';
import BaseColors from '../../config/colors';

let backPressed = 0;

/**
 *
 *@module Dashboard
 *
 */
const Dashboard = ({navigation}) => {
  const colors = useTheme();
  const BaseColor = colors.colors;
  // const peripherals = new Map();

  const dispatch = useDispatch();
  const {
    // eslint-disable-next-line no-unused-vars
    bleData,
    // characteristicID,
    // serviceID,
    // eslint-disable-next-line no-unused-vars
    deviceID,
    isBleConnected,
    connectedDeviceDetail,
    activeChildDetail,
    lowAlert,
    highAlert,
    isCurrentActiveDevice,
    activeDeviceId,
    isConnecting,
    swiperKey,
    // eslint-disable-next-line no-unused-vars
    emergencyAlert,
    lastDeviceId,
    // eslint-disable-next-line no-unused-vars
    isChildProductId,
    // isConnected
  } = useSelector(state => state.bluetooth);

  // console.log("🚀 ~ file: index.js ~ line 90 ~ Dashboard ~ isConnected", isConnected);
  const {
    setActiveDeviceId,
    setLastDeviceId,
    setDeviceID,
    setIsConnectLoad,
    setHomeDeviceClick,
    setSwiperKey,
    // eslint-disable-next-line no-unused-vars
    setChildProductId,
  } = bluetoothActions;
  const {setNotiCount} = AuthAction;
  const {isFarenheit} = useSelector(state => state.auth);
  const accessToken = useSelector(state => state.auth.accessToken);
  const animatedValue = useRef(new Animated.Value(0)).current;
  const animatedValue1 = useRef(new Animated.Value(0)).current;
  const [isChildActive, setIsChildActiveDevice] = useState({
    ...activeDeviceId,
  });

  // const parseBleData = bleData && !isEmpty(bleData) && !isObject(bleData)
  //   ? JSON.parse(bleData)
  //   : {};

  // useEffect(() => {
  //   const index = findIndex(
  //     connectedDeviceDetail,
  //     (i) => i.product_id === activeDeviceId.product_id
  //   );
  //   if (index > -1) {
  //     console.log("🚀 ~ file: index.js ~ line 135 ~ useEffect ~ index", index);
  //     dispatch(setActiveDeviceId(connectedDeviceDetail[index]));
  //   }
  // }, [connectedDeviceDetail, activeDeviceId]);

  // useFocusEffect(
  //     useCallback(() => {
  //       const index = findIndex(
  //         connectedDeviceDetail,
  //         (i) => i.product_id === activeDeviceId.product_id
  //       );
  //       if (index > -1) {
  //         setIsChildActiveDevice(connectedDeviceDetail[index]);
  //       }
  //     }, [])
  //   );
  useEffect(() => {
    if (Platform.OS === 'android' && Platform.Version >= 31) {
      sendErrorReport(Platform.Version, 'platform version2');
      check(PERMISSIONS.ANDROID.BLUETOOTH_CONNECT)
        .then(res => {
          console.log('BLUETOOTH_CONNECT--q-', res);
          if (res === 'granted') {
            // setBPermission(true);
          }
        })
        .catch(e => {
          console.log('bluetooth_', e);
        });
      request(PERMISSIONS.ANDROID.BLUETOOTH_CONNECT)
        .then(result => {
          console.log('BLUETOOTH_CONNECT----1', result);
        })
        .then(statuses => {
          console.log(
            'BLUETOOTH_CONNECT--2',
            statuses[PERMISSIONS.ANDROID.BLUETOOTH_CONNECT],
          );
        });
    }
  }, []);
  useEffect(() => {
    if (activeDeviceId) {
      const index = findIndex(
        connectedDeviceDetail,
        i => i.product_id === activeDeviceId.product_id,
      );
      if (index > -1) {
        // sendErrorReport(activeDeviceId, "activeDeviceId---dash");
        // sendErrorReport(
        //   connectedDeviceDetail[index],
        //   "connectedDeviceDetail[index]---dash"
        // );
        setIsChildActiveDevice(connectedDeviceDetail[index]);
      }
    }
  }, [activeDeviceId]);

  // useEffect(() => {
  //   setTimeout(() => {
  //     if (!isBleConnected && emergencyAlert) {
  //       navigation.navigate(translate("home"));
  //     }
  //   }, 1000);
  // }, [emergencyAlert]);

  const parseBleData = useMemo(
    () =>
      JSON.parse(
        isCurrentActiveDevice?.[activeDeviceId?.product_id || ''] || '{}',
      ) || {},
    [isCurrentActiveDevice, activeDeviceId],
  );

  const batteryVoltage =
    isObject(parseBleData) && !isEmpty(parseBleData) && parseBleData?.BV
      ? parseBleData.BV
      : parseBleData.bv;

  const SW =
    !isEmpty(activeDeviceId) && !isUndefined(activeDeviceId?.product_id)
      ? getSWVal(parseBleData, activeDeviceId)
      : 0;
  const temp =
    isObject(parseBleData) &&
    !isEmpty(parseBleData) &&
    parseBleData?.Temperature
      ? parseBleData.Temperature
      : parseBleData.TEMP;

  // const [startRead, setstartRead] = useState(false);
  // const [connnectedID, setconnnectedID] = useState("");
  // const [charID, setCharID] = useState("");
  // const [servID, setservID] = useState("");
  const [tempAlert, setTempAlert] = useState({
    type: '',
    bool: false,
    title: '',
    message: '',
  });
  // const [bluePulse, setBluePulse] = useState(false);

  const [myDevices, setMyDevices] = useState(false);
  const [isCardIndex, setCardIndex] = useState(0);

  const xml = `
  <svg xmlns="http://www.w3.org/2000/svg" width="279" height="120" viewBox="0 0 279 120">
  <g id="Group_6954" data-name="Group 6954" transform="translate(-83.034 -601.37)">
    <ellipse id="Ellipse_201" data-name="Ellipse 201" cx="60.5" cy="60" rx="60.5" ry="60" transform="translate(83.034 601.37)" fill=${BaseColor.card1}/>
    <ellipse id="Ellipse_202" data-name="Ellipse 202" cx="60.5" cy="60" rx="60.5" ry="60" transform="translate(241.034 601.37)" fill=${BaseColor.card1}/>
    <path id="Path_1561" data-name="Path 1561" d="M-1564,6677.491s15.631,13.9,33.446,13.9,37.812-13.9,37.812-13.9v85.244s-20-12.524-37.812-12.524S-1564,6762.735-1564,6762.735Z" transform="translate(1751.112 -6059.361)" fill=${BaseColor.card1}/>
  </g>
  </svg>
  `;

  // const [temp, settemp] = useState(10);

  // BluetoothStateManager.onStateChange((bluetoothState) => {
  //   console.log(
  //     "BluetoothStateManager.onStateChange -> bluetoothState",
  //     bluetoothState,
  //   );
  //   // do something...
  //   switch (bluetoothState) {
  //     case "Unknown":
  //     case "Resetting":
  //     case "Unsupported":
  //     case "Unauthorized":
  //     case "PoweredOff":
  //     case "PoweredOn":
  //       console.log("ON ==== ...... ******");
  //       // startScan();
  //       break;
  //     default:
  //       break;
  //   }
  // }, true /*= emitCurrentState */);

  useFocusEffect(
    useCallback(() => {
      if (accessToken !== '') {
        getBadgeCount();
      }
    }, []),
  );

  /** this function for get Badge Count
   * @function getBadgeCount
   * @param {object} data {}
   */
  async function getBadgeCount() {
    const headers = {
      'Content-Type': 'application/json',
      authorization: accessToken ? `Bearer ${accessToken}` : '',
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.getUserAlertCount,
        'POST',
        {},
        headers,
      );

      if (response.success) {
        dispatch(setNotiCount(response));
      } else {
        console.log(
          '🚀 ~ file: index.js ~ line 220 ~ getBadgeCount ~ response',
          response,
        );
      }
    } catch (error) {
      console.log('error for device list ===', error);
      sendErrorReport(error, 'get_device_list');
    }
  }

  useEffect(() => {
    setTimeout(() => {
      Animated.loop(
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1400,
          useNativeDriver: true,
        }),
      ).start();
    }, 100);
    setTimeout(() => {
      Animated.loop(
        Animated.timing(animatedValue1, {
          toValue: 1,
          duration: 1400,
          useNativeDriver: true,
        }),
      ).start();
    }, 500);
  }, []);

  // async function getConnected() {
  //   const headers = {
  //         "Content-Type": "application/json",
  //         authorization: accessToken ? `Bearer ${accessToken}` : "",
  //       };

  //       try {
  //         const response = await getApiData(
  //           BaseSetting.endpoints.getDeviceConnectStatus,
  //           "POST",
  //           {},
  //           headers
  //         );

  //         setIsConnected(response.is_connected);
  //         if (response.is_connected === false) {
  //           setTempAlert({
  //                     type: "",
  //                     bool: true,
  //                     title: "No products connected",
  //                     message: "",
  //                   });
  //         }
  //         console.log("🚀 ~ file: index.js ~ line 249 ~ getConnected ~ response", response);
  //       } catch (error) {
  //         console.log("error for device connection ===", error);
  //         sendErrorReport(error);
  //       }
  // }
  //   useFocusEffect(
  //     React.useCallback(() => {
  //       getConnected();
  //       // if (!isConnected) {
  //       //   setTempAlert({
  //       //     type: "",
  //       //     bool: true,
  //       //     title: "No products connected",
  //       //     message: "",
  //       //   });
  //       // }
  //     }, []),
  //   );

  useEffect(() => {
    BluetoothStateManager.getState().then(bluetoothState => {
      switch (bluetoothState) {
        case 'Unknown':
        case 'Resetting':
        case 'Unsupported':
        case 'Unauthorized':
        case 'PoweredOff':
          if (Platform.OS === 'android') {
            sendErrorReport(true, 'requestToEnableDash');
            check(PERMISSIONS.ANDROID.BLUETOOTH_CONNECT).then(res => {
              console.log('BLUETOOTH_CONNECT---', res);
              if (res === 'granted') {
                BluetoothStateManager.requestToEnable().then(result => {
                  console.log(
                    'BluetoothStateManager.requestToEnable -> result',
                    result,
                  );
                });
              }
            });
            request(PERMISSIONS.ANDROID.BLUETOOTH_CONNECT)
              .then(result => {
                console.log('BLUETOOTH_CONNECT----1', result);
              })
              .then(statuses => {
                console.log(
                  'BLUETOOTH_CONNECT--2',
                  statuses[PERMISSIONS.ANDROID.BLUETOOTH_CONNECT],
                );
              });
          } else {
            console.log('=====1111111');
            BluetoothStateManager.openSettings();
          }
          break;
        case 'PoweredOn':
          console.log('ON ==== ...... ******');
          // startScan();
          break;
        default:
          break;
      }
    });
  }, []);

  const getConvertedTemp = realTemp => {
    if (isFarenheit) {
      return ((Number(realTemp) * 9) / 5 + 32).toFixed(0);
    }
    return realTemp;
  };

  useEffect(() => {
    // dispatch(setActiveDeviceId(isActive));
    if (connectedDeviceDetail.length > 0) {
      connectedDeviceDetail.map(item => {
        if (isUndefined(item.product_id)) {
          connectedDeviceDetail.splice(item);
        }
      });
    }
  }, [connectedDeviceDetail]);

  useEffect(() => {
    BleManager.getConnectedPeripherals([]).then(results => {
      console.log('SEE CONNECTED PERI', results);
      const mydevs = [];
      results.map(v => {
        mydevs.push({id: v.id, name: v.name});
      });
      setMyDevices(mydevs);
    });
  }, []);

  function handleBackButtonClick() {
    if (backPressed > 0) {
      BackHandler.exitApp();
      backPressed = 0;
    } else {
      backPressed++;
      Toast.show('Press Again To Exit');
      setTimeout(() => {
        backPressed = 0;
      }, 2000);
      return true;
    }
    return true;
  }

  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      handleBackButtonClick,
    );
    return () => backHandler?.remove();
  }, []);

  const batteryPercent =
    batteryVoltage === 271
      ? 90
      : batteryVoltage === 270
      ? 80
      : batteryVoltage === 269 || batteryVoltage === 268
      ? 70
      : batteryVoltage === 267 || batteryVoltage === 266
      ? 60
      : batteryVoltage >= 260
      ? 50
      : batteryVoltage >= 252
      ? 40
      : batteryVoltage >= 246
      ? 30
      : batteryVoltage >= 236
      ? 20
      : batteryVoltage >= 215
      ? 10
      : batteryVoltage >= 175
      ? 0
      : 100;

  // this function for handle blue pulse
  useEffect(() => {
    let text = '';
    const possible =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < 5; i++) {
      text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    dispatch(BluetoothAction.setSwiperKey(text));
  }, []);
  useEffect(() => {
    if (
      parseBleData &&
      !isEmpty(activeDeviceId) &&
      (activeDeviceId?.device_bluetooth_name.toLowerCase() === 'babyauto-csa' ||
        activeDeviceId?.device_bluetooth_name.toLowerCase() ===
          'baby_auto-csa' ||
        activeDeviceId?.device_bluetooth_name.toLowerCase() ===
          'baby_auto-csb' ||
        activeDeviceId?.device_bluetooth_name.toLowerCase() ===
          'babyauto-csb' ||
        activeDeviceId?.device_bluetooth_name.toLowerCase() === 'reebaby-csa')
    ) {
      const arr = [
        parseBleData.s2,
        parseBleData.s3,
        parseBleData.s4,
        parseBleData.s5,
      ];

      if (arr.length > 0) {
        let count = 0;
        arr.map(val => {
          if (val >= 1) {
            count += 1;
          }
        });

        // if (count >= 3) {
        //   setBluePulse(true);
        // }
      }

      if (!isEmpty(activeChildDetail) && activeChildDetail.weight) {
        if (
          activeChildDetail.weight > 0 &&
          activeChildDetail.weight <= 9.5 &&
          parseBleData.s9 === 0
        ) {
          setTempAlert({
            type: '',
            bool: true,
            title: 'homeAlertTitle',
            message: 'changeSeatToForward',
          });
        }

        if (
          activeChildDetail.weight > 10 &&
          activeChildDetail.weight <= 36 &&
          (parseBleData.s6 === 1 ||
            parseBleData.s7 === 0 ||
            parseBleData.s8 === 0 ||
            parseBleData.s9 === 0 ||
            parseBleData.s10 === 0)
        ) {
          setTempAlert({
            type: '',
            bool: true,
            title: 'homeAlertTitle',
            message: 'changeSeatToRearFace',
          });
        }
      }
    }
    // sendErrorReport(parseBleData, "parseBleData");
  }, [parseBleData]);

  const Sensor = ({style}) => (
    <View
      style={[
        {
          position: 'absolute',
          height: 20,
          width: 20,
          borderRadius: 10,
          backgroundColor: BaseColor.alertRed,
        },
        style,
      ]}
    />
  );

  const multipleSensor =
    isObject(activeDeviceId) &&
    !isEmpty(activeDeviceId) &&
    (activeDeviceId?.device_bluetooth_name?.toLowerCase() === 'babyauto-csa' ||
      activeDeviceId?.device_bluetooth_name?.toLowerCase() === 'babyauto-csb' ||
      activeDeviceId?.device_bluetooth_name?.toLowerCase() === 'reebaby-csa' ||
      activeDeviceId?.device_bluetooth_name?.toLowerCase() ===
        'baby_auto-csa' ||
      activeDeviceId?.device_bluetooth_name?.toLowerCase() === 'baby_auto-csb');

  const cel = parseBleData?.Temperature
    ? parseInt(parseBleData?.Temperature - 2)
    : parseBleData?.Temp
    ? parseInt(parseBleData.Temp - 2)
    : 0;

  const displayTemperature = getConvertedTemp(cel);
  const displayHumidity = parseBleData?.Humidity
    ? Math.round(parseBleData?.Humidity)
    : parseBleData?.HUMIDITY
    ? Math.round(parseBleData?.HUMIDITY)
    : 0;

  const isReebabyCSA =
    activeDeviceId?.device_bluetooth_name?.toLowerCase() === 'reebaby-csa';
  const isBabyAutoCSA =
    activeDeviceId?.device_bluetooth_name?.toLowerCase() === 'baby_auto-csa';
  const isBabyAutoCSB =
    activeDeviceId?.device_bluetooth_name?.toLowerCase() === 'baby_auto-csb';

  async function getAutoConnect(id, childId) {
    const headers = {
      'Content-Type': 'application/json',
      authorization: accessToken ? `Bearer ${accessToken}` : '',
    };
    sendErrorReport(activeDeviceId, 'autoConnect===dash');
    sendErrorReport(id, 'autoConnect===id');
    sendErrorReport(childId, 'autoConnect===childId');
    try {
      const response = await getApiData(
        BaseSetting.endpoints.autoConn,
        'POST',
        {
          product_id: id,
          child_id: childId,
        },
        headers,
      );

      if (response.success) {
        console.log(
          '🚀 ~ file: index.js ~ line 544 ~ getAutoConnect ~ response',
          response,
        );
      } else {
        // Toast.show("error===>>>", response.message);
      }
    } catch (error) {
      sendErrorReport(JSON.parse(error), 'autoConnectDash');
      console.log('feed post error ===', error);
    }
  }

  const swiper = d => (
    <Swiper
      cards={connectedDeviceDetail}
      key={swiperKey}
      keyExtractor={data => data?.id}
      renderCard={item => (
        <View
          // key={swiperKey}
          style={{
            alignItems: 'center',
            justifyContent: 'center',
            position: 'relative',
            zIndex: 999999,
          }}>
          <Image
            // key={swiperKey}
            style={styles.selectedImage}
            source={
              !isUndefined(d) && !isEmpty(d) && !isEmpty(connectedDeviceDetail)
                ? {uri: d?.device_image}
                : require('../../assets/images/13.jpg')
            }
            resizeMode="cover"
          />
          {d?.nick_name && (
            <View
              style={{
                position: 'absolute',
                flex: 1,
                flexDirection: 'row',
                alignItems: 'center',
                bottom: 10,
                left: 10,
                backgroundColor: '#fff',
                borderRadius: 12,
              }}>
              <Image
                source={
                  d.child_profile
                    ? {uri: d.child_profile}
                    : require('../../assets/images/logo.png')
                }
                style={[styles.childImgStyle, {borderColor: BaseColor.black60}]}
              />
              <Text
                style={[styles.childNameStyle, {color: BaseColor.blackColor}]}
                numberOfLines={2}>
                {d.nick_name}
              </Text>
            </View>
          )}
          {multipleSensor ? (
            <>
              {/* baby_auto-csa */}
              {(isBabyAutoCSA &&
                activeChildDetail?.weight > 16 &&
                activeChildDetail?.weight <= 36) ||
              (isBabyAutoCSB && SW > 16 && SW <= 36) ? (
                <>
                  <Sensor
                    id="s4"
                    style={[
                      {top: 90, left: 150},
                      parseBleData.s4 === 0 ? {backgroundColor: '#008000'} : {},
                    ]}
                  />
                  <Sensor
                    id="s5"
                    style={[
                      {top: 90, right: 90},
                      parseBleData.s5 === 0 ? {backgroundColor: '#008000'} : {},
                    ]}
                  />
                  <Sensor
                    id="s2"
                    style={[
                      {top: 200, left: 100},
                      parseBleData.s2 === 0 ? {backgroundColor: '#008000'} : {},
                    ]}
                  />
                  <Sensor
                    id="s3"
                    style={[
                      {top: 200, right: 90},
                      parseBleData.s3 === 0 ? {backgroundColor: '#008000'} : {},
                    ]}
                  />
                </>
              ) : null}

              {!isBabyAutoCSA || !isBabyAutoCSB ? (
                <>
                  <Sensor
                    id="s4"
                    style={[
                      {top: 85, left: 110},
                      parseBleData.s4 === 0 ? {backgroundColor: '#008000'} : {},
                    ]}
                  />
                  <Sensor
                    id="s5"
                    style={[
                      {top: 80, right: 100},
                      parseBleData.s5 === 0 ? {backgroundColor: '#008000'} : {},
                    ]}
                  />
                  <Sensor
                    id="s2"
                    style={[
                      {top: 202, left: 110},
                      parseBleData.s2 === 0 ? {backgroundColor: '#008000'} : {},
                    ]}
                  />
                  <Sensor
                    id="s3"
                    style={[
                      {top: 202, right: 100},
                      parseBleData.s3 === 0 ? {backgroundColor: '#008000'} : {},
                    ]}
                  />
                </>
              ) : null}

              {/* S5 */}
              {/* <Sensor
                id="s5"
                style={[
                  { top: 90, right: 90 },
                  parseBleData.s5 === 0 ? { backgroundColor: "#008000" } : {},
                ]}
              /> */}
              {/* <Sensor
                id="s2"
                style={[
                  { top: 200, left: 100 },
                  parseBleData.s2 === 0 ? { backgroundColor: "#008000" } : {},
                ]}
              />
              <Sensor
                id="s3"
                style={[
                  { top: 200, right: 90 },
                  parseBleData.s3 === 0 ? { backgroundColor: "#008000" } : {},
                ]}
              /> */}
              <Sensor
                id="s7"
                style={[
                  {bottom: 56, left: 95},
                  parseBleData.s7 === 1 ? {backgroundColor: '#008000'} : {},
                ]}
              />
              <Sensor
                id="s8"
                style={[
                  {bottom: 56, right: 85},
                  parseBleData.s8 === 1 ? {backgroundColor: '#008000'} : {},
                ]}
              />
              <Sensor
                id="s6"
                style={[
                  {bottom: 12, right: '46%'},
                  parseBleData.s6 === 0 ? {backgroundColor: '#008000'} : {},
                ]}
              />
            </>
          ) : null}
          <View style={styles.pulseViewStyle}>
            {/* {SW > 0 ||
            (activeChildDetail?.weight > 0 &&
              (isBabyAutoCSA || isBabyAutoCSB)) ? (
              <>
                <Animated.View
                  style={[
                    styles.ripleStyle,
                    {
                      // backgroundColor: "rgba(0,128,0,0.6)",
                      backgroundColor: "rgba(198, 240, 245, 0.6)",
                      transform: [
                        {
                          scale: animatedValue,
                        },
                      ],
                    },
                    multipleSensor ? { bottom: -10, right: -10 } : {},
                  ]}
                />
                <Animated.View
                  style={[
                    styles.ripleStyle,
                    {
                      // backgroundColor: BaseColor.bDelight,
                      backgroundColor: "rgba(198, 240, 245, 0.3)",
                      transform: [
                        {
                          scale: animatedValue1,
                        },
                      ],
                    },
                    multipleSensor ? { bottom: -10, right: -10 } : {},
                  ]}
                />
              </>
            ) : null} */}
            {/* <View
              style={[
                {
                  position: "absolute",
                  height: 60,
                  width: 60,
                  borderRadius: 50,
                  bottom: 10,
                  right: 10,
                  backgroundColor: SW >= 1 ? BaseColor.bDelight : null,
                },
                multipleSensor ? { bottom: 0, right: 0 } : {},
              ]}
            >
              <View
                style={[
                  {
                    position: "absolute",
                    height: 45,
                    width: 45,
                    borderRadius: 50,
                    bottom: 7,
                    right: 7,
                    backgroundColor: SW >= 1 ? BaseColor.bLight : null,
                  },
                  multipleSensor ? { bottom: 7, right: 7 } : {},
                ]}
              >
                <View
                  style={[
                    {
                      position: "absolute",
                      height: 30,
                      width: 30,
                      borderRadius: 18,
                      // opacity: 1,
                      bottom: 8,
                      right: 8,
                      justifyContent: "center",
                      alignItems: "center",
                      backgroundColor:
                        SW >= 1 ? BaseColor.blueDark : BaseColor.alertRed,
                    },
                    multipleSensor ? { bottom: 7, right: 7 } : {},
                  ]}
                />
              </View>
            </View> */}
          </View>
          {batteryPercent ? (
            batteryPercent < 25 ? (
              <View style={styles.batteryDisplay}>
                <View style={styles.batteryContainer}>
                  <View
                    style={{
                      position: 'absolute',
                      zIndex: 99,
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: '100%',
                      height: '100%',
                    }}>
                    <Text
                      style={{
                        fontSize: 9,
                        textAlign: 'center',
                        marginTop: 2,
                        color:
                          batteryPercent >= 25
                            ? BaseColor.whiteColor
                            : BaseColor.alertRed,
                      }}>
                      {`${batteryPercent || 0}%`}
                    </Text>
                  </View>
                  <View
                    style={[
                      styles.batteryStatus,
                      {
                        width: `${batteryPercent}%`,
                        backgroundColor:
                          batteryPercent >= 25 ? 'green' : BaseColor.alertRed,
                      },
                    ]}
                  />
                  <View style={styles.batteryDot} />
                </View>
              </View>
            ) : null
          ) : null}
        </View>
      )}
      onSwiped={cardIndex => {
        setCardIndex(cardIndex);
        dispatch(setActiveDeviceId(connectedDeviceDetail[cardIndex]));
      }}
      disableRightSwipe={
        connectedDeviceDetail.length === 0 || connectedDeviceDetail.length === 1
      }
      disableLeftSwipe={
        connectedDeviceDetail.length === 0 || connectedDeviceDetail.length === 1
      }
      containerStyle={[
        styles.containerStyle,
        {backgroundColor: BaseColor.whiteColor},
      ]}
      verticalSwipe={false}
      infinite
      cardVerticalMargin={0}
      cardHorizontalMargin={0}
      cardIndex={isCardIndex}
      backgroundColor={BaseColor.whiteColor}
      stackSize={1}
      cardStyle={[styles.cardStyle, {backgroundColor: BaseColor.whiteColor}]}
      swipeAnimationDuration={10}
      animateCardOpacity
      stackAnimationTension={10}
      stackScale={10}
      stackAnimationFriction={0}
      stackSeparation={0}
      goBackToPreviousCardOnSwipeLeft
    />
  );
  // console.log("is child active-----==", isChildActive);
  return (
    <View style={{flex: 1, backgroundColor: BaseColor.whiteColor}}>
      {/* <GradientBack /> */}
      <CHeader
        image={require('../../assets/images/logo.png')}
        leftIconName="settings-2"
        rightIconName="notifications-bell-button"
        onLeftPress={() => {
          navigation.openDrawer();
        }}
        onRightPress={() => {
          navigation.navigate('Alerts');
        }}
      />
      <ScrollView
        contentContainerStyle={{paddingBottom: 150, marginTop: 16}}
        bounces={false}>
        <View style={{paddingBottom: 10}}>
          <View
            style={[
              styles.imageLastShadow,
              {backgroundColor: BaseColor.card2},
            ]}>
            <View
              style={[
                styles.imagemiddleShadow,
                {backgroundColor: BaseColor.card1},
              ]}>
              <View style={{marginBottom: 20}}>
                <View
                  activeOpacity={0.9}
                  // onPress={image}
                  style={[
                    styles.imageView,
                    {position: 'relative'},
                    // { backgroundColor: BaseColor.whiteColor },
                  ]}>
                  {swiper(isChildActive)}
                  {isConnecting ? (
                    <View
                      style={[
                        {
                          position: 'absolute',
                          height: 45,
                          width: 45,
                          borderRadius: 30,
                          bottom: 10,
                          right: 15,
                          justifyContent: 'center',
                          alignItems: 'center',
                          backgroundColor: BaseColor.whiteColor,
                        },
                      ]}>
                      <ActivityIndicator
                        color={BaseColor.blackColor}
                        size={30}
                      />
                    </View>
                  ) : !isBleConnected ? (
                    <View
                      style={[
                        {
                          position: 'absolute',
                          height: 45,
                          width: 45,
                          borderRadius: 30,
                          bottom: 10,
                          right: 15,
                          justifyContent: 'center',
                          alignItems: 'center',
                          backgroundColor: BaseColor.alertRed,
                        },
                      ]}>
                      <CustomIcon
                        name="link_off"
                        color={BaseColor.whiteColor}
                        size={22}
                      />
                    </View>
                  ) : (
                    <View
                      style={[
                        {
                          position: 'absolute',
                          height: 60,
                          width: 60,
                          borderRadius: 50,
                          bottom: 10,
                          right: 10,
                          backgroundColor:
                            SW >= 1 && isBleConnected
                              ? BaseColor.bDelight
                              : null,
                        },
                        multipleSensor ? {bottom: 0, right: 0} : {},
                      ]}>
                      <View
                        style={[
                          {
                            position: 'absolute',
                            height: 45,
                            width: 45,
                            borderRadius: 50,
                            bottom: 7,
                            right: 7,
                            backgroundColor:
                              SW >= 1 && isBleConnected
                                ? BaseColor.bLight
                                : null,
                          },
                          multipleSensor ? {bottom: 7, right: 7} : {},
                        ]}>
                        <View
                          style={[
                            {
                              position: 'absolute',
                              height: 30,
                              width: 30,
                              borderRadius: 18,
                              // opacity: 1,
                              bottom: 8,
                              right: 8,
                              justifyContent: 'center',
                              alignItems: 'center',
                              backgroundColor:
                                SW >= 1 && isBleConnected
                                  ? BaseColor.blueDark
                                  : BaseColor.alertRed,
                            },
                            multipleSensor ? {bottom: 7, right: 7} : {},
                          ]}
                        />
                      </View>
                    </View>
                  )}
                </View>
              </View>
            </View>
          </View>
        </View>
        {!isEmpty(activeDeviceId) &&
        activeDeviceId?.device_bluetooth_name?.toLowerCase() ===
          'babyauto-csa' ? null : (
          <>
            <View style={styles.shapeTitle}>
              <Text style={styles.alertTxt}>{translate('tempTitle')}</Text>
              <Text style={styles.alertTxt}>{translate('humidityTitle')}</Text>
            </View>
            <View
              style={{
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <SvgXml xml={xml} width="100%" />
              <View style={styles.shape}>
                <View
                  style={{
                    height: 116,
                    width: 116,
                    borderRadius: 100,
                    padding: 16,
                    // position: "absolute",
                    // left: 0,
                  }}>
                  <TouchableOpacity
                    activeOpacity={0.8}
                    onPress={() => {
                      dispatch(BluetoothAction.setTempModal(true));
                    }}
                    style={{
                      flex: 1,
                      backgroundColor: BaseColor.whiteColor,
                      borderRadius: 60,
                      justifyContent: 'center',
                      alignItems: 'center',
                      flexDirection: 'row',
                    }}>
                    <Text
                      style={{
                        fontFamily: FontFamily.default,
                        fontWeight: 'bold',
                        fontSize: 28,
                        color: BaseColor.black90,
                      }}>
                      {!isBleConnected ? '--' : `${displayTemperature}°`}
                    </Text>
                    <Text
                      style={{
                        fontFamily: FontFamily.default,
                        fontWeight: 'bold',
                        fontSize: 22,
                        color: BaseColor.black90,
                        alignSelf: 'center',
                        marginTop: -20,
                      }}>
                      {!isBleConnected ? '' : isFarenheit ? 'F' : 'C'}
                    </Text>
                  </TouchableOpacity>
                </View>
                <View
                  // onPress={() => {
                  //   let tempVal = temp + 1;
                  //   settemp(tempVal)
                  // }}
                  style={{
                    height: 116,
                    width: 116,
                    borderRadius: 100,
                    padding: 16,
                    // position: "absolute",
                    // right: 4,
                  }}>
                  <TouchableOpacity
                    activeOpacity={0.8}
                    onPress={() =>
                      dispatch(BluetoothAction.setHumdtModal(true))
                    }
                    style={{
                      flex: 1,
                      backgroundColor: BaseColor.whiteColor,
                      borderRadius: 100,
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}>
                    <Text
                      style={{
                        fontFamily: FontFamily.default,
                        fontWeight: 'bold',
                        fontSize: 28,
                        color: BaseColor.black90,
                      }}>
                      {/* {parseBleData?.Humidity
                        ? Math.round(parseBleData?.Humidity)
                        : parseBleData?.HUMIDITY
                        ? Math.round(parseBleData?.HUMIDITY)
                        : 0}
                      % */}
                      {!isBleConnected ? '--' : `${displayHumidity}%`}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
            <View style={styles.alertView}>
              <Text style={[styles.alertTxt, {color: BaseColor.blackColor}]}>
                {translate('carSeatText')}
              </Text>
              <View
                style={[
                  styles.alertcircle,
                  {backgroundColor: BaseColor.whiteColor},
                ]}>
                {isConnecting ? (
                  <ActivityIndicator color={BaseColor.blackColor} size={30} />
                ) : !isBleConnected && lastDeviceId ? (
                  <TouchableOpacity
                    style={{
                      backgroundColor: BaseColor.blueDark,
                      borderRadius: 10,
                    }}
                    activeOpacity={0.7}
                    onPress={() => {
                      sendErrorReport(
                        activeDeviceId?.product_id,
                        'activeDeviceId?.product_id',
                      );
                      dispatch(setIsConnectLoad(true));
                      dispatch(setHomeDeviceClick(true));
                      sendErrorReport(true, 'deviceIdSet');
                      dispatch(setDeviceID(activeDeviceId?.product_id));
                      dispatch(setLastDeviceId(activeDeviceId?.product_id));
                      setTimeout(() => {
                        if (!isBleConnected) {
                          dispatch(setIsConnectLoad(false));
                        }
                      }, 1000);
                      setTimeout(() => {
                        dispatch(setActiveDeviceId(activeDeviceId));
                        dispatch(setSwiperKey(activeDeviceId?.product_id));
                        // dispatch(setChildProductId({ product_id: activeDeviceId?.product_id, child_id: activeDeviceId.child_id }));
                        // getAutoConnect(isChildProductId.product_id, isChildProductId.child_id);
                      }, 1000);
                    }}>
                    <Text
                      style={{
                        borderRadius: 10,
                        borderColor: BaseColor.blueDark,
                        borderWidth: 0.5,
                        paddingVertical: 5,
                        paddingHorizontal: 20,
                        justifyContent: 'center',
                        textTransform: 'uppercase',
                        color: BaseColor.whiteColor,
                      }}>
                      {translate('connectBtn')}
                    </Text>
                  </TouchableOpacity>
                ) : (SW <= 0 ||
                    getConvertedTemp(temp) > Number(highAlert) ||
                    getConvertedTemp(temp) < Number(lowAlert)) &&
                  isBleConnected ? (
                  <CustomIcon
                    name="warning"
                    color={BaseColor.alertRed}
                    size={18}
                  />
                ) : isBleConnected ? (
                  <View style={{borderRadius: 20, overflow: 'hidden'}}>
                    <Icon
                      name="check"
                      color={BaseColor.whiteColor}
                      size={18}
                      style={{
                        borderRadius: 20,
                        borderColor: BaseColor.blueDark,
                        borderWidth: 0.5,
                        padding: 10,
                        justifyContent: 'center',
                        backgroundColor: BaseColor.blueDark,
                      }}
                    />
                  </View>
                ) : (
                  <TouchableOpacity
                    style={{
                      backgroundColor: BaseColor.blueDark,
                      borderRadius: 10,
                    }}
                    activeOpacity={0.7}
                    onPress={() => {
                      dispatch(setIsConnectLoad(true));
                      dispatch(setHomeDeviceClick(true));
                      sendErrorReport(
                        activeDeviceId?.product_id,
                        'read_data_deviceID_connect',
                      );
                      sendErrorReport(true, 'deviceIdSet2');
                      dispatch(setDeviceID(activeDeviceId?.product_id));
                      dispatch(setLastDeviceId(activeDeviceId?.product_id));
                      setTimeout(() => {
                        if (!isBleConnected) {
                          dispatch(setIsConnectLoad(false));
                        }
                      }, 1000);
                      setTimeout(() => {
                        dispatch(setActiveDeviceId(activeDeviceId));
                        dispatch(setSwiperKey(activeDeviceId?.product_id));
                        // dispatch(setChildProductId({ product_id: activeDeviceId?.product_id, child_id: activeDeviceId.child_id }));
                        // getAutoConnect(isChildProductId.product_id, isChildProductId.child_id);
                      }, 1000);
                    }}>
                    <Text
                      style={{
                        borderRadius: 10,
                        borderColor: BaseColor.blueDark,
                        borderWidth: 0.5,
                        paddingVertical: 5,
                        paddingHorizontal: 20,
                        justifyContent: 'center',
                        textTransform: 'uppercase',
                        color: BaseColor.whiteColor,
                      }}>
                      {translate('connectBtn')}
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            </View>
          </>
        )}
      </ScrollView>
      <CAlert
        visible={tempAlert.bool} // Only visible if logged in
        type={tempAlert.type}
        onCancelPress={() =>
          setTempAlert({
            type: '',
            bool: false,
            title: '',
            message: '',
          })
        }
        alertTitle={translate(tempAlert.title)}
        alertMessage={translate(tempAlert.message)}
        onOkPress={() =>
          setTempAlert({
            type: '',
            bool: false,
            title: '',
            message: '',
          })
        }
      />
      <CAlert
        visible={tempAlert.bool && accessToken} // Only visible if there are no product connected
        type={tempAlert.type}
        onCancelPress={() =>
          setTempAlert({
            type: '',
            bool: false,
            title: '',
            message: '',
          })
        }
        alertTitle={translate(tempAlert.title)}
        alertMessage={translate(tempAlert.message)}
        onOkPress={() =>
          setTempAlert({
            type: '',
            bool: false,
            title: '',
            message: '',
          })
        }
      />
    </View>
  );
};

export default Dashboard;
