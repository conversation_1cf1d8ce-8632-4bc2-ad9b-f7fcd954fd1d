/* eslint-disable quotes */
/* eslint-disable no-unused-expressions */
import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Platform,
  Alert,
  Dimensions,
} from 'react-native';

import {useDispatch, useSelector} from 'react-redux';
import {useTheme} from '@react-navigation/native';

import AuthAction from '../../redux/reducers/auth/actions';
import CHeader from '../../components/CHeader';
import WifiManager from 'react-native-wifi-reborn';
import {PermissionsAndroid} from 'react-native';
import {check, PERMISSIONS} from 'react-native-permissions';
import WebView from 'react-native-webview';
import {openInAppBrowser} from '../../utils/commonFunction';
/**
 *
 *@module OTAWeb
 *
 */
function OTAWeb({route, navigation}) {
  const colors = useTheme();
  const BaseColor = colors.colors;

  const {setUserId, setAccessToken, setUserData} = AuthAction;
  const dispatch = useDispatch();
  const [show, ishow] = useState(false);

  return (
    <>
      <View style={{flex: 1}}>
        <CHeader
          title="OTAWeb"
          backBtn
          leftIconName
          onLeftPress={() => {
            navigation.goBack();
          }}
        />
        <View
          style={{
            height: Dimensions.get('window').height - 300,
            backgroundColor: BaseColor.primary,
          }}>
          <WebView
            source={{
              uri: 'http://192.168.4.1/?userid=admin&pwd=admin',
            }}
            pullToRefreshEnabled
            onError={() => {
              ishow(true);
            }}
          />
        </View>
      </View>
    </>
  );
}

export default OTAWeb;
