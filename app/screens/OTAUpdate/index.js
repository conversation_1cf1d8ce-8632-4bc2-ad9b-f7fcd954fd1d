/* eslint-disable quotes */
/* eslint-disable no-unused-expressions */
import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Platform,
  Alert,
  Dimensions,
} from "react-native";

import { useDispatch, useSelector } from "react-redux";
import { useTheme } from "@react-navigation/native";

import AuthAction from "../../redux/reducers/auth/actions";
import CHeader from "../../components/CHeader";
import WifiManager from "react-native-wifi-reborn";
import { PermissionsAndroid } from "react-native";
import { check, PERMISSIONS } from "react-native-permissions";
import wifi from "react-native-android-wifi";
import WebView from "react-native-webview";
import { openInAppBrowser } from "../../utils/commonFunction";
import CButton from "../../components/CButton";
/**
 *
 *@module OTAUpdate
 *
 */
function OTAUpdate({ route, navigation }) {
  const colors = useTheme();
  const BaseColor = colors.colors;

  const { setUserId, setAccessToken, setUserData } = AuthAction;
  const dispatch = useDispatch();
  const [show, ishow] = useState(false);
  const checkPermission = () => {
    if (Platform.OS === "android") {
      console.log("called---6");
      PermissionsAndroid.check(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION
      ).then((result) => {
        if (result) {
          console.log("Permission is OK");
        } else {
          PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION
          ).then((res) => {
            if (res) {
              console.log("User accept");
            } else {
              console.log("User refuse");
            }
          });
        }
      });
    } else {
      check(PERMISSIONS.IOS.LOCATION_ALWAYS).then((res) => {
        if (res !== "granted") {
          console.log("permission granted === ");
        }
      });
    }
  };

  return (
    <>
      <View style={{ flex: 1 }}>
        <CHeader
          title="OTAUpdate"
          backBtn
          leftIconName
          onLeftPress={() => {
            navigation.goBack();
          }}
        />
        <TouchableOpacity
          onPress={async () => {
            checkPermission();
            if (Platform.OS === "android") {
              //found returns true if ssid is in the range
              wifi.findAndConnect("Bugaboo-HC", "ChillBaby", (found) => {
                if (found) {
                  setTimeout(() => {
                    ishow(true);
                  }, 1000);

                  // openInAppBrowser(
                  //   "http://192.168.4.1/?userid=admin&pwd=admin"
                  // );
                  // Alert.alert("wifi is in range connected");
                  console.log("wifi is in range");
                } else {
                  ishow(false);
                  console.log("wifi is not in range");
                }
              });
            }
            // setTimeout(() => {
            //   wifi.disconnect();
            //   WifiManager.getCurrentWifiSSID().then(
            //     (ssid) => {
            //       console.log("Your current connected wifi SSID is " + ssid);
            //     },
            //     () => {
            //       console.log("Cannot get current SSID!");
            //     }
            //   );
            // }, 2500);
            // if (Platform.OS === 'android') {
            //   await WifiManager.loadWifiList()
            //     .then(list => {
            //       console.log('wifi listqq!', list);
            //       setTimeout(async () => {
            //         await WifiManager.connectToProtectedSSID(
            //           'Bugaboo-HC',
            //           'ChillBaby',
            //           true,
            //         )
            //           .then(
            //             c => {
            //               // Alert.alert("wifi is in connected");
            //               // Alert.prompt("wifi is in connected");
            //               console.log('wifi connected!', c);
            //             },
            //             err => {
            //               console.log(
            //                 'Accounts / connectToSSID / status ===> Connection failed! ',
            //                 err,
            //               );
            //               console.log('Error ===> ', err);
            //               // ToastAndroid.show(err, ToastAndroid.LONG)
            //             },
            //           )
            //           .catch(e => {
            //             console.log('wifi error connecting!', e);
            //           });
            //       }, 500);
            //     })
            //     .catch(e => {
            //       console.log('wifi list error!', e);
            //     });
            // }
            // await WifiManager.reScanAndLoadWifiList().then((list) => {
            //   console.log("wifi list!", list);
            // });
            // await WifiManager.connectToProtectedSSID(
            //   'Bugaboo-HC',
            //   'ChillBaby',
            //   false,
            // )
            //   .then(
            //     c => {
            //       setTimeout(() => {
            //         ishow(true);

            //         // openInAppBrowser(
            //         //   "http://192.168.4.1/?userid=admin&pwd=admin"
            //         // );
            //       }, 2000);
            //       // Alert.alert(`wifi is in connected`);
            //       console.log('wifi connected!', c);
            //     },
            //     err => {
            //       console.log(
            //         'Accounts / connectToSSID / status ===> Connection failed! ',
            //         err,
            //       );
            //       console.log('Error ===> ', err);
            //       // ToastAndroid.show(err, ToastAndroid.LONG)
            //     },
            //   )
            //   .catch(e => {
            //     console.log('wifi error connecting!', e);
            //   });
            // WifiManager.getCurrentWifiSSID().then(
            //   ssid => {
            //     console.log('Your current connected wifi SSID is ' + ssid);
            //     if (ssid === 'Bugaboo-HC') {
            //       setTimeout(() => {
            //         ishow(true);
            //         // openInAppBrowser(
            //         //   "http://192.168.4.1/?userid=admin&pwd=admin"
            //         // );
            //       }, 5000);
            //     }
            //   },
            //   () => {
            //     console.log('Cannot get current SSID!');
            //   },
            // );
          }}>
          <Text>Install Updates</Text>
        </TouchableOpacity>
        <CButton
          title="webview"
          style={{
            margin: 50,
            borderWidth: 1,
            borderColor: BaseColor.blueDark,
            backgroundColor: BaseColor.whiteColor,
            elevation: 0,
            shadowOffset: {
              width: 0,
              height: 0,
            },
            shadowOpacity: 0.0,
          }}
          // style={[styles.BackBtn, { backgroundColor: BaseColor.whiteColor }]}
          onPress={() => {
            show ? navigation.navigate("OTAWeb") : console.log("nop----");
          }}
        />
        {ishow && (
          <View
            style={{
              height: Dimensions.get("window").height - 300,
              backgroundColor: BaseColor.primary,
            }}
          >
            <WebView
              source={{
                uri: "http://192.168.4.1/?userid=admin&pwd=admin",
              }}
              pullToRefreshEnabled
              onError={() => {
                ishow(true);
              }}
            />
          </View>
        )}
      </View>
    </>
  );
}

export default OTAUpdate;
