/* eslint-disable quotes */
/* eslint-disable no-unused-expressions */
import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  BackHandler,
  ActivityIndicator,
} from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import LinearGradient from 'react-native-linear-gradient';
import FIcon from 'react-native-vector-icons/Feather';
import Toast from 'react-native-simple-toast';
import {useDispatch, useSelector} from 'react-redux';
import {useTheme} from '@react-navigation/native';
import styles from './styles';
import CButton from '../../components/CButton';
import OtpComponent from '../../components/OtpComponent/index';
import {CustomIcon} from '../../config/LoadIcons';
import GradientBack from '../../components/gradientBack';
import {translate} from '../../lang/Translate';
import {getApiData} from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import AuthAction from '../../redux/reducers/auth/actions';
import {sendErrorReport} from '../../utils/commonFunction';
import BaseColors from '../../config/colors';

let backPressed = 0;

/**
 *
 *@module OTP
 *
 */
function Otp({route, navigation}) {
  const colors = useTheme();
  const BaseColor = colors.colors;

  const {setUserId, setAccessToken, setUserData} = AuthAction;
  const dispatch = useDispatch();

  const [loader, setloader] = useState(false);
  const [done, setdone] = useState(false);
  const [anim, setanim] = useState(false);
  const [backAnim, setBackAnim] = useState(false);

  const [numPad, setNumPad] = useState(false);
  const [code, setCode] = useState('');
  const {type, userId} = route.params;
  const [otpLoader, setOtpLoader] = useState(false);

  const user_id = useSelector(state => state.auth.user_id);

  const validation = () => {
    if (code == '') {
      // Toast.show("Enter One Time Password");
      Toast.show(translate('enterOTP'));
    } else {
      otpCheck();
    }
  };

  /** this function for OTP
   * @function otpCheck
   * @param {object} data user_id, otp_code
   */
  const otpCheck = () => {
    setloader(true);
    setanim(true);
    setBackAnim(false);
    const data = {
      user_id: userId || user_id,
      otp_code: Number(code),
    };

    getApiData(BaseSetting.endpoints.otp, 'POST', data)
      .then(response => {
        if (response.success) {
          if (type !== 'ForgotPassword') {
            dispatch(setUserData(response.data.user));
            dispatch(setUserId(response.data.user.id));
            dispatch(setAccessToken(response.data.token));
          }
          setTimeout(() => {
            setloader(false);
            setdone(true);
          }, 2000);
          setTimeout(() => {
            if (type === 'ForgotPassword') {
              navigation.push('UpdatePassword');
            } else if (type === 'Signup') {
              navigation.push('ChildInfo', {type: 'Otp'});
            } else if (type === 'LoginInactive') {
              navigation.push('Login');
            }
          }, 3000);
        } else {
          Toast.show(response.message);
          setanim(false);
          setBackAnim(true);
          setdone(false);
          setloader(false);
        }
      })
      .catch(err => {
        Toast.show('Something went wrong while verifying otp');
        sendErrorReport(err, 'verify_otp');
        setanim(false);
        setBackAnim(true);
        setdone(false);
        setloader(false);
        console.log('ERRR', err);
      });
  };

  // this function for resend otp
  /** this function for resend otp
   * @function resendOtp
   * @param {object} data user_id
   */
  async function resendOtp() {
    setOtpLoader(true);
    try {
      const response = await getApiData(BaseSetting.endpoints.sendOtp, 'POST', {
        user_id: userId || user_id,
        brand_name: 'Babyauto',
      });
      Toast.show(response.message);
      setOtpLoader(false);
    } catch (error) {
      console.log('resend otp error ===', error);
      sendErrorReport(error, 'resend_otp');
    }
  }

  function handleBackButtonClick() {
    if (backPressed > 0) {
      BackHandler.exitApp();
      backPressed = 0;
    } else {
      backPressed++;
      Toast.show('Press Again To Exit');
      setTimeout(() => {
        backPressed = 0;
      }, 2000);
      return true;
    }
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  return (
    <>
      <View style={styles.root}>
        {/* <GradientBack /> */}
        <View style={styles.mainContainer}>
          <View style={styles.loginTextView}>
            <View>
              <View
                style={[
                  styles.lockIconStyle,
                  {backgroundColor: BaseColor.whiteColor},
                ]}>
                <CustomIcon
                  name="Smartphone2"
                  color={BaseColors.IntroOrng}
                  size={45}
                />
              </View>
              <Text style={[styles.loginText, {color: BaseColors.blackColor}]}>
                {translate('otpScreen')}
              </Text>
            </View>
            <Text
              style={[styles.associatedTest, {color: BaseColors.blackColor}]}>
              {translate('otpText')}
            </Text>
          </View>
          <View style={styles.inputWrapper}>
            <OtpComponent
              code={code}
              onCodeFilled={cd => {
                // codeFilled("checkOtp", cd);
                console.log('checkOtp', cd);
              }}
              onCodeChanged={val => {}}
              onotpPress={() => {
                setNumPad(true);
              }}
            />
          </View>
          <View style={styles.inputWrapper}>
            <CButton
              style={styles.loginBtn}
              title={translate('otpBtn')}
              anim
              playAnimation={anim}
              backAnim={backAnim}
              onPress={() => {
                validation();
              }}
              loader={loader}
              done={done}
            />
          </View>
          <TouchableOpacity
            activeOpacity={0.8}
            style={styles.resendView}
            onPress={otpLoader ? null : resendOtp}>
            {otpLoader ? (
              <ActivityIndicator color={BaseColor.blueDark} />
            ) : (
              <>
                <CustomIcon
                  name="reload"
                  size={16}
                  color={BaseColors.blackColor}
                />
                <Text
                  style={[styles.resendText, {color: BaseColors.blackColor}]}>
                  {translate('otpResend')}
                </Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      </View>
      <Modal
        visible={numPad}
        onRequestClose={() => {
          setNumPad(false);
        }}
        animationType="slide"
        transparent>
        <TouchableOpacity
          style={{
            backgroundColor: 'transparent',
            flex: 1,
            justifyContent: 'flex-end',
          }}
          onPress={() => {
            setNumPad(false);
          }}>
          <LinearGradient
            start={{x: 0, y: 0}}
            end={{x: 0, y: 1}}
            colors={[BaseColor.whiteColor, BaseColor.whiteColor]}
            style={{
              paddingLeft: 15,
              paddingRight: 15,
              borderRadius: 5,
            }}>
            <View>
              <View
                style={{
                  flexDirection: 'row',
                }}>
                <TouchableOpacity
                  style={styles.numPad}
                  onPress={() => {
                    if (code.length !== 4) {
                      const pCode = code;
                      setCode(pCode + 1);
                    }
                  }}>
                  <Text
                    style={[styles.numTxtStyle, {color: BaseColor.blackColor}]}>
                    1
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.numPad}
                  onPress={() => {
                    if (code.length !== 4) {
                      const pCode = code;
                      setCode(pCode + 2);
                    }
                  }}>
                  <Text
                    style={[styles.numTxtStyle, {color: BaseColor.blackColor}]}>
                    2
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.numPad}
                  onPress={() => {
                    if (code.length !== 4) {
                      const pCode = code;
                      setCode(pCode + 3);
                    }
                  }}>
                  <Text
                    style={[styles.numTxtStyle, {color: BaseColor.blackColor}]}>
                    3
                  </Text>
                </TouchableOpacity>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                }}>
                <TouchableOpacity
                  style={styles.numPad}
                  onPress={() => {
                    if (code.length !== 4) {
                      const pCode = code;
                      setCode(pCode + 4);
                    }
                  }}>
                  <Text
                    style={[styles.numTxtStyle, {color: BaseColor.blackColor}]}>
                    4
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.numPad}
                  onPress={() => {
                    if (code.length !== 4) {
                      const pCode = code;
                      setCode(pCode + 5);
                    }
                  }}>
                  <Text
                    style={[styles.numTxtStyle, {color: BaseColor.blackColor}]}>
                    5
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.numPad}
                  onPress={() => {
                    if (code.length !== 4) {
                      const pCode = code;
                      setCode(pCode + 6);
                    }
                  }}>
                  <Text
                    style={[styles.numTxtStyle, {color: BaseColor.blackColor}]}>
                    6
                  </Text>
                </TouchableOpacity>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                }}>
                <TouchableOpacity
                  style={styles.numPad}
                  onPress={() => {
                    if (code.length !== 4) {
                      const pCode = code;
                      setCode(pCode + 7);
                    }
                  }}>
                  <Text
                    style={[styles.numTxtStyle, {color: BaseColor.blackColor}]}>
                    7
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.numPad}
                  onPress={() => {
                    if (code.length !== 4) {
                      const pCode = code;
                      setCode(pCode + 8);
                    }
                  }}>
                  <Text
                    style={[styles.numTxtStyle, {color: BaseColor.blackColor}]}>
                    8
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.numPad}
                  onPress={() => {
                    if (code.length !== 4) {
                      const pCode = code;
                      setCode(pCode + 9);
                    }
                  }}>
                  <Text
                    style={[styles.numTxtStyle, {color: BaseColor.blackColor}]}>
                    9
                  </Text>
                </TouchableOpacity>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                }}>
                <TouchableOpacity style={styles.numPad}>
                  <Text
                    style={[styles.numTxtStyle, {color: BaseColor.blackColor}]}
                  />
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.numPad}
                  onPress={() => {
                    if (code.length !== 4) {
                      const pCode = code;
                      setCode(pCode + 0);
                    }
                  }}>
                  <Text
                    style={[styles.numTxtStyle, {color: BaseColor.blackColor}]}>
                    0
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.numPad}
                  onPress={() => {
                    const pCode = code;
                    const nCode = pCode.slice(0, -1);
                    setCode(nCode);
                  }}>
                  <FIcon name="delete" size={24} color={BaseColor.blackColor} />
                </TouchableOpacity>
              </View>
            </View>
          </LinearGradient>
        </TouchableOpacity>

        {/* <View
          style={{
            backgroundColor: "transparent",
            flex: 1,
            justifyContent: "flex-end",
          }}
        >
          <View>
            <View
              style={{
                backgroundColor: BaseColor.blueDark,
                flexDirection: "row",
              }}
            >
              <TouchableOpacity style={styles.numPad}>
                <Text style={styles.numTxtStyle}>1</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.numPad}>
                <Text style={styles.numTxtStyle}>2</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.numPad}>
                <Text style={styles.numTxtStyle}>3</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View> */}
      </Modal>

      <CButton
        iconname="left-arrow"
        iconsize={20}
        iconColor={BaseColor.blackColor}
        style={[styles.BackBtn, {backgroundColor: BaseColor.whiteColor}]}
        onPress={() => {
          navigation.navigate('RedirectLS');
        }}
      />
    </>
  );
}

export default Otp;
