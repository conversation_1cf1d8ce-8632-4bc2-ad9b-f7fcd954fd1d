import { Dimensions, StyleSheet } from 'react-native';
import BaseColor from '../../config/colors';
import { FontFamily } from '../../config/typography';

const { height: dHeight, width: dWidth } = Dimensions.get('window');

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: BaseColor.whiteColor
  },
  mainContainer: {
    flex: 1,
    // backgroundColor: BaseColor.blueDark,
    // justifyContent: 'center',
    marginVertical: 60
  },
  closeBtn: {
    height: 40,
    width: 40,
    borderRadius: 20,
    alignSelf: 'flex-end',
  },
  loginBtn: {
    alignSelf: 'center',
    fontFamily: FontFamily.default,
    borderColor: BaseColor.blackColor,
    borderWidth: 1
  },
  mainInputStyle: {
    // flex: 1,
    justifyContent: 'center',
  },
  inputWrapper: {
    marginVertical: 7,
    marginHorizontal: 40,
  },
  loginText: {
    fontSize: 20,
    color: BaseColor.whiteColor,
    // fontWeight: '700',
    letterSpacing: 0.8,
    fontFamily: FontFamily.default,
  },
  loginTextView: {
    marginTop: 40,
    marginBottom: 20,
    alignItems: 'center',
    fontFamily: FontFamily.default,
  },
  associatedTest: {
    fontFamily: FontFamily.default,
    color: BaseColor.whiteColor,
    fontSize: 16,
    textAlign: 'center',
    marginHorizontal: 42,
  },
  lockIconStyle: {
    backgroundColor: BaseColor.whiteColor,
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    width: 80,
    height: 80,
    borderRadius: 40,
    elevation: 5,
    marginBottom: 20,
  },
  resendView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: 100,
    alignSelf: 'center',
    marginVertical: 20,
  },
  resendText: {
    fontSize: 16,
    fontFamily: FontFamily.default,
    color: BaseColor.whiteColor,
    fontWeight: 'bold',
    paddingHorizontal: 8,
  },
  numPad: {
    flex: 1,
    // justifyContent: 'center',
    alignItems: 'center',
    height: 60,
    marginVertical: 5
  },
  numTxtStyle: {
    color: BaseColor.blackColor,
    fontFamily: FontFamily.default,
    fontSize: 24
  },
  closeBtn1: {
    height: 40,
    width: 40,
    borderRadius: 20,
    alignSelf: 'flex-end',
    position: 'absolute',
    borderColor: BaseColor.textGrey,
    borderWidth: 0.5,
    right: 12,
    top: 34,
  },
  BackBtn: {
    height: 40,
    width: 40,

    alignSelf: 'flex-end',
    position: 'absolute',
    left: 12,
    top: 34,
  },
});

export default styles;
