/* eslint-disable quotes */
import React, {useEffect, useState} from 'react';
import {FlatList, View, BackHandler, Text} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
} from 'react-native-reanimated';
import Toast from 'react-native-simple-toast';
import {useSelector, useDispatch} from 'react-redux';
import styles from './styles';
import CHeader from '../../components/CHeader';
import DeviceList from '../../components/ConDeviceList';
import GradientBack from '../../components/gradientBack';
import {translate} from '../../lang/Translate';
import CButton from '../../components/CButton';
import BaseColor from '../../config/colors';
import {getApiData} from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import {sendErrorReport} from '../../utils/commonFunction';
import BluetoothAction from '../../redux/reducers/bluetooth/actions';

const CDeviceList = ({navigation, route}) => {
  const {
    params: {deviceDetail, title, item},
  } = route;
  const listAnim = useSharedValue(0);
  const opacityAnim = useSharedValue(0);
  const [btnLoad, setBtnLoad] = useState('');
  const token = useSelector(state => state.auth.accessToken);
  const dispatch = useDispatch();
  const listStyleAnim = useAnimatedStyle(() => ({
    transform: [
      {
        translateY: withTiming(listAnim.value, {
          duration: 1000,
        }),
      },
    ],
    opacity: withTiming(opacityAnim.value, {
      duration: 1000,
    }),
  }));

  useEffect(() => {
    opacityAnim.value = 1;
    listAnim.value = -300;
  }, []);

  const render = ({item}) => (
    <DeviceList
      img={item.device_image}
      title={item.device_name}
      bluetoothName={item.device_bluetooth_name}
      text={item.text}
      onEditPress={() => {
        navigation.navigate('EditDevice', {deviceDetail: item});
      }}
    />
  );

  function handleBackButtonClick() {
    navigation.goBack();
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  async function deleteDevice() {
    const headers = {
      'Content-Type': 'application/json',
      authorization: token ? `Bearer ${token}` : '',
    };

    const data = {
      child_id: item?.id,
    };

    setBtnLoad('delete');

    try {
      const response = await getApiData(
        BaseSetting.endpoints.removeChild,
        'POST',
        data,
        headers,
      );

      Toast.show(translate(response.message));
      setBtnLoad('');
      dispatch(BluetoothAction.setDeviceID(''));
      dispatch(BluetoothAction.setDeletedRecent(true));
      dispatch(BluetoothAction.setActiveDeviceId({}));
      dispatch(BluetoothAction.setConnectedDeviceDetails([]));
      navigation.navigate('DrawerNav');
    } catch (error) {
      console.log('error ===', error);
      sendErrorReport(error, 'delete_device');
    }
  }

  return (
    <View style={styles.root}>
      {/* <GradientBack /> */}
      <CHeader
        title={title || ''}
        backBtn
        leftIconName
        onLeftPress={() => {
          navigation.goBack();
        }}
      />
      <Animated.View style={[styles.flatlistView, {top: 300}, listStyleAnim]}>
        <FlatList
          data={deviceDetail || []}
          renderItem={render}
          keyExtractor={item => {
            item.id;
          }}
          bounces={false}
          showsVerticalScrollIndicator={false}
          ListFooterComponent={() => {
            if (deviceDetail.length === 0) {
              return (
                <View style={styles.emptyView}>
                  <Text style={styles.emptyDataText}>
                    {translate('noDevice')}
                  </Text>
                </View>
              );
            }
            return null;
          }}
        />
        <View
          style={{flex: 1, justifyContent: 'flex-end', marginHorizontal: 16}}>
          <View style={{flexDirection: 'row', marginBottom: 16}}>
            <CButton
              title={translate('editProfile')}
              style={{
                backgroundColor: BaseColor.whiteColor,
                borderRadius: 8,
                marginTop: 16,
                marginEnd: 4,
                flex: 1,
                borderColor: BaseColor.textGrey,
                borderWidth: 0.5,
              }}
              titleStyle={{
                color: BaseColor.blueDark,
                fontWeight: 'bold',
              }}
              loader={btnLoad === 'edit'}
              onPress={() => {
                // editDevice();
                navigation.navigate('ChildInfo', {
                  type: 'edit',
                  item: route.params.item,
                });
              }}
            />
            <CButton
              title={translate('deleteProfile')}
              style={{
                backgroundColor: BaseColor.alertRed,
                borderRadius: 8,
                marginTop: 16,
                marginStart: 4,
                flex: 1,
              }}
              titleStyle={{
                color: BaseColor.whiteColor,
                fontWeight: 'bold',
              }}
              loader={btnLoad === 'delete'}
              onPress={() => {
                deleteDevice();
              }}
            />
          </View>
        </View>
      </Animated.View>
    </View>
  );
};

export default CDeviceList;
