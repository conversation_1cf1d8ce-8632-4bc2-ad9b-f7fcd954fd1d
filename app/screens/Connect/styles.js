import { StyleSheet } from 'react-native';
import BaseColor from '../../config/colors';
import { FontFamily } from '../../config/typography';

const styles = StyleSheet.create({
  root: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: BaseColor.whiteColor
  },
  imageLastShadow: {
    marginTop: 20,
    backgroundColor: BaseColor.white20,
    justifyContent: 'center',
    width: 250,
    height: 250,
    alignSelf: 'center',
    borderRadius: 200,
  },
  imagemiddleShadow: {
    backgroundColor: BaseColor.transparentWhite,
    justifyContent: 'center',
    width: 200,
    height: 200,
    alignSelf: 'center',
    borderRadius: 100,
  },
  imageView: {
    backgroundColor: BaseColor.whiteColor,
    width: 200,
    height: 200,
    alignSelf: 'center',
    justifyContent: 'center',
    borderRadius: 125,
    borderColor: BaseColor.textGrey,
    borderWidth: 0.5
  },
  deviceIcon: {
    textAlign: 'center',
    textAlignVertical: 'center',
  },
  connectText: {
    textAlign: 'center',
    fontSize: 28,
    paddingVertical: 12,
    color: BaseColor.whiteColor,
    fontWeight: 'bold',
  },
  otherText: {
    textAlign: 'center',
    fontSize: 15,
    color: BaseColor.whiteColor,
    fontFamily: FontFamily.default,
  },
  checkIconView: {
    marginTop: 34,
    width: 60,
    height: 60,
    alignSelf: 'center',
    justifyContent: 'center',
    borderRadius: 25,
    backgroundColor: BaseColor.whiteColor,
  },
  checkIcon: {
    textAlign: 'center',
    textAlignVertical: 'center',
  },
});

export default styles;
