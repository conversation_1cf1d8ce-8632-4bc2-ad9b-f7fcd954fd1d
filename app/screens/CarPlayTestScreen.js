/**
 * CarPlay Test Screen
 * Test screen to verify CarPlay functionality and status
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
} from 'react-native';
import CarPlayService from '../services/CarPlayService';
import CarPlayStatusComponent from '../components/CarPlayStatusComponent';

const CarPlayTestScreen = () => {
  const [logs, setLogs] = useState([]);
  const [carPlayStatus, setCarPlayStatus] = useState({
    isConnected: false,
    isForeground: false,
    hasActiveTemplate: false,
  });

  useEffect(() => {
    // Update status every 2 seconds
    const interval = setInterval(() => {
      const status = CarPlayService.getCarPlayStatus();
      setCarPlayStatus(status);
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const addLog = (message) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev.slice(-9), `${timestamp}: ${message}`]);
  };

  const testCarPlayConnection = () => {
    const status = CarPlayService.getCarPlayStatus();
    addLog(`CarPlay Status - Connected: ${status.isConnected}, Foreground: ${status.isForeground}`);
  };

  const testTemperatureAlert = () => {
    addLog('Sending temperature alert to CarPlay...');
    CarPlayService.showBabyAlert({
      type: 'temperature',
      temperature: 78,
      humidity: 65,
      timestamp: new Date().toISOString(),
    });
  };

  const testMotionAlert = () => {
    addLog('Sending motion alert to CarPlay...');
    CarPlayService.showBabyAlert({
      type: 'motion',
      temperature: 72,
      humidity: 60,
      timestamp: new Date().toISOString(),
    });
  };

  const testSoundAlert = () => {
    addLog('Sending sound alert to CarPlay...');
    CarPlayService.showBabyAlert({
      type: 'sound',
      temperature: 74,
      humidity: 62,
      timestamp: new Date().toISOString(),
    });
  };

  const testNotification = () => {
    addLog('Sending notification to CarPlay...');
    CarPlayService.showNotification({
      title: 'Test Notification',
      message: 'This is a test notification from the app',
      type: 'info',
    });
  };

  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <Text style={styles.title}>CarPlay Test Screen</Text>
        
        {/* Status Component */}
        <CarPlayStatusComponent />
        
        {/* Test Buttons */}
        <View style={styles.buttonSection}>
          <Text style={styles.sectionTitle}>Test Functions</Text>
          
          <TouchableOpacity style={styles.button} onPress={testCarPlayConnection}>
            <Text style={styles.buttonText}>Check CarPlay Status</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.button, styles.alertButton]} 
            onPress={testTemperatureAlert}
          >
            <Text style={styles.buttonText}>Test Temperature Alert</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.button, styles.alertButton]} 
            onPress={testMotionAlert}
          >
            <Text style={styles.buttonText}>Test Motion Alert</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.button, styles.alertButton]} 
            onPress={testSoundAlert}
          >
            <Text style={styles.buttonText}>Test Sound Alert</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.button, styles.notificationButton]} 
            onPress={testNotification}
          >
            <Text style={styles.buttonText}>Test Notification</Text>
          </TouchableOpacity>
        </View>
        
        {/* Logs Section */}
        <View style={styles.logsSection}>
          <View style={styles.logsHeader}>
            <Text style={styles.sectionTitle}>Activity Logs</Text>
            <TouchableOpacity style={styles.clearButton} onPress={clearLogs}>
              <Text style={styles.clearButtonText}>Clear</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.logsContainer}>
            {logs.length === 0 ? (
              <Text style={styles.noLogs}>No activity yet...</Text>
            ) : (
              logs.map((log, index) => (
                <Text key={index} style={styles.logText}>
                  {log}
                </Text>
              ))
            )}
          </View>
        </View>
        
        {/* Instructions */}
        <View style={styles.instructionsSection}>
          <Text style={styles.sectionTitle}>Instructions</Text>
          <Text style={styles.instructionText}>
            1. Open CarPlay Simulator{'\n'}
            2. Connect to your app{'\n'}
            3. Test the buttons above{'\n'}
            4. Check the status and logs{'\n'}
            5. Switch between apps to test foreground/background detection
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginVertical: 20,
    color: '#333',
  },
  buttonSection: {
    margin: 15,
    padding: 15,
    backgroundColor: 'white',
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  button: {
    backgroundColor: '#2196F3',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginVertical: 5,
  },
  alertButton: {
    backgroundColor: '#FF5722',
  },
  notificationButton: {
    backgroundColor: '#4CAF50',
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontWeight: 'bold',
    fontSize: 16,
  },
  logsSection: {
    margin: 15,
    padding: 15,
    backgroundColor: 'white',
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  logsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  clearButton: {
    backgroundColor: '#FF9800',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  clearButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 12,
  },
  logsContainer: {
    backgroundColor: '#f8f8f8',
    padding: 10,
    borderRadius: 5,
    minHeight: 100,
    maxHeight: 200,
  },
  noLogs: {
    textAlign: 'center',
    color: '#999',
    fontStyle: 'italic',
  },
  logText: {
    fontSize: 12,
    color: '#333',
    marginVertical: 2,
    fontFamily: 'monospace',
  },
  instructionsSection: {
    margin: 15,
    padding: 15,
    backgroundColor: 'white',
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  instructionText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
});

export default CarPlayTestScreen;
