import React, {useEffect, useState} from 'react';
import {
  Text,
  View,
  StatusBar,
  TouchableOpacity,
  SafeAreaView,
  BackHandler,
} from 'react-native';
import {useSelector} from 'react-redux';
import Toast from 'react-native-simple-toast';
import GradientBack from '../../components/gradientBack';
import BaseColor from '../../config/colors';
import styles from './styles';
import {CustomIcon} from '../../config/LoadIcons';
import CHeader from '../../components/CHeader';
import {translate} from '../../lang/Translate';
import {getApiData} from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import {sendErrorReport} from '../../utils/commonFunction';

let backPressed = 0;

/**
 *
 *@module Gender
 *
 */
const Gender = ({navigation}) => {
  const [gender, setGender] = useState(true);

  const user_id = useSelector(state => state.auth.user_id);
  const {accessToken} = useSelector(state => state.auth);

  function handleBackButtonClick() {
    if (backPressed > 0) {
      BackHandler.exitApp();
      backPressed = 0;
    } else {
      backPressed++;
      Toast.show('Press Again To Exit');
      setTimeout(() => {
        backPressed = 0;
      }, 2000);
      return true;
    }
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  /** this function for child gender
   * @function userGender
   * @param {object} data gender
   */
  const userGender = () => {
    const data = {
      gender: gender ? 'Male' : 'Female',
    };

    const headers = {
      'Content-Type': 'application/json',
      authorization: accessToken ? `Bearer ${accessToken}` : '',
    };

    getApiData(BaseSetting.endpoints.gender, 'POST', data, headers)
      .then(response => {
        if (response.success) {
          navigation.navigate('QRScanner');
        } else {
          // Toast.show(response.message);
        }
      })
      .catch(err => {
        Toast.show('Something went wrong while updating user details');
        sendErrorReport(err, 'gender_update');
        console.log('ERRR', err);
      });
  };

  return (
    <View style={styles.root}>
      <GradientBack />
      <StatusBar
        backgroundColor={BaseColor.blueLight}
        barStyle="dark-content"
      />
      <CHeader
        title={translate('genderScreen')}
        rightIconName="check"
        onRightPress={() => userGender()}
      />

      <View style={styles.genderView}>
        <TouchableOpacity
          activeOpacity={1}
          onPress={() => {
            setGender(false);
          }}
          style={{
            ...styles.selectGender,
            backgroundColor: !gender ? BaseColor.whiteColor : 'transparent',
          }}>
          <View style={styles.iconView}>
            <View
              style={{
                ...styles.genderIcon,
                backgroundColor: !gender
                  ? BaseColor.blueLight
                  : BaseColor.transparentWhite,
              }}>
              <CustomIcon name="woman" size={60} color={BaseColor.whiteColor} />
            </View>

            {!gender ? (
              <View
                style={{
                  ...styles.selectedCheck,
                  backgroundColor: BaseColor.blueLight,
                }}>
                <CustomIcon
                  name="check"
                  size={16}
                  color={BaseColor.whiteColor}
                />
              </View>
            ) : null}
          </View>

          <Text
            style={{
              ...styles.genderName,
              color: !gender ? BaseColor.blueLight : BaseColor.whiteColor,
            }}>
            {translate('genderFemale')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          activeOpacity={1}
          onPress={() => {
            setGender(true);
          }}
          style={{
            ...styles.selectGender,
            backgroundColor: gender ? BaseColor.whiteColor : 'transparent',
          }}>
          <View style={styles.iconView}>
            <View
              style={{
                ...styles.genderIcon,
                backgroundColor: gender
                  ? BaseColor.blueLight
                  : BaseColor.transparentWhite,
              }}>
              <CustomIcon
                name="avatar"
                size={60}
                color={BaseColor.whiteColor}
              />
            </View>

            {gender ? (
              <View
                style={{
                  ...styles.selectedCheck,
                  backgroundColor: BaseColor.blueLight,
                }}>
                <CustomIcon
                  name="check"
                  size={16}
                  color={BaseColor.whiteColor}
                />
              </View>
            ) : null}
          </View>

          <Text
            style={{
              ...styles.genderName,
              color: gender ? BaseColor.blueLight : BaseColor.whiteColor,
            }}>
            {translate('genderMale')}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default Gender;
