import { Dimensions, StyleSheet } from 'react-native';
import BaseColor from '../../config/colors';
import { FontFamily } from '../../config/typography';

const styles = StyleSheet.create({
  root: {
    flex: 1,
  },
  headerView: {
    paddingHorizontal: 18,
    paddingTop: 34,
    paddingBottom: 16,
  },
  headerTitle: {
    fontSize: 20,
    color: BaseColor.whiteColor,
    fontFamily: FontFamily.default,
    alignSelf: 'center',
    fontWeight: 'bold',
  },
  genderView: {
    flex: 1,
    marginTop: 10,
  },
  selectGender: {
    flex: 1,
    alignSelf: 'center',
    justifyContent: 'center',
    borderTopRightRadius: 50,
    width: Dimensions.get('screen').width,
    height: Dimensions.get('screen').width / 1.4,
    backgroundColor: 'red'
  },
  iconView: {
    width: 150,
    height: 150,
    alignSelf: 'center',
    borderRadius: 150,
    justifyContent: 'center',
    alignItems: 'center',
  },
  genderIcon: {
    width: 150,
    height: 150,
    textAlign: 'center',
    alignSelf: 'center',
    textAlignVertical: 'center',
    borderRadius: 75,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedCheck: {
    width: 34,
    height: 34,
    position: 'absolute',
    bottom: 6,
    right: 10,
    borderColor: BaseColor.whiteColor,
    borderWidth: 3,
    textAlign: 'center',
    alignSelf: 'center',
    textAlignVertical: 'center',
    borderRadius: 75,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10
  },
  genderName: {
    fontSize: 20,
    textAlign: 'center',
    paddingVertical: 14,
    fontFamily: FontFamily.default,
  },
});

export default styles;
