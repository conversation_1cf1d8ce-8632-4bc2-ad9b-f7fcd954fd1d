/* eslint-disable max-len */
/* eslint-disable quotes */
import React, { useEffect, useRef, useState } from 'react';
import {
  Text,
  View,
  Image,
  TouchableOpacity,
  Modal,
  ScrollView,
  SafeAreaView,
  BackHandler,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import CountryPicker from 'react-native-country-picker-modal';
import LinearGradient from 'react-native-linear-gradient';
import FAIcon from 'react-native-vector-icons/FontAwesome';
import Toast from 'react-native-simple-toast';
import { useDispatch, useSelector } from 'react-redux';
import {
  findIndex,
  isArray,
  isEmpty,
  isNaN,
  isNumber,
  isObject,
  isSymbol,
} from 'lodash';
import { useTheme } from '@react-navigation/native';
import GetLocation from 'react-native-get-location';
import DeviceInfo from 'react-native-device-info';
import CButton from '../../components/CButton';
import CInput from '../../components/CInput';
import GradientBack from '../../components/gradientBack';
import styles from './styles';
import { FontFamily } from '../../config/typography';
import { getApiData } from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import {
  enableAnimateInEaseOut,
  sendErrorReport,
} from '../../utils/commonFunction';
import AuthAction from '../../redux/reducers/auth/actions';
import { translate, initTranslate } from '../../lang/Translate';
import DropDown from '../../components/DropDown';
import BaseColors from '../../config/colors';
import { store } from '../../redux/store/configureStore';
import languageActions from '../../redux/reducers/language/actions';

/**
 *
 * @module SignUp
 */
const Signup = ({ navigation }) => {
  const colors = useTheme();
  const BaseColor = colors.colors;

  const brandToken = useSelector(state => state.auth.brandToken);
  const langList = useSelector(state => state.auth.langList);
  console.log('🚀 ~ file: index.js ~ line 54 ~ Signup ~ langList', langList);

  const [loader, setloader] = useState(false);
  const [done, setdone] = useState(false);
  const [anim, setanim] = useState(false);
  const [backAnim, setBackAnim] = useState(false);

  const [nameError, setNameError] = useState(false);
  const [showCheckList, setShowCheckList] = useState(false);
  const [isUpperCase, setIsUpperCase] = useState(false);
  const [isLowerCase, setIsLowerCase] = useState(false);
  const [isNu, setIsNumber] = useState(false);
  const [isSpecial, setIsSp] = useState(false);
  const [isTwelve, setIsTwelve] = useState(false);
  const [passwordError, setPasswordError] = useState(false);
  const [mailError, setMailError] = useState(false);
  const [numError, setNumError] = useState(false);
  const [langError, setLangError] = useState(false);

  const [nameErrorTxt, setnameErrorTxt] = useState('');
  const [passwordErrorTxt, setPasswordErrorTxt] = useState('');
  const [mailErrorTxt, setMailErrorTxt] = useState('');
  const [numErrorTxt, setNumErrorTxt] = useState('');
  const [langTxt, setLangTxt] = useState('');

  const fNameRef = useRef();
  const passwordRef = useRef();
  const emailRef = useRef();
  const pNumRef = useRef();
  const [hideShow, setHideShow] = useState(true);
  const dispatch = useDispatch();
  const { setLanguage } = languageActions;
  const { setUserId, setIsFarenheit } = AuthAction;
  const [state, setstate] = useState({
    fullName: '',
    password: '',
    email: '',
    pNum: '',
    country: '',
    selectedCountry: 'ES',
    selectedCountryName: 'Spain',
    countryCode: '34',
    agree: false,
    selLang: langList[0],
    lat: null,
    lng: null,
    state: '',
  });

  useEffect(() => {
    getLocation();
  }, []);

  /** this function for get location
   * @function getLocation
   * @param {*} data {}
   */
  const getLocation = () => {
    const myApiKey = 'AIzaSyCFPe5S9WU6oDJtC6RgM1iVcZQKakGRJkA';
    console.log('called----2');
    GetLocation.getCurrentPosition({
      enableHighAccuracy: true,
      timeout: 15000,
      maximumAge: 10000,
    })
      .then(location => {
        console.log('Location====>>>>', location);

        fetch(
          `https://maps.googleapis.com/maps/api/geocode/json?address=${location.latitude},${location.longitude}&key=${myApiKey}`,
        )
          .then(response => response.json())
          .then(responseJson => {
            console.log(
              `ADDRESS GEOCODE is BACK!! => ${JSON.stringify(responseJson)}`,
            );
            let stateV = '';
            const { address_components } = responseJson.results[0];
            console.log(address_components);
            address_components.map(item => {
              if (item.types[0] === 'administrative_area_level_1') {
                stateV = item.long_name;
              }
            });
            console.log(
              'chillbaby ~ file: index.js ~ line 105 ~ .then ~ state',
              stateV,
            );
            setstate({
              ...state,
              lat: location.latitude,
              lng: location.longitude,
              state: stateV,
            });
          });
      })
      .catch(error => {
        const { code, message } = error;
        console.warn(code, message);
        sendErrorReport(error, 'get_location');
      });
  };

  function handleBackButtonClick() {
    navigation.navigate('RedirectLS');
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  const Validation = () => {
    const passVal =
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^a-zA-Z\d])[A-Za-z\d\S]{8,15}$/;
    const numVal = /^[0-9]+$/;
    const emailVal =
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

    enableAnimateInEaseOut();

    if (state.fullName === '') {
      allErrorFalse();
      setNameError(true);
      // setnameErrorTxt("Please enter FullName");
      setnameErrorTxt(translate('enterName'));
    } else if (state.password === '') {
      allErrorFalse();
      setPasswordError(true);
      // setPasswordErrorTxt("Please enter Password");
      setPasswordErrorTxt(translate('enterPasswrd'));
    } else if (!passVal.test(String(state.password))) {
      allErrorFalse();
      setPasswordError(true);
      // setPasswordErrorTxt(
      //   "Password must contain 8-15 characters, 1 x Upper case, 1 x Lower case, 1 x number and 1 x special character such as !,?,&"
      // );
      setPasswordErrorTxt(translate('passwrdValid'));
    } else if (state.email === '') {
      allErrorFalse();
      setMailError(true);
      // setMailErrorTxt("Please enter email");
      setMailErrorTxt(translate('enterEmail'));
    } else if (!emailVal.test(String(state.email))) {
      allErrorFalse();
      setMailError(true);
      // setMailErrorTxt("Please enter valid Email");
      setMailErrorTxt(translate('enterEmailvalid'));
    } else if (state.pNum === '') {
      allErrorFalse();
      setNumError(true);
      // setNumErrorTxt("Please enter Phone number");
      setNumErrorTxt(translate('enterPhone'));
    } else if (
      !numVal.test(String(state.pNum)) ||
      state.pNum.length < 6 ||
      state.pNum.length > 12
    ) {
      allErrorFalse();
      setNumError(true);
      // setNumErrorTxt("Please enter valid Phone number");
      setNumErrorTxt(translate('enterPhonevalid'));
    } else if (!state.agree) {
      allErrorFalse();
      // Toast.show("Please accept Terms and Conditions", Toast.SHORT, [
      //   "UIAlertController",
      // ]);
      Toast.show(translate('accept'), Toast.SHORT, ['UIAlertController']);
    } else {
      allErrorFalse();
      userSignUp();
    }
  };

  const allErrorFalse = () => {
    setNameError(false);
    setNumError(false);
    setPasswordError(false);
    setMailError(false);
  };

  /** this function for user SignUp
   * @function userSignUp
   * @param {object} data full_name, password, email, phone, phone_code, country, token, language_id, latitude, longitude, state
   */
  const userSignUp = () => {
    setloader(true);
    setanim(true);
    setBackAnim(false);
    const data = {
      full_name: state.fullName,
      password: state.password,
      email: state.email,
      phone: state.pNum,
      phone_code: `+${state.countryCode}`,
      country: state.selectedCountryName,
      country_code: state.selectedCountry,
      token: brandToken,
      language_id: state.selLang.id,
      latitude: state.lat,
      longitude: state.lng,
      state: state.state,
      brand_name: 'Babyauto',
      app_name: 'babyauto',
    };

    getApiData(BaseSetting.endpoints.signUp, 'POST', data)
      .then(response => {
        const uId =
          response && isObject(response.data) && response.data.id
            ? response.data.id
            : null;
        dispatch(setUserId(uId));
        if (response.success) {
          dispatch(setIsFarenheit(false));
          const langIndex = findIndex(
            langList,
            i => i.id === response?.data?.language_id,
          );
          setTimeout(() => {
            if (langIndex > -1) {
              dispatch(
                setLanguage(
                  langList[langIndex]?.lang_code,
                  langList[langIndex]?.lang_name,
                ),
              );
              setTimeout(() => {
                initTranslate(store, false);
              }, 100);
            }
            setloader(false);
            setdone(true);
          }, 2000);
          setTimeout(() => {
            navigation.navigate('Otp', { type: 'Signup' });
          }, 3000);
          // Toast.shokenw(response.message);
        } else {
          Toast.show(response.message?.message || response.message);
          setanim(false);
          setBackAnim(true);
          setdone(false);
          setloader(false);
        }
      })
      .catch(err => {
        Toast.show('Something went wrong while signup');
        setanim(false);
        setBackAnim(true);
        setdone(false);
        setloader(false);
        console.log('ERR====>>>>', err);
        sendErrorReport(err, 'sign_up');
      });
  };
  function isUpper(str) {
    return /^(?=.*?[A-Z])/.test(str);
  }
  function isLower(str) {
    return /^(?=.*?[a-z])/.test(str);
  }
  function isNumericCheck(str) {
    return /^(?=.*?[0-9])/.test(str);
  }
  function isSpCheck(str) {
    return /[ `!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/.test(str);
  }
  function isTw(str) {
    if (str.length >= 12) {
      return true;
    } else {
      return false;
    }
  }

  //   function validateStructure(value){
  //     let  pattern = r'^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[!@#\$&*~]).{8,}$';
  //     RegExp regExp = new RegExp(pattern);
  //     return regExp.hasMatch(value);
  // }

  return (
    <>
      <View style={styles.root}>
        {/* <GradientBack /> */}
        <KeyboardAvoidingView
          behavior={Platform.OS == 'ios' ? 'padding' : null}
          showsVerticalScrollIndicator={false}
          style={styles.mainContainer}>
          <ScrollView
            contentContainerStyle={{ flexGrow: 1, padding: 24 }}
            showsVerticalScrollIndicator={false}
            bounces={false}>
            <View style={{ justifyContent: 'center', flex: 1 }}>
              <View style={styles.mainInputStyle}>
                <View style={styles.logoImg}>
                  <Image
                    source={require('../../assets/images/logo.png')}
                    style={{ height: '100%', width: '100%' }}
                  />
                </View>
                <View style={styles.loginTextView}>
                  <Text
                    style={[styles.loginText, { color: BaseColor.blackColor }]}>
                    {translate('signup')}
                  </Text>
                </View>
                <CInput
                  ref={fNameRef}
                  placeholder={translate('fullName')}
                  value={state.fullName}
                  onChangeText={val => {
                    setstate({ ...state, fullName: val });
                  }}
                  placeholderTextColor={BaseColors.textGrey}
                  iconName="user2"
                  onSubmitEditing={() => {
                    passwordRef.current.focus();
                  }}
                  showError={nameError}
                  errorMsg={nameErrorTxt}
                  rightIcon
                />
                <CInput
                  ref={passwordRef}
                  placeholder={translate('loginPassword')}
                  value={state.password}
                  onChangeText={val => {
                    setstate({ ...state, password: val });
                    const lower = isLower(val);
                    if (lower) {
                      setIsLowerCase(true);
                    } else {
                      setIsLowerCase(false);
                    }
                    const upper = isUpper(val);
                    if (upper) {
                      setIsUpperCase(true);
                    } else {
                      setIsUpperCase(false);
                    }
                    const num = isNumericCheck(val);
                    if (num) {
                      setIsNumber(true);
                    } else {
                      setIsNumber(false);
                    }

                    const sp = isSpCheck(val);
                    if (sp) {
                      setIsSp(true);
                    } else {
                      setIsSp(false);
                    }
                    const tw = isTw(val);
                    if (tw) {
                      setIsTwelve(true);
                    } else {
                      setIsTwelve(false);
                    }

                    if (val.length <= 0) {
                      setIsUpperCase(false);
                      setIsLowerCase(false);
                      setIsNumber(false);
                      setIsTwelve(false);
                      setIsSp(false);
                    }
                    if (
                      isUpper(val) &&
                      isLower(val) &&
                      isNumericCheck(val) &&
                      isSpCheck(val) &&
                      isTw(val)
                    ) {
                      console.log('treuuuuu');
                      setShowCheckList(false);
                    } else {
                      setShowCheckList(true);
                    }
                  }}
                  onFocus={() => {
                    console.log('on focus');
                    setShowCheckList(true);
                  }}
                  onBlur={() => {
                    console.log(' on blue');
                    setShowCheckList(false);
                  }}
                  placeholderTextColor={BaseColors.textGrey}
                  // leftIconName="lock2"
                  textInputWrapper={{
                    marginTop: 12,
                  }}
                  onSubmitEditing={() => {
                    emailRef.current.focus();
                  }}
                  secureTextEntry={hideShow}
                  onShowPasswordpress={() => {
                    setHideShow(!hideShow);
                  }}
                  // hideLeftIcon
                  // rightIcon={false}
                  // iconName={hideShow ? 'eye-slash' : 'eye'}
                  // hideLeftIcon
                  iconName="lock2"
                  rightIcon={true}
                  showRightIcon={true}
                  rightIconName={hideShow ? 'eye-slash' : 'eye'}
                  showError={passwordError}
                  errorMsg={passwordErrorTxt}
                />
                {showCheckList && (
                  <View
                    style={{
                      marginHorizontal: 10,
                      padding: 20,
                      borderRadius: 10,
                      shadowColor: 'purple',
                      height: 150,
                      width: '90%',
                      backgroundColor: 'white',
                      justifyContent: 'center',
                      elevation: 10,
                      shadowOffset: { width: 1, height: 1 },
                      shadowRadius: 3,
                      shadowOpacity: 0.5,
                    }}>
                    <Text style={{ fontSize: 16, fontWeight: 'bold' }}>
                      {translate('pswdReq')}
                    </Text>
                    <View style={{ flexDirection: 'row' }}>
                      <FAIcon
                        name={isLowerCase ? 'check' : 'remove'}
                        size={18}
                        color={
                          isLowerCase ? BaseColor.green : BaseColor.alertRed
                        }
                      />
                      <Text style={{ marginLeft: 5 }}>
                        {translate('oneLowerCase')}
                      </Text>
                    </View>

                    <View style={{ flexDirection: 'row' }}>
                      <FAIcon
                        name={isUpperCase ? 'check' : 'remove'}
                        size={18}
                        color={
                          isUpperCase ? BaseColor.green : BaseColor.alertRed
                        }
                      />
                      <Text style={{ marginLeft: 5 }}>
                        {translate('oneUpperCase')}
                      </Text>
                    </View>

                    <View style={{ flexDirection: 'row' }}>
                      <FAIcon
                        name={isNu ? 'check' : 'remove'}
                        size={18}
                        color={isNu ? BaseColor.green : BaseColor.alertRed}
                      />
                      <Text style={{ marginLeft: 5 }}>
                        {translate('oneNumber')}
                      </Text>
                    </View>

                    <View style={{ flexDirection: 'row' }}>
                      <FAIcon
                        name={isSpecial ? 'check' : 'remove'}
                        size={18}
                        color={isSpecial ? BaseColor.green : BaseColor.alertRed}
                      />
                      <Text style={{ marginLeft: 5 }}>
                        {translate('oneSp')}
                      </Text>
                    </View>
                    <View style={{ flexDirection: 'row' }}>
                      <FAIcon
                        name={isTwelve ? 'check' : 'remove'}
                        size={18}
                        color={isTwelve ? BaseColor.green : BaseColor.alertRed}
                      />
                      <Text style={{ marginLeft: 5 }}>
                        {translate('twLong')}
                      </Text>
                    </View>
                  </View>
                )}
                <CInput
                  ref={emailRef}
                  placeholder={translate('emailId')}
                  value={state.email}
                  onChangeText={val => {
                    setstate({ ...state, email: val });
                  }}
                  placeholderTextColor={BaseColors.textGrey}
                  hideLeftIcon
                  // iconName="envelope-2"
                  textInputWrapper={{
                    marginTop: 12,
                  }}
                  keyboardType="email-address"
                  onSubmitEditing={() => {
                    pNumRef.current.focus();
                  }}
                  showError={mailError}
                  errorMsg={mailErrorTxt}
                  rightIcon
                />
                <CInput
                  ref={pNumRef}
                  placeholder={translate('forgotInput')}
                  value={state.pNum}
                  onChangeText={val => {
                    setstate({ ...state, pNum: val });
                  }}
                  placeholderTextColor={BaseColors.textGrey}
                  hideLeftIcon
                  // iconName="smartphone"
                  textInputWrapper={{
                    marginTop: 12,
                  }}
                  keyboardType="number-pad"
                  onSubmitEditing={() => {
                    Keyboard.dismiss();
                  }}
                  showError={numError}
                  errorMsg={numErrorTxt}
                  rightIcon
                />
                <LinearGradient
                  start={{ x: 0, y: 0 }}
                  end={{ x: 0, y: 1 }}
                  colors={['#0000', BaseColor.white20]}
                  style={[
                    styles.countryPickerStyle,
                    {
                      backgroundColor: BaseColor.white40,
                      borderColor: BaseColors.textGrey,
                    },
                  ]}>
                  <View>
                    <View>
                      <CountryPicker
                        {...{
                          countryCode: state.selectedCountry || 'ES',
                          withFilter: true,
                          withFlag: true,
                          // renderFlagButton: false,
                          withCountryNameButton: true,
                          withAlphaFilter: true,
                          withCallingCode: true,
                          withEmoji: true,
                          disabled: true,
                          onSelect: val => {
                            setstate({
                              ...state,
                              selectedCountry: val.cca2,
                              selectedCountryName: val.name,
                              countryCode: val.callingCode[0],
                            });
                          },
                          theme: {
                            fontSize: 16,
                            onBackgroundTextColor: BaseColor.textGrey,
                            primaryColor: BaseColor.alertRed,
                            backgroundColor: BaseColor.whiteColor,
                            filterPlaceholderTextColor: BaseColor.blackColor,
                          },
                          // onOpen: null,
                          // onClose: () => {
                          //   // onClose();
                          // },
                        }}
                        visible={state.selectedCountry}
                      />
                    </View>
                  </View>
                </LinearGradient>
                <DropDown
                  placeholder={translate('selectLang')}
                  data={langList}
                  textStyle={{
                    fontFamily: FontFamily.default,
                    color: BaseColors.textGrey,
                    fontSize: 16,
                  }}
                  style={{
                    marginTop: 20,
                    flex: 1,
                    marginEnd: 4,
                    borderColor: BaseColors.textGrey,
                    paddingHorizontal: 15,
                  }}
                  valueProp="lang_name"
                  onSelect={val => {
                    // setHeight(val);
                    console.log('selLang==>>', val);
                    setstate({ ...state, selLang: val });
                  }}
                  selectedObject={state.selLang}
                  svgProp="flag"
                  lang={false}
                />
                <TouchableOpacity
                  style={{
                    marginTop: 16,
                    flexDirection: 'row',
                    alignItems: 'center',
                    paddingHorizontal: 26,
                    marginBottom: 32,
                  }}
                  activeOpacity={0.7}
                  onPress={() => {
                    setstate({ ...state, agree: !state.agree });
                  }}>
                  <FAIcon
                    name={state.agree ? 'circle' : 'circle-o'}
                    size={18}
                    color={BaseColor.blackColor}
                  />
                  <Text
                    style={[
                      styles.rememberText,
                      // { color: BaseColors.blackColor },
                    ]}>
                    {translate('agreeTo')}
                  </Text>
                  <TouchableOpacity
                    style={[
                      styles.rememberText,
                      { color: BaseColor.blackColor },
                    ]}>
                    <Text
                      onPress={() => {}}
                      style={{
                        fontSize: 12,
                        fontFamily: FontFamily.default,
                        color: BaseColors.blackColor,
                        textDecorationLine: 'underline',
                      }}>
                      {translate('termNCondition')}
                    </Text>
                  </TouchableOpacity>
                </TouchableOpacity>
                <View style={styles.inputWrapper}>
                  <CButton
                    style={styles.loginBtn}
                    title={translate('loginToSignup')}
                    anim
                    playAnimation={anim}
                    backAnim={backAnim}
                    onPress={() => {
                      Validation();
                    }}
                    loader={loader}
                    done={done}
                  />
                </View>
              </View>
            </View>
            <View style={{ alignItems: 'center', marginTop: 30 }}>
              <Text style={{ fontSize: 10, color: 'black' }}>
                Version {DeviceInfo.getVersion()}
              </Text>
            </View>
          </ScrollView>
          <CButton
            iconname="cancel"
            iconStyle={{ alignItems: 'flex-end' }}
            iconsize={10}
            iconColor={BaseColor.blackColor}
            style={[
              styles.closeBtn,
              {
                backgroundColor: BaseColor.whiteColor,
                borderColor: BaseColors.textGrey,
              },
            ]}
            onPress={() => {
              navigation.navigate('RedirectLS');
            }}
          />
        </KeyboardAvoidingView>
      </View>
    </>
  );
};

export default Signup;
