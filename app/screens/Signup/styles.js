import { Dimensions, StyleSheet } from 'react-native';
import BaseColor from '../../config/colors';
import { FontFamily } from '../../config/typography';

const { height: dHeight, width: dWidth } = Dimensions.get('window');

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: BaseColor.whiteColor
  },
  mainContainer: {
    flex: 1,
  },
  closeBtn: {
    height: 40,
    width: 40,
    borderRadius: 20,
    alignSelf: 'flex-end',
    position: 'absolute',
    borderColor: BaseColor.textGrey,
    borderWidth: 0.5,
    top: 34,
    right: 12,
  },
  loginBtn: {
    alignSelf: 'center',
    fontFamily: FontFamily.default,
    borderColor: BaseColor.blackColor,
    borderWidth: 1
  },
  mainInputStyle: {
    // flex: 1,
    justifyContent: 'center',
  },
  loginText: {
    fontSize: 28,
    color: BaseColor.whiteColor,
    // fontWeight: '700',
    fontFamily: FontFamily.default,
  },
  loginTextView: {
    marginTop: 40,
    marginBottom: 20,
    alignItems: 'center',
    fontFamily: FontFamily.default,
  },
  rememberText: {
    fontSize: 12,
    fontFamily: FontFamily.default,
    color: BaseColor.blackColor,
    marginStart: 8
  },
  countryPickerStyle: {
    height: 50,
    paddingLeft: 20,
    borderWidth: 1,
    backgroundColor: BaseColor.white60,
    borderColor: BaseColor.whiteColor,
    borderRadius: 30,
    paddingVertical: 8,
    justifyContent: 'center',
    marginTop: 12,
  },
  logoImg: {
    height: 60,
    width: 60,
    alignSelf: 'center',
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 7,
    // },
    // shadowOpacity: 0.41,
    // shadowRadius: 9.11,
    // elevation: 14,
  },
});

export default styles;
