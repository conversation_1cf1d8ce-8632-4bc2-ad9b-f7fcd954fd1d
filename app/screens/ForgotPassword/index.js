/* eslint-disable quotes */
/* eslint-disable no-unused-expressions */
import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  BackHandler,
  KeyboardAvoidingView,
  ScrollView,
  Platform,
} from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useDispatch, useSelector } from 'react-redux';
import Toast from 'react-native-simple-toast';
import { isObject } from 'lodash';
import { useTheme } from '@react-navigation/native';
import styles from './styles';
import CButton from '../../components/CButton';
import CInput from '../../components/CInput';
import { translate } from '../../lang/Translate';
import {
  enableAnimateInEaseOut,
  sendErrorReport,
} from '../../utils/commonFunction';
import { getApiData } from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import AuthAction from '../../redux/reducers/auth/actions';
import GradientBack from '../../components/gradientBack';
import CHeader from '../../components/CHeader';
import BaseColors from '../../config/colors';

/**
 *
 *@module ForgotPassword
 */
function ForgotPassword({ navigation }) {
  const colors = useTheme();
  const BaseColor = colors.colors;
  const { brandToken } = useSelector(state => state.auth);

  const [isNumber, setIsNumber] = useState('');
  const [IsNumberError, setIsNumberError] = useState(false);
  const [IsNumberErrorTxt, setIsNumberErrorTxt] = useState('');

  const [loader, setloader] = useState(false);
  const [done, setdone] = useState(false);
  const [anim, setanim] = useState(false);
  const [backAnim, setBackAnim] = useState(false);

  const dispatch = useDispatch();
  const { setUserId } = AuthAction;

  const validation = () => {
    enableAnimateInEaseOut();

    const emailVal =
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

    if (isNumber == '') {
      allErrorFalse();
      setIsNumberError(true);
      setIsNumberErrorTxt('Please enter Email Address');
    } else if (!emailVal.test(String(isNumber))) {
      allErrorFalse();
      setIsNumberError(true);
      setIsNumberErrorTxt('Please enter valid Email');
    } else {
      allErrorFalse();
      forgotPassword();
    }
  };

  const allErrorFalse = () => {
    setIsNumberError(false);
  };

  /** this function for forgot Password
   * @function forgotPassword
   * @param {object} data email, token
   */
  const forgotPassword = () => {
    setloader(true);
    setanim(true);
    setBackAnim(false);
    const data = {
      email: isNumber,
      token: brandToken,
      brand_name: 'Babyauto',
      app_name: 'babyauto',
    };

    getApiData(BaseSetting.endpoints.forgotPassword, 'POST', data)
      .then(response => {
        const uId =
          response && isObject(response.data) && response.data.user_id
            ? response.data.user_id
            : null;
        dispatch(setUserId(uId));

        if (response.success) {
          setTimeout(() => {
            setloader(false);
            setdone(true);
          }, 2000);
          setTimeout(() => {
            navigation.navigate('Otp', { type: 'ForgotPassword' });
          }, 3000);
        } else {
          Toast.show(response.message);
          setanim(false);
          setBackAnim(true);
          setdone(false);
          setloader(false);
        }
      })
      .catch(err => {
        Toast.show(
          'Something went wrong while sending password recovery request',
        );
        sendErrorReport(err, 'forgot_pass_api');
        setanim(false);
        setBackAnim(true);
        setdone(false);
        setloader(false);
        console.log('ERRR', err);
      });
  };

  function handleBackButtonClick() {
    navigation.goBack();
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);
  return (
    <View style={styles.root}>
      {/* <GradientBack /> */}
      <View style={styles.headerStyle}>
        <CHeader
          backBtn
          onLeftPress={() => {
            navigation.goBack();
          }}
        />
      </View>
      <KeyboardAvoidingView
        behavior={Platform.OS == 'ios' ? 'padding' : null}
        style={styles.mainContainer}>
        <ScrollView
          contentContainerStyle={{ flexGrow: 1 }}
          bounces={false}
          showsVerticalScrollIndicator={false}>
          <View style={styles.loginTextView}>
            <View>
              <View
                style={[
                  styles.lockIconStyle,
                  { backgroundColor: BaseColor.whiteColor },
                ]}>
                <MaterialCommunityIcons
                  name="key-outline"
                  color={BaseColors.IntroOrng}
                  size={40}
                />
              </View>
              <Text
                style={[styles.loginText, { color: BaseColors.blackColor }]}>
                {translate('forgotScreen')}
              </Text>
            </View>
            <Text
              style={[styles.associatedTest, { color: BaseColors.blackColor }]}>
              {translate('forgotEmail')}
            </Text>
          </View>
          <View style={styles.inputWrapper}>
            <CInput
              placeholder={translate('emailId')}
              value={isNumber}
              onChangeText={val => {
                setIsNumber(val);
              }}
              keyboardType="email-address"
              placeholderTextColor={BaseColor.blackColor}
              iconName="envelope-2"
              showError={IsNumberError}
              errorMsg={IsNumberErrorTxt}
              onSubmitEditing={() => {
                validation();
              }}
            />
          </View>
          <View style={styles.inputWrapper}>
            <CButton
              style={styles.loginBtn}
              title={translate('forgotBtn')}
              anim
              playAnimation={anim}
              backAnim={backAnim}
              onPress={() => {
                validation();
              }}
              loader={loader}
              done={done}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

export default ForgotPassword;
