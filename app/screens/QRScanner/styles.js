/* eslint-disable quotes */
import { StyleSheet, Dimensions } from "react-native";
import BaseColor from "../../config/colors";
import { FontFamily } from "../../config/typography";

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: BaseColor.whiteColor
  },
  mainView: {
    flex: 1,
    backgroundColor: BaseColor.whiteColor,
    marginTop: 10,
  },
  discriptionTextView: {
    position: "absolute",
    bottom: 0,
    width: Dimensions.get("screen").width,
    backgroundColor: BaseColor.blueDark,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
  },
  discriptionText: {
    textAlign: "center",
    paddingVertical: 20,
    fontSize: 16,
    color: BaseColor.whiteColor,
  },
  nonSPTextView: {
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    backgroundColor: BaseColor.whiteColor,
    paddingVertical: 30,
  },
  nonSPText: {
    textAlign: "center",
    fontSize: 14,
    color: BaseColor.blueLight,
    textDecorationLine: "underline",
    fontWeight: "bold",
  },
  bottomViewStyle: {
    backgroundColor: BaseColor.whiteColor,
    // borderTopLeftRadius: 25,
    // borderTopRightRadius: 25,
  },
  scanDescViewStyle: { padding: 20, paddingHorizontal: 50, marginBottom: 30 },
  qrTextStyle: {
    textAlign: "center",
    fontSize: 16,
    fontFamily: FontFamily.default,
    color: BaseColor.blackColor,
  },
  openSettingsText: {
    textAlign: "center",
    fontSize: 18,
    marginTop: 20,
    fontFamily: FontFamily.default,
    color: BaseColor.whiteColor,
  },
  linkViewStyle: {
    backgroundColor: "#FFF",
    padding: 25,
    paddingHorizontal: 50,
    borderTopLeftRadius: 25,
    borderTopRightRadius: 25,
  },
  linkTextStyle: {
    color: BaseColor.blueDark,
    textAlign: "center",
    textDecorationLine: "underline",
  },
});

export default styles;
