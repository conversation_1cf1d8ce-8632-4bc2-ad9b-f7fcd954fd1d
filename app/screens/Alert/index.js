/* eslint-disable eqeqeq */
/* eslint-disable indent */
/* eslint-disable no-nested-ternary */
/* eslint-disable quotes */
import React, { useEffect, useState } from 'react';
import {
  FlatList,
  View,
  BackHandler,
  ActivityIndicator,
  Text,
} from 'react-native';
import { useSelector } from 'react-redux';
import { isArray, isEmpty } from 'lodash';
import styles from './styles';
import AlertCard from '../../components/AlertCardList';
import CHeader from '../../components/CHeader';
import { translate } from '../../lang/Translate';
import CAlert from '../../components/CAlert';
import {
  enableAnimateInEaseOut,
  sendErrorReport,
} from '../../utils/commonFunction';
import { getApiData } from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import BaseColor from '../../config/colors';
import NoData from '../../components/Nodata';

/**
 *
 * @module AlertsList
 *
 */
const Alerts = ({ navigation }) => {
  const [alertModal, setalertModal] = useState(false);
  const [alertState, setAlertState] = React.useState({
    alerts: [],
    alertLoad: true,
    loadMore: false,
    loadingMoreData: false,
    page: 1,
  });
  const accessToken = useSelector(state => state.auth.accessToken);

  // this function for get alerts data
  /** this function for get alerts data
   * @function getAlerts
   * @param {object} data page
   */
  async function getAlerts(load) {
    const headers = {
      'Content-Type': 'application/json',
      authorization: accessToken ? `Bearer ${accessToken}` : '',
    };
    setAlertState({
      ...alertState,
      alertLoad: load === 'loadMore' ? false : true,
    });

    try {
      const response = await getApiData(
        BaseSetting.endpoints.getAlerts,
        'POST',
        { page: alertState.page },
        headers,
      );

      if (response.success) {
        const ary = isArray(response.data) ? response.data : [];
        console.log('🚀 ~ file: index.js ~ line 67 ~ getAlerts ~ ary', ary);
        const allAry =
          load === 'loadMore' ? [...alertState.alerts, ...ary] : ary;
        const loadMoreVar = response.next_enable || false;
        setAlertState({
          alerts: allAry,
          alertLoad: false,
          loadMore: loadMoreVar,
          loadingMoreData: false,
          page: loadMoreVar ? alertState.page + 1 : alertState.page,
        });
      } else {
        setAlertState({
          alerts: [],
          alertLoad: false,
          loadingMoreData: false,
        });
      }
    } catch (error) {
      console.log('alert error ===', error);
      sendErrorReport(error, 'getAlerts');
      setAlertState({
        alerts: [],
        alertLoad: false,
        loadingMoreData: false,
      });
    }
  }

  useEffect(() => {
    getAlerts();
    readNotification();
  }, []);

  /** this function for read Notification
   * @function readNotification
   * @param {object} data {}
   */

  async function readNotification() {
    const headers = {
      'Content-Type': 'application/json',
      authorization: accessToken ? `Bearer ${accessToken}` : '',
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.readNotification,
        'POST',
        {},
        headers,
      );

      // Toast.show(response.message);
      if (response.success) {
        console.log('successsss');
      }
    } catch (error) {
      console.log('read_notification error ===', error);
      sendErrorReport(error, 'read_notification');
    }
  }

  // this function for clear all alerts
  /** this function for clear all alerts
   * @function clearAlerts
   * @param {object} data {}
   */
  async function clearAlerts() {
    const headers = {
      'Content-Type': 'application/json',
      authorization: accessToken ? `Bearer ${accessToken}` : '',
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.clearAlerts,
        'POST',
        {},
        headers,
      );
      if (response.success) {
        enableAnimateInEaseOut();
        setAlertState({
          alerts: [],
          alertLoad: false,
          loadMore: false,
          loadingMoreData: false,
          page: 1,
        });
      }
    } catch (error) {
      console.log('add alert error ===', error);
      sendErrorReport(error, 'clear_Alerts');
    }
  }

  const render = ({ item, index }) => {
    const nIndex = index + 1;
    console.log('iconssssss', item.icon);
    return (
      <AlertCard
        animTime={nIndex * 300}
        largeIcon="stroller"
        title={item.title}
        time={item.createdAt}
        icon={item.icon || ''}
        rightIcon="chevron-right"
      />
    );
  };

  function handleBackButtonClick() {
    navigation.goBack();
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  return (
    <View style={styles.root}>
      {/* <GradientBack /> */}
      <CHeader
        title={translate('pushText')}
        rightIconName={isEmpty(alertState.alerts) ? '' : 'clear'}
        onRightPress={() => (isEmpty(alertState.alerts) ? null : clearAlerts())}
        backBtn
        leftIconName
        onLeftPress={() => {
          navigation.goBack();
        }}
      />
      {alertState.alertLoad ? (
        <View style={styles.loaderView}>
          <ActivityIndicator color={BaseColor.blackColor} />
        </View>
      ) : !isEmpty(alertState?.alerts) && isArray(alertState?.alerts) ? (
        <View style={styles.flatlistView}>
          <FlatList
            data={alertState.alerts}
            renderItem={render}
            keyExtractor={item => item.id}
            contentContainerStyle={{ paddingBottom: 80 }}
            onEndReached={() => {
              if (alertState.loadMore && !alertState.loadingMoreData) {
                getAlerts('loadMore');
              }
            }}
            onEndReachedThreshold={0.5}
            ListFooterComponent={() => {
              if (alertState?.alerts?.length === 0) {
                return (
                  <View style={styles.emptyView}>
                    <Text style={styles.emptyDataText}>
                      {translate('noAlerts')}
                    </Text>
                  </View>
                );
              }
              return null;
            }}
          />
        </View>
      ) : (
        <NoData />
      )}
      <CAlert
        visible={alertModal}
        onRequestClose={() => setalertModal(false)}
        alertMessage={translate('homeAlertMsg')}
        alertTitle={translate('homeAlertTitle')}
        onCancelPress={() => {
          setalertModal(false);
        }}
      />
    </View>
  );
};

export default Alerts;
