import { Dimensions, StyleSheet } from 'react-native';
import BaseColor from '../../config/colors';
import { FontFamily } from '../../config/typography';

const styles = StyleSheet.create({
  root: {
    flex: 1,
  },
  image: {
    width: Dimensions.get('screen').width,
    height: Dimensions.get('screen').height,
    resizeMode: 'cover'
  },
  contentView: {
    flex: 1,
    position: 'absolute',
    bottom: 0,
    width: Dimensions.get('screen').width,
    height: 250,
    borderTopLeftRadius: 46,
    paddingHorizontal: 34,
    paddingVertical: 22,
    backgroundColor: BaseColor.whiteColor
  },
  titleText: {
    color: BaseColor.blackColor,
    fontSize: 26,
    // textAlign: 'center',
    alignItems: 'flex-start',
    paddingBottom: 20,
    paddingTop: 14,
    fontFamily: FontFamily.default,
  },
  descriptionText: {
    color: BaseColor.blackColor,
    fontSize: 16,
    // textAlign: 'center',
    alignItems: 'flex-start',
    fontFamily: FontFamily.default,
    lineHeight: 21
  },
  buttonCircle: {
    width: 40,
    height: 40,
    backgroundColor: 'rgba(0, 0, 0, .2)',
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    bottom: 20,
    right: 20,
  },
});

export default styles;
