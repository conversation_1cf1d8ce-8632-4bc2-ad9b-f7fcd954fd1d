/* eslint-disable max-len */
/* eslint-disable react/no-array-index-key */
/* eslint-disable global-require */
import React, {useEffect, useState} from 'react';
import {StatusBar, View, BackHandler, TouchableOpacity} from 'react-native';
import {useDispatch} from 'react-redux';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import BaseColor from '../../config/colors';
import styles from './styles';
import CButton from '../../components/CButton';
import authActions from '../../redux/reducers/auth/actions';
import {enableAnimateInEaseOut} from '../../utils/commonFunction';
import {translate} from '../../lang/Translate';

const Walkthrough = ({navigation}) => {
  const animTime = 200;
  const [currentIndex, setcurrentIndex] = useState(0);

  const dispatch = useDispatch();

  const anim = useSharedValue(1);
  const nextImgAnim = useSharedValue(0);

  const slides = [
    {
      key: 'one',
      image: require('../../assets/images/Intro-large-1-2.jpg'),
      // title: 'Welcome to Babyauto',
      title: translate('introTitle1'),
      text: `${translate('introText11')}\n${translate(
        'introText12',
      )}\n${translate('introText13')} ${translate('introText14')}`,
    },
    {
      key: 'two',
      image: require('../../assets/images/Intro-large-2.jpg'),
      // title: 'Connecting your device',
      title: translate('introTitle2'),
      // text:
      //   'During set-up you will need to locate\nyour personal QR code to scan with\nyou phone which will link to your\nnew Babyauto smart device.',
      text: `${translate('introText21')} ${translate(
        'introText22',
      )} ${translate('introText23')} ${translate('introText24')}`,
    },
    {
      key: 'three',
      image: require('../../assets/images/Intro-large-3-2.jpg'),
      // title: 'Your digital partner',
      title: translate('introTitle3'),
      text: `${translate('introText31')} ${translate(
        'introText32',
      )} ${translate('introText33')} ${translate('introText34')}`,
    },
  ];

  const animStyle = useAnimatedStyle(() => ({
    opacity: withTiming(
      anim.value,
      {
        duration: animTime,
      },
      () => {
        anim.value = 1;
      },
    ),
  }));

  const titleStyle = useAnimatedStyle(() => ({
    opacity: withTiming(
      anim.value,
      {
        duration: animTime,
      },
      () => {
        anim.value = 1;
      },
    ),
  }));

  const desStyle = useAnimatedStyle(() => ({
    opacity: withTiming(
      anim.value,
      {
        duration: animTime,
      },
      () => {
        anim.value = 1;
      },
    ),
  }));

  const {setWalkthrough} = authActions;

  function handleBackButtonClick() {
    BackHandler.exitApp();
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  const changeIndex = () => {
    if (currentIndex !== 2) {
      nextImgAnim.value = 0;
      anim.value = 0.2;
      setTimeout(() => {
        const nIndex = currentIndex + 1;
        setcurrentIndex(nIndex);
      }, animTime);
    }
  };

  const preIndex = () => {
    if (currentIndex !== 0) {
      anim.value = 0.2;
      setTimeout(() => {
        const nIndex = currentIndex - 1;
        setcurrentIndex(nIndex);
      }, animTime);
    }
  };

  enableAnimateInEaseOut();
  return (
    <>
      <View style={{flex: 1, backgroundColor: 'black'}}>
        <View style={{flex: 1}}>
          <View style={{flex: 1}}>
            <StatusBar
              backgroundColor="transparent"
              barStyle="dark-content"
              translucent
            />
            <Animated.Image
              style={[styles.image, animStyle]}
              source={slides[currentIndex].image}
              resizeMode="cover"
            />
            {/* <LinearGradient
              start={{ x: 0, y: 0 }}
              end={{ x: 0, y: 1 }}
              colors={[BaseColor.whiteColor, BaseColor.whiteColor]}
              style={styles.contentView}
            > */}
            <View style={styles.contentView}>
              <Animated.Text style={[styles.titleText, titleStyle]}>
                {translate(slides[currentIndex].title)}
              </Animated.Text>
              <Animated.Text style={[styles.descriptionText, desStyle]}>
                {slides[currentIndex].text}
              </Animated.Text>
              <View
                style={{
                  position: 'absolute',
                  alignItems: 'flex-start',
                  flexDirection: 'row',
                  bottom: 24,
                  alignSelf: 'flex-start',
                  paddingHorizontal: 34,
                }}>
                {slides.map((item, index) => (
                  <Animated.View
                    key={`i${index}`}
                    style={{
                      backgroundColor:
                        index === currentIndex
                          ? BaseColor.IntroOrng
                          : BaseColor.IntroLight,
                      margin: 4,
                      borderRadius: 5,
                      height: 7,
                      width: index === currentIndex ? 30 : 15,
                    }}
                  />
                ))}
              </View>
            </View>
            {/* </LinearGradient> */}
          </View>
        </View>
        <View
          style={{
            flex: 1,
            position: 'absolute',
            height: '100%',
            width: '100%',
            flexDirection: 'row',
            backgroundColor: '#0000',
          }}>
          <TouchableOpacity style={{flex: 1}} onPress={preIndex} />
          <TouchableOpacity style={{flex: 2}} onPress={changeIndex} />
        </View>
        {currentIndex === 2 ? (
          <View style={styles.buttonCircle}>
            <CButton
              iconname="check"
              iconsize={18}
              iconColor={BaseColor.blueLight}
              style={{
                width: 50,
                height: 50,
                borderRadius: 50,
                borderColor: BaseColor.textGrey,
                borderWidth: 0.2,
              }}
              onPress={() => {
                navigation.navigate('RedirectLS');
                dispatch(setWalkthrough(false));
              }}
            />
          </View>
        ) : (
          <View style={styles.buttonCircle}>
            <CButton
              iconname="next-1"
              iconsize={18}
              iconColor={BaseColor.blueLight}
              style={{
                width: 50,
                height: 50,
                borderRadius: 50,
                borderColor: BaseColor.textGrey,
                borderWidth: 0.2,
              }}
              onPress={changeIndex}
            />
          </View>
        )}
      </View>
    </>
  );
};

export default Walkthrough;
