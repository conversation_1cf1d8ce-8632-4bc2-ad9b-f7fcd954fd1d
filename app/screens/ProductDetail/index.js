/* eslint-disable quotes */
import React, {useEffect, useState} from 'react';
import {
  BackHandler,
  Dimensions,
  FlatList,
  Image,
  ScrollView,
  Text,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native';
import FAIcon from 'react-native-vector-icons/FontAwesome';
import {useTheme} from '@react-navigation/native';
import Toast from 'react-native-simple-toast';
import {useSelector} from 'react-redux';
import Share, {Social} from 'react-native-share';
import RNFetchBlob from 'rn-fetch-blob';
import HTML, {RenderHTML} from 'react-native-render-html';
import CHeader from '../../components/CHeader';
import GradientBack from '../../components/gradientBack';
import {translate} from '../../lang/Translate';
import styles from './styles';
import {openInAppBrowser, sendErrorReport} from '../../utils/commonFunction';
import {FontFamily} from '../../config/typography';
import {CustomIcon} from '../../config/LoadIcons';
import BaseSetting from '../../config/setting';
import {getApiData} from '../../utils/apiHelper';
import {isEmpty} from 'lodash';

/**
 *
 *@module ProductDetail
 *
 */
export default function ProductDetail({navigation, route}) {
  const token = useSelector(state => state.auth.accessToken);
  const productDetails = route?.params?.productDetail;
  const collectionTitle = route?.params?.collectionTitle;
  console.log('ProductDetail -> productDetails', productDetails);

  const colors = useTheme();
  const BaseColor = colors.colors;

  const [currentIndex, setcurrentIndex] = useState(0);
  const [clrArr, setClrArr] = useState([]);
  const [selectedImageId, setSelectedImageId] = useState(0);
  const [selectedClrId, setSelectedClrId] = useState(0);
  const [quantity, setQuantity] = useState(0);
  const [price, setPrice] = useState(
    productDetails?.variants[0]?.price
      ? productDetails?.variants[0]?.price
      : '0',
  );
  const htmld = productDetails?.body_html;

  const setMin = () => {
    if (quantity > 0) {
      setQuantity(quantity - 1);
    }
  };
  const setMax = () => {
    setQuantity(quantity + 1);
  };

  function handleBackButtonClick() {
    navigation.goBack();
    return true;
  }

  const link = `https://shop.babyauto.com/collections/${collectionTitle}/products/${productDetails.handle}?utm_source=App&utm_medium=shop&utm_id=CBT`;
  // `https://shop.babyauto.com/products/${productDetails.handle}`;
  useEffect(() => {
    getColors(productDetails?.options);
  });

  const getColors = optionsArr => {
    if (!isEmpty(optionsArr)) {
      optionsArr.find(obj => {
        if (obj?.name.includes('Color')) {
          setClrArr(obj.values);
        }
      });
    }
  };

  const getPrice = index => {
    if (!isEmpty(productDetails.variants)) {
      productDetails.variants.find(obj => {
        if (obj?.title.includes(clrArr[index])) {
          setPrice(obj?.price);
          scrollToColorIndex(obj?.image_id);
        }
      });
    }
  };
  const scrollToColorIndex = objImgID => {
    productDetails.images.find(obj => {
      if (obj?.id === objImgID) {
        productDetails.images.find((product, index) => {
          if (product === obj) {
            ref.scrollToIndex({
              animated: true,
              index: index,
              viewPosition: index,
            });
            refThumb.scrollToIndex({animated: true, index: index});
          }
        });
      }
    });
  };

  const findImageId = index => {
    // colorImageId == images array id
    if (!isEmpty(productDetails.variants)) {
      productDetails.variants.find(obj => {
        if (obj?.title.includes(clrArr[index])) {
          scrollToColorIndex(obj?.image_id);
        }
      });
    }
  };
  const [refThumb, setRefThumb] = useState(null);

  const onViewRef = React.useRef(viewableItems => {
    console.log('------***', viewableItems.viewableItems[0].index);
    setcurrentIndex(viewableItems.viewableItems[0].index);
    // Use viewable items in state or as intended
  });
  const viewConfigRef = React.useRef({viewAreaCoveragePercentThreshold: 50});

  const [ref, setRef] = useState(null);

  useEffect(() => {
    if (refThumb !== null) {
      refThumb.scrollToIndex({
        animated: true,
        index: currentIndex,
        viewOffset: Dimensions.get('window').width / 3,
      });
    }
  }, [currentIndex]);

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);
  /** this function for Click, Share and View ,Product-Action
   * @function getProductAction
   * @param {object} data product_id, type
   */
  async function getProductAction(id, type) {
    const headers = {
      'Content-Type': 'application/json',
      authorization: token ? `Bearer ${token}` : '',
    };
    try {
      const response = await getApiData(
        BaseSetting.endpoints.addproductaction,
        'POST',
        {
          product_id: id,
          type,
        },
        headers,
      );

      if (response.success) {
        // setPostLike(response.data);
        // Toast.show(type);
      } else {
        Toast.show(response.message);
      }
    } catch (error) {
      sendErrorReport(error, 'get_feed_post');
      console.log('feed post error ===', error);
    }
  }

  /** this function for get Product View
   * @function getEcomProductView
   * @param {object} data product_id, type
   */
  async function getEcomProductView(id, type) {
    const headers = {
      'Content-Type': 'application/json',
      authorization: token ? `Bearer ${token}` : '',
    };
    try {
      const response = await getApiData(
        BaseSetting.endpoints.addEcomProductAction,
        'POST',
        {
          product_id: id,
          type,
        },
        headers,
      );

      if (response.success) {
        console.log('ecom action----', response);
        // setPostLike(response.data);
        // Toast.show(type);
      } else {
        Toast.show(response.message);
      }
    } catch (error) {
      sendErrorReport(error, 'get_feed_post');
      console.log('feed post error ===', error);
    }
  }

  // const onViewRef = React.useRef((viewableItems) => {
  //   console.log(viewableItems.viewableItems[0].index);
  //   setcurrentIndex(viewableItems.viewableItems[0].index);
  //   // Use viewable items in state or as intended
  // });
  // const viewConfigRef = React.useRef({ viewAreaCoveragePercentThreshold: 50 });

  const shareImage = url => {
    if (
      productDetails.product_link &&
      (productDetails.product_link.includes('http://') ||
        productDetails.product_link.includes('https://'))
    ) {
      const shareResponse = Share.open({
        url,
        message: `Shared Post ${productDetails.product_link}`,
      })
        .then(res1 => {
          console.log(res1);
        })
        .catch(err => {
          console.log(err);
        });
      console.log(shareResponse);
    } else {
      Toast.show(translate('urlError'));
    }
  };

  const onShare = async type => {
    try {
      if (type === 'share') {
        const {fs} = RNFetchBlob;
        let imagePath = null;
        RNFetchBlob.config({
          fileCache: true,
        })
          .fetch('GET', productDetails.product_image)
          // the image is now dowloaded to device's storage
          .then(resp => {
            // the image path you can use it directly with Image component
            imagePath = resp.path();
            return resp.readFile('base64');
          })
          .then(base64Data => {
            // here's base64 encoded image
            const base64 = `data:image/png;base64,${base64Data}`;
            shareImage(base64);
            // remove the file from storage
            return fs.unlink(imagePath);
          });
      } else {
        const result = await Share.open({
          message: `Shared Post ${productDetails.product_link}`,
          // url: shareText,
        });
        if (result.action === Share.sharedAction) {
          if (result.activityType) {
            // shared with activity type of result.activityType
          } else {
            // shared
          }
        } else if (result.action === Share.dismissedAction) {
          // dismissed
        }
      }
    } catch (error) {
      console.log('error: ==>', error);
    }
  };
  const onShareEcom = async type => {
    try {
      if (type === 'share') {
        const {fs} = RNFetchBlob;
        let imagePath = null;
        RNFetchBlob.config({
          fileCache: true,
        })
          .fetch('GET', productDetails.image.src)
          // the image is now dowloaded to device's storage
          .then(resp => {
            // the image path you can use it directly with Image component
            imagePath = resp.path();
            return resp.readFile('base64');
          })
          .then(base64Data => {
            // here's base64 encoded image
            const base64 = `data:image/png;base64,${base64Data}`;
            shareImage(base64);
            // remove the file from storage
            return fs.unlink(imagePath);
          });
      } else {
        const result = await Share.open({
          message: `Shared Post ${link}`,
          // url: shareText,
        });
        if (result.action === Share.sharedAction) {
          if (result.activityType) {
            // shared with activity type of result.activityType
          } else {
            // shared
          }
        } else if (result.action === Share.dismissedAction) {
          // dismissed
        }
      }
    } catch (error) {
      console.log('error: ==>', error);
    }
  };
  return (
    <View style={{flex: 1, backgroundColor: BaseColor.whiteColor}}>
      {/* <GradientBack /> */}

      <CHeader
        title={productDetails.title} //  {productDetails.product_name}
        backBtn
        leftIconName
        onLeftPress={() => {
          navigation.goBack();
        }}
      />

      {/* <ScrollView contentContainerStyle={{}}>
        <View style={styles.root}>
          <View style={{ height: 400, width: "100%" }}>
            {/* <FlatList
              data={imgArr}
              horizontal
              pagingEnabled
              renderItem={({ item }) => (
                <Image style={styles.imageStyle} source={item.image} />
              )}
              contentContainerStyle={styles.imgFlatlist}
              onViewableItemsChanged={onViewRef.current}
              viewabilityConfig={viewConfigRef.current}
              showsHorizontalScrollIndicator={false}
            /> }

            <Image
              style={styles.imageStyle}
              source={{ uri: productDetails.product_image }}
            />

            {/* <View style={styles.imgScrollDot}>
              {imgArr.map((item, index) => (
                <View
                  style={[
                    styles.dots,
                    {
                      backgroundColor:
                        index == currentIndex
                          ? BaseColor.orange
                          : BaseColor.black30,
                      borderColor:
                        index == currentIndex
                          ? BaseColor.blackColor
                          : BaseColor.whiteColor,
                    },
                  ]}
                />
              ))}
            </View> }
          </View>

          <View style={{ padding: 8 }}>
            <View
              style={{
                flex: 1,
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <Text style={[styles.name, { color: BaseColor.blackColor }]}>
                {productDetails?.product_name}
              </Text>
              <TouchableOpacity
                style={{ padding: 8 }}
                activeOpacity={0.7}
                onPress={() => {
                  getProductAction(productDetails.id, "shared");
                  // Share.share({
                  //   message: `Share Product ${productDetails.product_link} `,
                  //   url: productDetails.product_link,
                  // });
                  onShare("share");
                }}
              >
                <CustomIcon
                  name="send-2"
                  color={BaseColor.blackColor}
                  size={18}
                />
              </TouchableOpacity>
            </View>
            <Text style={[styles.price, { color: BaseColor.blackColor }]}>
              $
              {' '}
              {productDetails?.sell_price}
            </Text>

            {/* <Text style={[styles.despTxt, { color: BaseColor.whiteColor }]}>
              Description
            </Text>
            {despArr.map((item) => (
              <View style={[styles.row, styles.desp]}>
                <FAIcon
                  name="dot-circle-o"
                  color={BaseColor.whiteColor}
                  size={16}
                />
                <Text
                  style={[
                    styles.addToCartText,
                    { color: BaseColor.whiteColor },
                  ]}
                >
                  {item?.text}
                </Text>
              </View>
            ))} }

            {/* <Text style={styles.newTextStyle}>
              {productDetails?.description}
            </Text> }

            <HTML source={{ html: productDetails?.description }} />

            <TouchableOpacity
              // ref={this.accordian}
              activeOpacity={0.7}
              style={[
                styles.accCont,
                { marginTop: 24 },
              ]}
              onPress={async () => {
                if (
                  productDetails?.product_guide
                  && productDetails?.product_guide.includes("https://")
                ) {
                  openInAppBrowser(productDetails.product_guide);
                } else {
                  Toast.show("Not valid Link");
                }
              }}
            >
              <Text
                style={{
                  fontSize: 14,
                  fontWeight: "bold",
                  color: BaseColor.blackColor,
                  fontFamily: FontFamily.default,
                }}
              >
                {translate("faqNManuals")}
              </Text>
              <FAIcon
                name="angle-right"
                size={30}
                color={BaseColor.blackColor}
              />
            </TouchableOpacity>
            <TouchableOpacity
              // ref={this.accordian}
              activeOpacity={0.7}
              style={[styles.accCont, { backgroundColor: BaseColor.whiteColor }]}
              onPress={() => {
                if (
                  productDetails?.video_link
                  && productDetails?.video_link.includes("https://")
                ) {
                  openInAppBrowser(productDetails.video_link);
                } else {
                  Toast.show("Not valid Link");
                }
              }}
            >
              <Text
                style={{
                  fontSize: 14,
                  fontWeight: "bold",
                  color: BaseColor.blackColor,
                  fontFamily: FontFamily.default,
                }}
              >
                {translate("technicalDrawing")}
                {" "}
              </Text>
              <FAIcon
                name="angle-right"
                size={30}
                color={BaseColor.blackColor}
              />
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView> */}
      <ScrollView
        contentContainerStyle={{
          paddingHorizontal: 15,
          backgroundColor: BaseColor.whiteColor,
        }}>
        <View style={[styles.root, {backgroundColor: BaseColor.whiteColor}]}>
          {/* <View style={{ height: 400, width: "100%" }}> */}
          <View
            style={{
              height: 300,
              marginVertical: 15,
              backgroundColor: BaseColor.whiteColor,
            }}>
            <FlatList
              data={productDetails.images}
              ref={refr => {
                setRef(refr);
              }}
              horizontal
              pagingEnabled
              renderItem={({item}) => (
                <Image style={styles.imageStyle} source={{uri: item.src}} />
              )}
              contentContainerStyle={styles.imgFlatlist}
              onViewableItemsChanged={onViewRef.current}
              viewabilityConfig={viewConfigRef.current}
              showsHorizontalScrollIndicator={false}
            />
          </View>
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <TouchableOpacity
              onPress={() => {
                if (currentIndex > 0) {
                  ref.scrollToIndex({
                    animated: true,
                    index: currentIndex - 1,
                    viewPosition: currentIndex - 1,
                  });
                  setcurrentIndex(currentIndex - 1);
                }
              }}>
              <FAIcon
                name="angle-left"
                size={20}
                color={BaseColor.blackColor}
              />
            </TouchableOpacity>

            <FlatList
              data={productDetails.images}
              horizontal
              ref={refr => {
                setRefThumb(refr);
              }}
              renderItem={({item, index}) => (
                <TouchableOpacity
                  style={{
                    margin: 10,
                    borderColor: 'black',
                    borderWidth: index === currentIndex ? 2 : 0,
                    padding: 2,
                  }}
                  onPress={() => {
                    setcurrentIndex(index);
                    ref.scrollToIndex({
                      animated: true,
                      index: index,
                      viewPosition: index,
                    });
                  }}>
                  <Image
                    source={{uri: item.src}}
                    style={{height: 50, width: 50}}
                  />
                </TouchableOpacity>
              )}
              style={{marginHorizontal: 10}}
              // onViewableItemsChanged={onViewRefThumb.current}
              // viewabilityConfig={viewConfigRefThumb.current}
              showsHorizontalScrollIndicator={false}
            />
            <TouchableOpacity
              onPress={() => {
                if (currentIndex < productDetails.images.length - 1) {
                  ref.scrollToIndex({
                    animated: true,
                    index: currentIndex + 1,
                    viewPosition: currentIndex + 1,
                  });
                  setcurrentIndex(currentIndex + 1);
                }
              }}>
              <FAIcon
                name="angle-right"
                size={20}
                color={BaseColor.blackColor}
              />
            </TouchableOpacity>
          </View>
          <View style={{padding: 8}}>
            <View
              style={{
                flex: 1,
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}>
              <Text style={[styles.name, {color: BaseColor.blackColor}]}>
                {productDetails?.title}
              </Text>
              <TouchableOpacity
                style={{padding: 8}}
                activeOpacity={0.7}
                onPress={() => {
                  getEcomProductView(productDetails.id, 'shared');
                  Share.open({
                    title: 'Share Product',
                    // message: `Share Product ${link} `,
                    url: link,
                  });
                  // Share.share({
                  //   message: `Share Product ${link} `,
                  //   url: link,
                  // });
                  // onShare("share");
                  // onShareEcom("share");
                }}>
                <CustomIcon
                  name="send-2"
                  color={BaseColor.blackColor}
                  size={18}
                />
              </TouchableOpacity>
            </View>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              {productDetails?.variants[0]?.compare_at_price &&
              productDetails?.variants[0]?.compare_at_price > 0 ? (
                <>
                  <Text
                    style={{
                      ...styles.valueStyle,
                      color: BaseColor.blackColor,
                      fontSize: 18,
                      textDecorationLine: 'line-through',
                      marginEnd: 10,
                    }}>
                    €{productDetails?.variants[0].compare_at_price}
                  </Text>
                </>
              ) : null}

              <Text style={[styles.price, {color: BaseColor.blackColor}]}>
                €{price}
              </Text>
            </View>
            <Text>{translate('taxIncluded')}</Text>
            <HTML source={{html: htmld}} />
            <View
              style={[styles.divider, {backgroundColor: BaseColor.black40}]}
            />

            {/* {!isEmpty(clrArr) && (
              <>
                <View style={{ flexDirection: "row" }}>
                  <Text
                    style={{ fontWeight: "bold", color: BaseColor.blackColor }}
                  >
                    Color
                  </Text>
                  <Text style={{}}> - {clrArr[selectedClrId]} </Text>
                </View>
                <FlatList
                  data={clrArr}
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  renderItem={({ item, index }) => (
                    <TouchableOpacity
                      style={{
                        marginVertical: 10,
                        marginRight: 10,
                        borderColor: "black",
                        borderWidth: index === selectedClrId ? 2 : 0,
                        padding: 2,
                      }}
                      onPress={() => {
                        getPrice(index);
                        findImageId(index);
                        setSelectedClrId(index);
                      }}
                    >
                      <View
                        style={{
                          backgroundColor: `${item.toLowerCase()}`,
                          height: 30,
                          width: 30,
                          borderColor: "black",
                          borderWidth: 1,
                        }}
                      />
                    </TouchableOpacity>
                  )}
                />
              </>
            )} */}
          </View>
        </View>
      </ScrollView>

      <TouchableOpacity
        style={[styles.addToCartView, {backgroundColor: BaseColor.orange}]}
        activeOpacity={0.7}
        onPress={() => {
          console.log(
            'openUrl -> productDetails?.product_link',
            productDetails?.product_link,
          );
          // if (
          //   productDetails?.product_link
          //   && productDetails?.product_link.includes("https://")
          // ) {
          //   getProductAction(productDetails.id, "clicked");
          //   openInAppBrowser(productDetails?.product_link);
          // } else {
          //   Toast.show("Not valid Link");
          // }
          if (productDetails?.handle) {
            if (link && link.includes('https://')) {
              getEcomProductView(productDetails.id, 'clicked');
              console.log('browser link------------', link);
              openInAppBrowser(link);
            } else {
              Toast.show('Not valid Link');
            }
          } else if (
            productDetails?.product_link &&
            productDetails?.product_link.includes('https://')
          ) {
            getEcomProductView(productDetails.id, 'clicked');
            openInAppBrowser(productDetails?.product_link);
          } else {
            Toast.show('Not valid Link');
          }
        }}>
        <FAIcon name="shopping-cart" color={BaseColor.whiteColor} size={24} />
        <Text style={[styles.addToCartText, {color: BaseColor.whiteColor}]}>
          Buy now
        </Text>
      </TouchableOpacity>
    </View>
  );
}
