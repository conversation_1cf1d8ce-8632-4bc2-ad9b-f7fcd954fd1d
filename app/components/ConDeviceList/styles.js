/* eslint-disable quotes */
import { Dimensions, StyleSheet } from "react-native";
import BaseColor from "../../config/colors";
import { FontFamily } from "../../config/typography";

const styles = StyleSheet.create({
  root: {
    flex: 1,
    alignSelf: "center",
    borderRadius: 10,
    marginBottom: 24,
    backgroundColor: BaseColor.whiteColor,
  },
  editIcon: {
    position: "absolute",
    zIndex: 1,
    right: 20,
    top: 12,
    width: 40,
    height: 40,
    justifyContent: "center",
    borderRadius: 40,
    backgroundColor: BaseColor.whiteColor,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  img: {
    width: Dimensions.get("screen").width - 32,
    height: 300,
    borderRadius: 18,
  },
  textView: {
    position: "absolute",
    bottom: 0,
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  titleText: {
    fontSize: 24,
    color: BaseColor.blackColor,
    fontFamily: FontFamily.default,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 5,
    },
    shadowOpacity: 0.34,
    shadowRadius: 6.27,
    elevation: 10,
  },
  otherText: {
    fontSize: 14,
    color: BaseColor.textGrey,
    fontFamily: FontFamily.default,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 5,
    },
    shadowOpacity: 0.34,
    shadowRadius: 6.27,
    elevation: 10,
  },
});

export default styles;
