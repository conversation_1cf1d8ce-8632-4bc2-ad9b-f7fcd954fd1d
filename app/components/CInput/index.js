/* eslint-disable react/jsx-props-no-spreading */
import React, { useEffect, useRef, useState } from 'react';
import { Text, TextInput, TouchableOpacity, View } from 'react-native';
import FAIcon from 'react-native-vector-icons/FontAwesome';
import LinearGradient from 'react-native-linear-gradient';
import DateTimePicker from '@react-native-community/datetimepicker';
import DatePicker from 'react-native-date-picker';
import moment from 'moment';
import { useTheme } from '@react-navigation/native';
import styles from './styles';
import { CustomIcon } from '../../config/LoadIcons';
import { FontFamily } from '../../config/typography';
import BaseColors from '../../config/colors';
import { translate } from '../../lang/Translate';

// Remove font scale
Text.defaultProps = Text.defaultProps || {};
Text.defaultProps.allowFontScaling = false;

const CInput = React.forwardRef((props, ref) => {
  const colors = useTheme();
  const BaseColor = colors.colors;

  const {
    onSubmitEditing = () => {},
    placeholder = 'Default placeholder',
    onChangeText = () => {},
    onFocus = () => {},
    onBlur = () => {},
    isLastInput,
    returnKeyType,
    textInputWrapper,
    secureTextEntry,
    icon,
    rightIcon,
    rightIconStyle,
    rightIconName,
    showRightIcon,
    onShowPasswordpress,
    editable = true,
    value,
    colorStyle,
    keyboardType,
    placeholderTextColor = BaseColor.placeHolderColor,
    disabled = false,
    error,
    maxLength = 50,
    showCountryPicker = false,
    onCountrySelect,
    countryCode,
    containterStyle,
    inputStyle,
    textInputStyle,
    iconStyle,
    viewStyle,
    txtColor = BaseColor.blackColor,
    countryLabelColor = BaseColor.blackColor,
    multiline = false,
    disableTxtColor = 'rgba(0, 0, 0, 0.4)',
    iconColor,
    iconSize,
    iconName,
    keyboardAppearance,
    hideLeftIcon,
    showError,
    errorMsg,
    datePicker,
    selectDate,
    onDateChange,
    setstate,
    state,
    ...rest
  } = props;

  const [showDate, setshowDate] = useState(false);
  const dataPickRef = useRef();

  const [datePickerVisible, setDatePickerVisible] = useState(false);
  const toggleDatePicker = () => {
    // console.log('hatahata');
    setDatePickerVisible(!datePickerVisible); // Toggle date picker visibility
  };

  // console.log('setDatePicker-->', datePickerVisible);

  const [date, setDate] = useState(new Date());
  const [mode, setMode] = useState('date');
  const [show, setShow] = useState(false);

  const onChange = (event, selectedDate) => {
    const currentDate = selectedDate || date;
    setShow(Platform.OS === 'ios');
    setDate(currentDate);
    setstate({ ...state, dob: currentDate?.toLocaleDateString() });
  };

  const showMode = currentMode => {
    setShow(true);
    setMode(currentMode);
  };

  const showDatepicker = () => {
    if (Platform.OS !== 'ios') {
      showMode('date');
    } else {
      setShow(true);
    }
  };

  const isIOS = Platform.OS === 'ios';

  useEffect(() => {
    if (datePicker === true && state?.dob) {
      const dateString = state?.dob;
      let parts = dateString.split('/');
      let dateObject = new Date(parts[2], parts[1] - 1, parts[0]); // months are 0-based in
      setDate(dateObject);
      // setDate(date);
    }
  }, [datePicker, state?.dob]);

  return (
    <>
      <LinearGradient
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
        colors={['#0000', BaseColor.white20]}
        style={[
          styles.inputWrapper,
          {
            backgroundColor: BaseColor.whiteColor,
            borderColor: BaseColors.textGrey,
          },
          textInputWrapper,
          // {borderWidth: 1},
        ]}>
        {/* <View style={[viewStyle || styles.inputWrapper, textInputWrapper]}> */}
        {datePicker ? (
          <>
            {!isIOS ? (
              <>
                <TouchableOpacity onPress={showDatepicker}>
                  <View
                    style={{
                      height: 50,
                      justifyContent: 'center',
                      alignItems: 'center',
                      // paddingStart: 10,
                      color: value ? BaseColor.textGrey : BaseColor.textGrey,
                    }}>
                    <Text style={{ color: BaseColor.textGrey }}>
                      {state?.dob
                        ? date.toLocaleDateString()
                        : translate('dob')}
                    </Text>
                  </View>
                </TouchableOpacity>
                {show && (
                  <DateTimePicker
                    testID="dateTimePicker"
                    value={date}
                    mode={mode}
                    is24Hour={true}
                    display="default"
                    onChange={onChange}
                  />
                )}
              </>
            ) : (
              <>
                <View
                  style={{
                    height: 50,
                    justifyContent: 'center',
                    alignItems: 'flex-start',
                    paddingStart: 10,
                    color: value ? BaseColor.textGrey : BaseColor.textGrey,
                  }}>
                  <DateTimePicker
                    style={{}}
                    testID="dateTimePicker"
                    value={date}
                    mode={mode}
                    is24Hour={true}
                    display="default"
                    onChange={onChange}
                  />
                </View>
              </>
            )}
          </>
        ) : (
          <View
            style={{
              height: 50,
              flexDirection: 'row',
              alignItems: 'center',
              paddingStart: 24,
            }}>
            {!hideLeftIcon ? (
              <CustomIcon
                name={iconName || 'rocket'}
                size={18}
                color={iconColor || BaseColor.blackColor}
              />
            ) : null}
            <TextInput
              {...rest}
              ref={ref}
              selectionColor={BaseColor.black50}
              placeholder={placeholder}
              placeholderTextColor={placeholderTextColor}
              style={[
                styles.input,
                {
                  marginStart: hideLeftIcon ? 0 : 12,
                  color: BaseColor.textGrey,
                  // color: showError ? BaseColor.orange : BaseColor.whiteColor,
                },
                inputStyle,
                editable ? styles.colorInput : colorStyle,
              ]}
              onChangeText={onChangeText}
              blurOnSubmit={false}
              onSubmitEditing={onSubmitEditing}
              returnKeyType={returnKeyType || (isLastInput ? 'go' : 'next')}
              secureTextEntry={secureTextEntry}
              editable={editable}
              value={value}
              maxLength={maxLength}
              keyboardType={keyboardType}
              multiline={multiline}
              onFocus={onFocus}
              onBlur={onBlur}
            />
            {/* {!rightIcon && iconName ? (
              <TouchableOpacity
                style={{
                  marginRight: 20,
                  marginBottom: 5,
                  // backgroundColor: "red",
                  padding: 10,
                }}
                onPress={onShowPasswordpress}>
                <FAIcon
                  name={iconName || 'rocket'}
                  size={15}
                  color={iconColor || BaseColor.blackColor}
                />
              </TouchableOpacity>
            ) : null} */}
            {showRightIcon && rightIconName ? (
              <TouchableOpacity
                style={{
                  marginRight: 20,
                  marginBottom: 5,
                  // backgroundColor: "red",
                  padding: 10,
                }}
                onPress={onShowPasswordpress}>
                <FAIcon
                  name={rightIconName || 'rocket'}
                  size={15}
                  color={iconColor || BaseColor.blackColor}
                />
              </TouchableOpacity>
            ) : null}
            {showError && (
              <CustomIcon
                name="warning"
                size={18}
                color={iconColor || BaseColor.alertRed}
                style={{ marginEnd: 24 }}
              />
            )}
            {/* </View> */}
          </View>
        )}
      </LinearGradient>
      {showError ? (
        <Text
          style={{
            // fontFamily: FontFamily.default,
            textAlign: 'left',
            fontWeight: 'bold',
            color: BaseColor.alertRed,
            marginStart: 24,
            marginEnd: 24,
            marginTop: 4,
            fontSize: 12,
            letterSpacing: 1,
          }}>
          {errorMsg}
        </Text>
      ) : null}
    </>
  );
});

export default CInput;
