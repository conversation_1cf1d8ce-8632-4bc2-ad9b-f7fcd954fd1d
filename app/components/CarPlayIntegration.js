/**
 * CarPlay Integration Component
 * Connects CarPlay service with existing notification system
 */

import React, { useEffect } from 'react';
import { Platform } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import CarPlayService from '../services/CarPlayService';

const CarPlayIntegration = () => {
  const dispatch = useDispatch();

  // Get notification data from Redux store
  const notifications = useSelector(
    state => state.notification?.notifications || [],
  );
  const deviceStatus = useSelector(
    state => state.bluetooth?.deviceStatus || {},
  );
  const temperatureData = useSelector(
    state => state.device?.temperatureData || {},
  );
  const humidityData = useSelector(state => state.device?.humidityData || {});

  useEffect(() => {
    // Only initialize on iOS
    if (Platform.OS !== 'ios') {
      return;
    }

    // Initialize CarPlay service with delay to avoid startup crashes
    console.log('CarPlay integration component mounted');

    // CarPlay service is already initialized with delay in constructor
    // No need to do anything here

    // Cleanup on unmount
    return () => {
      try {
        CarPlayService.cleanup();
      } catch (error) {
        console.error('Error cleaning up CarPlay:', error);
      }
    };
  }, []);

  useEffect(() => {
    // Handle new notifications
    if (notifications.length > 0 && Platform.OS === 'ios') {
      const latestNotification = notifications[notifications.length - 1];
      handleNewNotification(latestNotification);
    }
  }, [notifications]);

  useEffect(() => {
    // Monitor device status changes
    if (Platform.OS === 'ios') {
      handleDeviceStatusChange(deviceStatus);
    }
  }, [deviceStatus]);

  useEffect(() => {
    // Monitor temperature changes
    if (Platform.OS === 'ios' && temperatureData.temperature) {
      handleTemperatureChange(temperatureData);
    }
  }, [temperatureData]);

  useEffect(() => {
    // Monitor humidity changes
    if (Platform.OS === 'ios' && humidityData.humidity) {
      handleHumidityChange(humidityData);
    }
  }, [humidityData]);

  /**
   * Handle new notification from the app
   */
  const handleNewNotification = notification => {
    if (!CarPlayService.isCarPlayConnected()) {
      return;
    }

    try {
      const { title, body, data } = notification;

      // Determine notification type based on data
      let notificationType = 'info';
      if (data?.type === 'critical' || data?.priority === 'high') {
        notificationType = 'critical';
      } else if (data?.type === 'warning') {
        notificationType = 'warning';
      }

      // Show notification in CarPlay
      CarPlayService.showNotification({
        title: title || 'ChillBaby',
        message: body || 'New notification',
        type: notificationType,
        onPress: () => handleNotificationPress(notification),
      });

      // If it's a critical baby monitoring alert, show as alert too
      if (
        data?.category === 'baby_monitor' &&
        notificationType === 'critical'
      ) {
        CarPlayService.showBabyAlert({
          type: data.alertType || 'general',
          temperature: data.temperature,
          humidity: data.humidity,
          timestamp: data.timestamp || new Date().toISOString(),
        });
      }
    } catch (error) {
      console.error('Error handling notification in CarPlay:', error);
    }
  };

  /**
   * Handle device status changes
   */
  const handleDeviceStatusChange = status => {
    if (!CarPlayService.isCarPlayConnected()) {
      return;
    }

    try {
      // Check for device disconnection
      if (
        status.connected === false ||
        status.connectionStatus === 'disconnected'
      ) {
        CarPlayService.showBabyAlert({
          type: 'device_disconnected',
          timestamp: new Date().toISOString(),
        });
      }

      // Check for low battery
      if (status.batteryLevel && status.batteryLevel < 20) {
        CarPlayService.showNotification({
          title: '🔋 Low Battery',
          message: `Device battery: ${status.batteryLevel}%`,
          type: 'warning',
        });
      }
    } catch (error) {
      console.error('Error handling device status in CarPlay:', error);
    }
  };

  /**
   * Handle temperature changes
   */
  const handleTemperatureChange = tempData => {
    if (!CarPlayService.isCarPlayConnected()) {
      return;
    }

    try {
      const { temperature, threshold } = tempData;

      // Check for temperature alerts
      if (threshold && threshold.high && temperature > threshold.high) {
        CarPlayService.showBabyAlert({
          type: 'temperature_high',
          temperature,
          timestamp: new Date().toISOString(),
        });
      } else if (threshold && threshold.low && temperature < threshold.low) {
        CarPlayService.showBabyAlert({
          type: 'temperature_low',
          temperature,
          timestamp: new Date().toISOString(),
        });
      }
    } catch (error) {
      console.error('Error handling temperature change in CarPlay:', error);
    }
  };

  /**
   * Handle humidity changes
   */
  const handleHumidityChange = humidityData => {
    if (!CarPlayService.isCarPlayConnected()) {
      return;
    }

    try {
      const { humidity, threshold } = humidityData;

      // Check for humidity alerts
      if (
        threshold &&
        ((threshold.high && humidity > threshold.high) ||
          (threshold.low && humidity < threshold.low))
      ) {
        CarPlayService.showBabyAlert({
          type: 'humidity_alert',
          humidity,
          timestamp: new Date().toISOString(),
        });
      }
    } catch (error) {
      console.error('Error handling humidity change in CarPlay:', error);
    }
  };

  /**
   * Handle notification press in CarPlay
   */
  const handleNotificationPress = notification => {
    console.log('CarPlay notification pressed:', notification);

    // You can dispatch actions here to navigate to specific screens
    // or perform other actions when notifications are pressed in CarPlay

    // Example: Navigate to alerts screen
    // dispatch(NavigationActions.navigate('Alerts'));
  };

  // This component doesn't render anything visible
  return null;
};

export default CarPlayIntegration;
