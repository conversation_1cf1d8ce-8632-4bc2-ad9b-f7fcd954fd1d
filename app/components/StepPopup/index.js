/* eslint-disable no-nested-ternary */
/* eslint-disable indent */
/* eslint-disable quotes */
import React from "react";
import { Image, Modal, Text, TouchableOpacity, View } from "react-native";
import Icon from "react-native-vector-icons/AntDesign";
import { translate } from "../../lang/Translate";
import BaseColor from "../../config/colors";

export default function StepPopup(props) {
  const {
    visible,
    descriptionText,
    descriptionText2,
    image,
    onClose = () => {},
    onNext = () => {},
    onTroubleshoot = () => {},
    trouble,
    settings,
    step8,
    step5,
    title,
    step7,
    step6,
  } = props;

  return (
    <Modal
      visible={visible}
      style={{ flex: 1, padding: 16, backgroundColor: BaseColor.whiteColor }}
      transparent
      animationType="fade"
    >
      <TouchableOpacity
        activeOpacity={1}
        style={{
          flex: 1,
          backgroundColor: "#00000050",
          justifyContent: "center",
          alignItems: "center",
          padding: 26,
          paddingTop: 44,
        }}
      >
        <View
          style={{
            backgroundColor: BaseColor.whiteColor,
            borderRadius: 12,
            width: "100%",
            paddingHorizontal: 5,
          }}
        >
          <TouchableOpacity
            style={{
              height: 50,
              width: 50,
              borderRadius: 25,
              backgroundColor: BaseColor.blueDark,
              position: "absolute",
              alignSelf: "center",
              top: -25,
              alignItems: "center",
              justifyContent: "center",
              zIndex: 100,
            }}
            onPress={onClose}
          >
            <Icon
              name="setting"
              style={{ color: BaseColor.whiteColor, fontSize: 30 }}
            />
          </TouchableOpacity>
          {/* <TouchableOpacity
            style={{
              height: 35,
              width: 35,
              borderRadius: 18,
              backgroundColor: BaseColor.blueDark,
              position: "absolute",
              right: -10,
              top: -15,
              alignItems: "center",
              justifyContent: "center",
              zIndex: 100,
            }}
            onPress={onClose}
          >
            <Icon
              name="md-close"
              style={{ color: BaseColor.whiteColor, fontSize: 20 }}
            />
          </TouchableOpacity> */}
          {!trouble && (
            <>
              {descriptionText === "step4b1" && (
                <Image
                  style={{
                    marginTop: 25,
                    marginBottom: 20,
                    width: 170,
                    height: 170,
                    borderRadius: 12,
                    alignSelf: "center",
                    position: "absolute",
                    top: 0,
                    zIndex: 999,
                  }}
                  source={require("../../assets/images/step4b.png")}
                  resizeMode="contain"
                />
              )}
              {image && (
                <Image
                  style={{
                    marginTop: 40,
                    marginBottom: 20,
                    width: step5 ? 260 : step7 ? 240 : 150,
                    height: step5 ? 300 : step7 ? 250 : 150,
                    borderRadius: 12,
                    alignSelf: "center",
                  }}
                  source={image}
                  resizeMode={step6 ? "contain" : "cover"}
                />
              )}
            </>
          )}
          {settings && (
            <Image
              style={{
                marginTop: 40,
                width: 240,
                height: 270,
                borderRadius: 12,
                alignSelf: "center",
                overflow: "hidden",
              }}
              source={image}
              resizeMode="cover"
            />
          )}
          {title && (
            <Text
              style={{
                marginHorizontal: 20,
                marginBottom: 10,
                color: BaseColor.textGrey,
                textAlign: "center",
                marginTop: 40,
              }}
            >
              {title}
            </Text>
          )}
          {descriptionText === "stepAllowBT" ? (
            <Text style={{ textAlign: "center", color: BaseColor.textGrey }}>
              {`${translate("please")} `}
              <Text style={{ fontWeight: "bold" }}>
                {`${translate("allowAccess")} `}
              </Text>
              {translate("stepAllowBT")}
            </Text>
          ) : descriptionText !== "step4b1" ? (
            <Text
              style={{
                marginHorizontal: 20,
                marginBottom: 10,
                color: BaseColor.textGrey,
                textAlign: "center",
                marginTop: trouble ? 40 : 0,
              }}
            >
              {descriptionText}
            </Text>
          ) : (
            <>
              <Text style={{ textAlign: "center", color: BaseColor.textGrey }}>
                {translate("step4b1")}
                <Text style={{ fontWeight: "bold", fontStyle: "italic" }}>
                  connect
                </Text>
                {translate("step4b2")}
              </Text>
            </>
          )}

          <Text
            style={{
              marginHorizontal: 20,
              marginBottom: 10,
              color: BaseColor.textGrey,
              textAlign: "center",
            }}
          >
            {descriptionText2}
          </Text>
          <View
            style={{
              paddingBottom: 12,
              marginHorizontal: 25,
            }}
          >
            {!trouble && (
              <TouchableOpacity
                style={{
                  backgroundColor: BaseColor.blueDark,
                  alignItems: "center",
                  padding: 12,
                  borderRadius: 10,
                  marginVertical: 4,
                  paddingHorizontal: 25,
                }}
                onPress={onNext}
              >
                <Text
                  style={{
                    color: BaseColor.whiteColor,
                    fontSize: 16,
                  }}
                >
                  {step8 ? translate("Finish") : translate("Next")}
                </Text>
              </TouchableOpacity>
            )}
            {trouble && (
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                }}
              >
                <TouchableOpacity
                  style={{
                    borderColor: BaseColor.blueDark,
                    borderWidth: 1,
                    backgroundColor: BaseColor.whiteColor,
                    alignItems: "center",
                    justifyContent: "center",
                    flex: 1,
                    padding: 12,
                    borderRadius: 10,
                    marginVertical: 4,
                    marginEnd: 10,
                  }}
                  onPress={onNext}
                >
                  <Text
                    style={{
                      color: BaseColor.blueDark,
                      fontSize: 16,
                    }}
                  >
                    {settings ? translate("Next") : translate("Back")}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={{
                    backgroundColor: BaseColor.blueDark,
                    alignItems: "center",
                    flex: 1,
                    padding: 12,
                    borderRadius: 10,
                    marginVertical: 4,
                    marginStart: 10,
                  }}
                  onPress={onTroubleshoot}
                >
                  <Text
                    style={{
                      color: BaseColor.whiteColor,
                      fontSize: 16,
                      textAlign: "center",
                    }}
                  >
                    {settings
                      ? translate("Settings")
                      : translate("Troubleshoot")}
                  </Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    </Modal>
  );
}
