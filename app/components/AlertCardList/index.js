/* eslint-disable no-tabs */
/* eslint-disable quotes */
import React, { useEffect } from "react";
import {
  Text, View, TouchableOpacity, Image
} from "react-native";
import FAIcon from "react-native-vector-icons/FontAwesome5";
import MCIcon from "react-native-vector-icons/MaterialCommunityIcons";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from "react-native-reanimated";
import { useTheme } from "@react-navigation/native";
import moment from "moment";
import { SvgXml, SvgUri } from "react-native-svg";
import { isEmpty } from "lodash";
import { CustomIcon } from "../../config/LoadIcons";
import styles from "./styles";
// import { translate } from "../../lang/Translate";

const AlertCard = (props) => {
  const {
    title,
    onPress = () => {},
    time,
    rightIcon,
    altMsg,
    icon,
    // childName,
    animTime = 1000,
  } = props;

  const colors = useTheme();
  const BaseColor = colors.colors;

  const listAlertAnim = useSharedValue(0);
  const opacityAnim = useSharedValue(0);

  const alertStyleAnim = useAnimatedStyle(() => ({
    transform: [
      {
        translateX: withTiming(listAlertAnim.value, {
          duration: animTime,
        }),
      },
    ],
    opacity: withTiming(opacityAnim.value, {
      duration: animTime,
    }),
  }));

  useEffect(() => {
    opacityAnim.value = 1;
    listAlertAnim.value = -600;
  }, []);

  const xml = `<svg xmlns="http://www.w3.org/2000/svg" width="17.431" height="20.74" viewBox="0 0 17.431 20.74">
          <g id="Group_3803" data-name="Group 3803" transform="translate(-3224.647 20895.377)">
            <g id="car-seat" transform="translate(3224.647 -20895.377)">
              <path id="Path_12615" data-name="Path 12615" d="M280.841,362.795a16.72,16.72,0,0,0-3.651-.55,2.137,2.137,0,0,0,.118-1.688.771.771,0,0,0-.667-.353H276.6v-.864h.55a.871.871,0,0,0,.864-.864v-3.219a.354.354,0,0,0-.039-.2,4.126,4.126,0,0,0,3.455-2.159,3.982,3.982,0,0,0-.314-3.573,2.445,2.445,0,0,0-2.12-1.178h-7.617a2.27,2.27,0,0,0-2.12,1.256,4.029,4.029,0,0,0,.079,3.73,4.52,4.52,0,0,0,3.3,1.885c0,.078-.039.118-.039.2v3.219a.871.871,0,0,0,.864.864h.589v.864a.736.736,0,0,0-.707.393,1.821,1.821,0,0,0,.157,1.61,11.992,11.992,0,0,0-3.73.667.39.39,0,0,0-.2.51.377.377,0,0,0,.353.236.236.236,0,0,0,.157-.039c1.963-.864,7.97-.785,10.443-.039a.348.348,0,0,0,.471-.275A.259.259,0,0,0,280.841,362.795Zm-5.457-3.651a.842.842,0,0,0,.471.157v.864h-.9v-.9A.443.443,0,0,0,275.383,359.144Zm-1.885-4.829V348.9h3.808v5.418c-.236,0-.471.039-.707.039h-2.395A4.763,4.763,0,0,0,273.5,354.315Zm3.691.785a.084.084,0,0,1,.079.079V358.4a.084.084,0,0,1-.079.079h-1.335a.084.084,0,0,1-.079-.079v-3.219a.084.084,0,0,1,.079-.079h1.335Zm3.337-5.379a3.139,3.139,0,0,1,.236,2.827,3.3,3.3,0,0,1-2.709,1.688V348.9h1.021A1.661,1.661,0,0,1,280.527,349.721ZM270.083,352.7a3.221,3.221,0,0,1-.039-2.945,1.564,1.564,0,0,1,1.413-.864h1.256v5.339A3.764,3.764,0,0,1,270.083,352.7Zm3.416,5.732v-3.219a.084.084,0,0,1,.079-.079h1.3a.084.084,0,0,1,.079.079v3.219a.084.084,0,0,1-.079.079h-1.335A.137.137,0,0,1,273.5,358.437Zm.589,2.552h2.552a2.821,2.821,0,0,1-.275,1.139H274.4A2.694,2.694,0,0,1,274.088,360.989Zm9.619-.746a2.365,2.365,0,0,0-1.963-1.178.57.57,0,0,1-.236-.039c-.078-.236.393-1.06.707-1.688a10.7,10.7,0,0,0,1.531-4.044c.353-3.926-.55-6.007-2.827-6.6-1.531-.393-9.619-.471-11.229,0-2.2.628-3.062,2.788-2.67,6.6a10.856,10.856,0,0,0,1.57,3.926c.353.628.864,1.531.785,1.767a.8.8,0,0,1-.314.039,2.358,2.358,0,0,0-2.081.9c-.51.785-.432,1.924.2,3.376l.118.275c.55,1.256,1.413,3.337,3.73,3.455.55.039,2.2.079,4.358.079h0c2.12,0,3.219-.079,3.769-.079h.118c2.513-.118,3.3-1.727,4-3.141a4.293,4.293,0,0,1,.314-.589A2.972,2.972,0,0,0,283.707,360.243Zm-.824,2.788c-.118.2-.2.393-.314.589-.667,1.335-1.335,2.591-3.376,2.709h-.118c-.55.039-1.649.079-3.73.079-2.159,0-3.769-.079-4.319-.079-1.806-.079-2.552-1.845-3.062-2.984l-.118-.275a3.114,3.114,0,0,1-.275-2.63c.236-.393.707-.55,1.413-.55a1.029,1.029,0,0,0,.982-.432c.353-.55-.118-1.374-.746-2.552a10.182,10.182,0,0,1-1.453-3.651c-.393-3.416.275-5.222,2.081-5.771a29.552,29.552,0,0,1,5.065-.275,35.974,35.974,0,0,1,5.732.314c1.178.275,2.67,1.217,2.238,5.771a10.279,10.279,0,0,1-1.453,3.73c-.628,1.139-1.021,1.924-.707,2.473a.952.952,0,0,0,.9.393,1.555,1.555,0,0,1,1.3.785C283.275,361.028,283.55,361.853,282.882,363.031Z" transform="translate(-266.647 -346.373)" fill="#ea542d"/>
            </g>
          </g>
        </svg>`;
  return (
    <Animated.View style={[styles.renderView, { left: 600 }, alertStyleAnim]}>
      <View
        style={[styles.cardView, { backgroundColor: BaseColor.whiteColor }]}
      >
        <View style={styles.touchableContent}>
          <View
            style={[styles.icons, { backgroundColor: BaseColor.whiteColor }]}
          >
            {isEmpty(icon) ? (
              <SvgXml xml={xml} width={22} height={22} />
            ) : (
              <Image
                source={{
                  uri:
                    icon,
                }}
                style={{
                  height: 32,
                  width: 32,
                }}
              />
            )}
            <View
              style={{
                ...styles.icons,
                position: "absolute",
                width: 18,
                height: 18,
                backgroundColor: BaseColor.org,
                bottom: 0,
                right: 0,
                borderRadius: 50,
              }}
            >
              <MCIcon name="sine-wave" size={8} color={BaseColor.whiteColor} />
            </View>
            {/* )} */}
          </View>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={onPress}
            style={{
              flex: 1,
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <View style={styles.contentTextView}>
              <Text style={[styles.title, { color: BaseColor.blackColor }]}>
                {title}
              </Text>
              {/* <Text style={[styles.time, { color: BaseColor.textGrey }]}>
                {childName}
              </Text> */}
              <Text style={styles.time}>{moment(time).fromNow()}</Text>
              {altMsg ? <Text style={styles.altMsg}>{altMsg}</Text> : null}
            </View>
            <FAIcon name={rightIcon} size={16} color={BaseColor.textGrey} />
          </TouchableOpacity>
        </View>
      </View>
    </Animated.View>
  );
};

export default AlertCard;
