/* eslint-disable no-nested-ternary */
/* eslint-disable indent */
/* eslint-disable quotes */
import { isArray, isEmpty, isObject, isString } from "lodash";
import React from "react";
import {
  Image,
  Modal,
  Text,
  TouchableOpacity,
  View,
  Linking,
} from "react-native";
import Icon from "react-native-vector-icons/AntDesign";
import BaseColor from "../../config/colors";
import { translate } from "../../lang/Translate";

export default function ChildProfilePopup(props) {
  const { visible, title, image, position, onClose = () => {}, detail } = props;

  function handleUrl(item) {
    const url = item?.button_url ? item?.button_url : "";
    if (url !== "") {
      Linking.canOpenURL(url).then((supported) => {
        if (!supported) {
          console.error("No handler for URL:", url);
        } else {
          Linking.openURL(url);
        }
      });
    }
    onClose();
  }
  function NextPressed() {
    onClose();
  }

  return (
    <Modal
      visible={visible}
      style={{ flex: 1, padding: 16 }}
      transparent
      animationType="fade"
    >
      <TouchableOpacity
        activeOpacity={1}
        style={{
          flex: 1,
          backgroundColor: "#00000050",
          justifyContent:
            position === "bottom"
              ? "flex-end"
              : position === "top"
              ? "flex-start"
              : "center",
          alignItems: "center",
          padding: 16,
          paddingTop: position === "top" ? 55 : 44,
        }}
      >
        <View
          style={{
            backgroundColor: "#fafafa",
            // padding: 12,
            borderRadius: 12,
            width: "100%",
            // height: position === "full" ? "100%" : "55%",
            // justifyContent: "space-around",
          }}
        >
          <TouchableOpacity
            style={{
              height: 50,
              width: 50,
              borderRadius: 25,
              backgroundColor: BaseColor.blueDark,
              position: "absolute",
              alignSelf: "center",
              top: -25,
              alignItems: "center",
              justifyContent: "center",
              zIndex: 100,
            }}
            onPress={onClose}
          >
            <Icon
              name="setting"
              style={{ color: BaseColor.whiteColor, fontSize: 30 }}
            />
          </TouchableOpacity>
          {/* <TouchableOpacity
            style={{
              height: 35,
              width: 35,
              borderRadius: 18,
              backgroundColor: BaseColor.blueDark,
              position: "absolute",
              right: -10,
              top: -15,
              alignItems: "center",
              justifyContent: "center",
              zIndex: 100,
            }}
            onPress={onClose}
          >
            <Icon
              name="md-close"
              style={{ color: BaseColor.whiteColor, fontSize: 20 }}
            />
          </TouchableOpacity> */}
          <Image
            style={{
              width: 280,
              height: 300,
              borderRadius: 12,
              alignSelf: "center",
              marginTop: 35,
              marginBottom: 10,
            }}
            source={image}
            resizeMode="cover"
          />
          <Text style={{ marginHorizontal: 20, color: BaseColor.textGrey }}>
            {translate("childProPopup")}
          </Text>
          <Text
            style={{
              marginHorizontal: 20,
              color: BaseColor.textGrey,
              marginVertical: 10,
            }}
          >
            {translate("childProPopup2")}
          </Text>
          <View
            style={{
              paddingBottom: 12,
              alignItems: "center",
            }}
          >
            <TouchableOpacity
              style={{
                backgroundColor: BaseColor.blueDark,
                alignItems: "center",
                padding: 8,
                borderRadius: 10,
                marginVertical: 4,
                paddingHorizontal: 25,
              }}
              onPress={() => NextPressed()}
            >
              <Text
                style={{
                  color: BaseColor.whiteColor,
                  fontSize: 16,
                }}
              >
                {translate("Next")}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </TouchableOpacity>
    </Modal>
  );
}
