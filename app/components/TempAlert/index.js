import React, { useEffect, useState, useRef } from 'react';
import {
  Alert,
  AppState,
  NativeEventEmitter,
  NativeModules,
  PermissionsAndroid,
  Platform,
} from 'react-native';
import _, {
  debounce,
  find,
  findIndex,
  flattenDeep,
  isArray,
  isEmpty,
  isObject,
  isString,
  isUndefined,
  map,
  throttle,
  toNumber,
} from 'lodash';
import { useDispatch, useSelector } from 'react-redux';
import moment from 'moment';
import PushNotification from 'react-native-push-notification';
import GetLocation from 'react-native-get-location';
import Toast from 'react-native-simple-toast';
import BleManager from 'react-native-ble-manager';
import { BleManager as BM } from 'react-native-ble-plx';
import BluetoothStateManager from 'react-native-bluetooth-state-manager';
import { bytesToString } from 'convert-string';
import {
  check,
  PERMISSIONS,
  requestMultiple,
  request,
} from 'react-native-permissions';
// import BackgroundTimer from "react-native-background-timer";
import BackgroundTimer from 'react-native-background-timer';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Video from 'react-native-video';
import Torch from 'react-native-torch';
import CAlert from '../CAlert';
import BluetoothActions from '../../redux/reducers/bluetooth/actions';
import AuthActions from '../../redux/reducers/auth/actions';
import { translate } from '../../lang/Translate';
import { getApiData } from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import {
  getSWValue,
  getUpdatedList,
  send_to_server,
  sendErrorReport,
} from '../../utils/commonFunction';

const BleManagerModule = NativeModules.BleManager;
const bleManagerEmitter = new NativeEventEmitter(BleManagerModule);

const manager = new BM({
  restoreStateIdentifier: 'BleInTheBackground',
  restoreStateFunction: restoredState => {
    if (restoredState == null) {
      console.log('BleManager was constructed for the first time');
    } else {
      console.log(
        'BleManager was restored. Check `restoredState.connectedPeripherals` property.',
      );
    }
  },
});

// let parseBleData = {};
let interval = null;
/**
 *
 *@module SeatAlerts
 *
 */
const TempAlert = (props, { navigation }) => {
  const peripherals = new Map();
  const navRef = props?.navigation?.current;
  const currentScreen = props?.navigation?.current?.getCurrentRoute();
  const [isReadData, setIsReadData] = useState(true);
  const dispatch = useDispatch();
  const {
    setAlertTime,
    setEmergencyAlert,
    setTempModal,
    setHumdtModal,
    setIsConvert,
    setConnectedDeviceDetails,
    setConnectedDeviceDetail,
    setLeftChildAlert,
    setDeviceDetail,
    set60secTimeOut,
    setAlertDataRedux,
    setIsConnected,
    setDeviceDisconnect,
    setIsBleConnected,
  } = BluetoothActions;
  const [visible, setVisible] = useState(false);
  const visibleRef = useRef(null);
  visibleRef.current = visible;
  const step8Done = useSelector(state => state.auth.step8Done);
  const [alertData, setAlertData] = useState({
    name: '',
    title: '',
    message: '',
    type: '',
  });
  const {
    lowAlert,
    highAlert,
    lowHAlert,
    highHAlert,
    bleData,
    alertTime,
    emergencyAlert,
    isBleConnected,
    connectedDeviceDetail,
    connectedDeviceDetails,
    activeChildDetail,
    deviceID,
    lastDeviceId,
    tempModal,
    humdtModal,
    serviceID,
    characteristicID,
    isCurrentActiveDevice,
    activeDeviceId,
    deviceDetail,
    isConnecting,
    isHomeDeviceClick,
    isSetMtu,
    isChildProductId,
    isLeftChildAlert,
    is60secTimeOut,
    smsSentAndroidBg,
    cancelSMS,
    deletedRecent,
    isFromScanner,
    isConnected,
    deviceDisconnect,
  } = useSelector(state => state.bluetooth);
  const locationDisclouser = useSelector(
    state => state.auth.locationDisclouser,
  );
  const bleDataRef = useRef();
  bleDataRef.current = bleData;

  const [appState, setAppState] = useState(true);
  const [timer, setTimer] = React.useState(60);
  const [isFlashLight, setIsFlashlight] = useState(false);
  const nowTime = moment();
  const durationTemp = moment.duration(nowTime.diff(alertTime.temp));
  const durationHum = moment.duration(nowTime.diff(alertTime.hum));
  const batteryAlert = moment.duration(nowTime.diff(alertTime.voc));
  const accessToken = useSelector(state => state.auth.accessToken);
  const languageData = useSelector(state => state.language);
  const [isCheckBattery, setCheckBattery] = useState(false);
  const [startRead, setstartRead] = useState(false);
  const [isSend, setIsSend] = useState(true);
  const [isChange, setIsChange] = useState(false);
  const [sound, setSound] = useState(false);
  const [is60done, setis60done] = useState(false);
  const [BPermissionOk, setBPermission] = useState(Platform.OS === 'ios'); /// false this for android
  const [productConnect, setProductConnect] = useState(false);
  const [powerDetail, setPowerDetail] = useState({
    powerTime: null,
    value: null,
  });
  const [emergencyAlertTimeout, setEmergencyAlertTimeout] = useState(false);
  const [state, setSWState] = useState({
    key: '',
    value: null,
    isDisCon: true,
    isAutoConnect: '',
  });
  const playerRef = useRef();
  const [deviceList, setDeviceList] = useState([]);

  useEffect(() => {
    console.log('After Disconnecte Comes to this function 111');
    if (!isBleConnected && !emergencyAlert) {
      console.log('After Disconnecte  111');
      BluetoothStateManager.getState().then(bluetoothState => {
        switch (bluetoothState) {
          case 'Unknown':
          case 'Resetting':
          case 'Unsupported':
          case 'Unauthorized':
          case 'PoweredOff':
            Toast.show(translate('turnOnBle'));
            if (Platform.OS === 'android') {
              check(PERMISSIONS.ANDROID.BLUETOOTH_CONNECT).then(res => {
                console.log('BLUETOOTH_CONNECT---', res);
                if (res === 'granted') {
                  BluetoothStateManager.requestToEnable().then(result => {
                    console.log(
                      'BluetoothStateManager.requestToEnable -> result',
                      result,
                    );
                  });
                }
              });
              request(PERMISSIONS.ANDROID.BLUETOOTH_CONNECT)
                .then(result => {
                  console.log('BLUETOOTH_CONNECT----1', result);
                })
                .then(statuses => {
                  console.log(
                    'BLUETOOTH_CONNECT--2',
                    statuses[PERMISSIONS.ANDROID.BLUETOOTH_CONNECT],
                  );
                });
            } else {
              BluetoothStateManager.openSettings();
            }
            break;
          case 'PoweredOn':
            break;
          default:
            break;
        }
      });
    }
  }, []);
  useEffect(() => {
    console.log('After Disconnecte Comes to this function 111');
    if (Platform.OS === 'android' && Platform.Version >= 31) {
      console.log('After Disconnecte  222');
      check(PERMISSIONS.ANDROID.BLUETOOTH_SCAN).then(res => {
        console.log('BLUETOOTH_SCAN---', res);
        if (res === 'granted') {
          setBPermission(true);
          // sendErrorReport(true, "BLUETOOTH_SCAN");
        }
      });
      request(PERMISSIONS.ANDROID.BLUETOOTH_SCAN)
        .then(result => {
          console.log('BLUETOOTH_SCAN----1', result);
        })
        .then(statuses => {
          console.log(
            'BLUETOOTH_SCAN--2',
            statuses[PERMISSIONS.ANDROID.BLUETOOTH_SCAN],
          );
        });
    } else {
      setBPermission(true);
    }
  }, []);
  async function getDeviceList() {
    const headers = {
      'Content-Type': 'application/json',
      authorization: accessToken ? `Bearer ${accessToken}` : '',
    };

    const newObj = {
      type: 'add',
    };

    const data = {
      platform: Platform.OS,
    };
    console.log('api called 1111');
    try {
      const response = await getApiData(
        BaseSetting.endpoints.connectedDevice,
        'POST',
        data,
        headers,
      );
      console.log('api called 2222---->>>>', response);
      // sendErrorReport(response, "response__childinFoList _ list");
      if (response.success && isArray(response.data)) {
        dispatch(setConnectedDeviceDetails(flattenDeep(response.data)));
        dispatch(setConnectedDeviceDetail(flattenDeep(response.data)));
        dispatch(setDeviceDisconnect(false));
        const arr = response.data;
        setDeviceList(arr);
      } else {
        Toast.show(response.message);
      }
    } catch (error) {
      console.log('error for device list ===', error);
      sendErrorReport(error, 'get_device_l');
    }
  }

  const getChildInfo = async isReadDataFalse => {
    const headers = {
      'Content-Type': 'application/json',
      authorization: accessToken ? `Bearer ${accessToken}` : '',
    };
    console.log('getChildInfo -> headers', headers);

    getApiData(
      BaseSetting.endpoints.getUserChild,
      'POST',
      {
        platform: Platform.OS === 'ios' ? 'IOS' : 'ANDROID',
      },
      headers,
    )
      .then(response => {
        // sendErrorReport(response, "response__childinFo");
        if (response.success) {
          const childArr = response.data;
          const selectedChild = childArr.find(
            item => item.product_id === deviceID,
          );
          // console.log("childdd aaaa", childArr);

          console.log('childddd', selectedChild.device_connection);
          if (
            !isReadDataFalse &&
            !isBleConnected &&
            selectedChild?.device_connection === 'Active'
          ) {
            sendErrorReport(deviceID, 'read_data_device_again');
            if (deviceID && !isBleConnected) {
              sendErrorReport(deviceID, 'read_data_deviceID_again');
              console.log('read data again');
              readData();
            } else {
              if (isBleConnected) {
                dispatch(BluetoothActions.setDeviceID(''));
              }
              setstartRead(false);
            }
          }

          dispatch(setDeviceDetail(response.data));
        } else {
          Toast.show(response.message);
        }
      })
      .catch(err => {
        // console.log("ERRR", err);
        Toast.show('Something went wrong while getting child detail');
        sendErrorReport(err, 'get_child_in_device4');
      });
  };
  // useEffect(() => {
  //   if (timer > 0 && !appState) {
  //     interval = setInterval(() => setTimer((p) => p - 1), 1000);
  //     setIsFlashlight(!isFlashLight);
  //   }
  // }, [timer]);

  // useEffect(() => {
  //   if (timer === 0 && !appState) {
  //     clearInterval(interval);
  //     setIsFlashlight(false);
  //     Torch.switchState(false);
  //   }
  // }, [timer]);

  // useEffect(() => {
  //   if (!appState) {
  //     Torch.switchState(isFlashLight);
  //   }
  // }, [isFlashLight]);

  // useEffect(() => {
  //   if (emergencyAlert && !isBleConnected && isSend) {
  //     // sendErrorReport(isFlashLight, "isFlash1");
  //     setTimeout(() => {
  //       setIsFlashlight(!isFlashLight);
  //     }, 500);
  //   } else {
  //     // sendErrorReport(isFlashLight, "isFlash2");
  //     // Torch.switchState(false);
  //   }
  // }, [isFlashLight, emergencyAlert, isBleConnected, isSend]);

  // useEffect(() => {
  //   if (isSend) {
  //     // Torch.switchState(isFlashLight);
  //   } else {
  //     // Torch.switchState(false);
  //   }
  // }, [isFlashLight, isSend]);
  // const soundTon = "../../config/Siren.mp3";

  // For Checking if data submited to API
  // const [isBleDataSubmited, setIsBleDataSubmited] = useState(0);

  // parseBleData =
  // bleDataRef.current &&
  // !isEmpty(bleDataRef.current) &&
  // !isObject(bleDataRef.current) &&
  // isString(bleDataRef.current)
  //   ? JSON.parse(bleDataRef.current)
  //   : {};

  const [parseBleData, setParseBleData] = useState(send_to_server());

  useEffect(() => {
    const intervalId = setInterval(() => {
      const randomBle = send_to_server();
      setParseBleData(randomBle);
      // dispatch(setIsBleConnected(true));
    }, 10000);
    return () => clearInterval(intervalId);
  }, [parseBleData]);

  const temp =
    isObject(parseBleData) &&
    !isEmpty(parseBleData) &&
    parseBleData?.Temperature
      ? parseBleData?.Temperature - 2
      : parseBleData.TEMP - 2 || 0;
  const humdt =
    isObject(parseBleData) && !isEmpty(parseBleData) && parseBleData?.Humidity
      ? parseBleData?.Humidity
      : parseBleData.HUMDT || 0;
  const batteryVoltage =
    isObject(parseBleData) && !isEmpty(parseBleData) && parseBleData?.BV
      ? parseBleData?.BV
      : parseBleData.bv || 0;

  // const SW = getSWValue(parseBleData, connectedDeviceDetail);
  const retrieveConnected = async deviceId =>
    new Promise(resolve => {
      BleManager.getConnectedPeripherals([]).then(results => {
        console.log(results);
        let matched = false;
        if (results.length === 0) {
          console.log('No connected peripherals');
          // sendErrorReport(true, "60secTimeOut-false-no");
          // dispatch(set60secTimeOut(false));
          AsyncStorage.setItem('async60secTimeout', 'false');
          matched = false;
        }
        for (let i = 0; i < results.length; i++) {
          const peripheral = results[i];
          if (deviceId === peripheral?.id) {
            sendErrorReport(peripheral, 'read_data_peripheral');
            matched = true;
          }
          // peripheral.connected = true;
          // peripherals.set(peripheral.id, peripheral);
          // setList(Array.from(peripherals.values()));
        }
        resolve(matched);
      });
    });

  // useEffect(() => {
  //   dispatch(AuthActions.setCurrentScreen(currentScreen?.name));
  // }, [currentScreen]);

  useEffect(() => {
    console.log('After Disconnecte Comes to this function 3333');
    sendErrorReport(deviceID, 'read_data_deviceID1');
    if (deviceID) {
      console.log('After Disconnecte 3333');
      sendErrorReport(deviceID, 'read_data_deviceID');
      readData();
    } else {
      console.log('After Disconnecte 444');
      if (isBleConnected) {
        dispatch(BluetoothActions.setDeviceID(''));
      }
      setstartRead(false);
    }
  }, [deviceID, isHomeDeviceClick]);

  useEffect(() => {
    console.log('After Disconnecte Comes to this function 4444');
    // sendErrorReport(step8Done, "temp_alerts_isCurrent");
    Object.keys(isCurrentActiveDevice).forEach(key => {
      const multiBleData = JSON.parse(isCurrentActiveDevice[key]);
      const multiSW = getSWValue(multiBleData);
      const nData = find(
        connectedDeviceDetail,
        item => item?.product_id === key,
      );
      if (!isUndefined(nData)) {
        deviceName = nData?.device_bluetooth_name;
      }
      // const battery = batteryVoltage > 2.75 ? 100 : ((batteryVoltage - 1.8) * 100) / 0.69;
      const battery =
        batteryVoltage === 296
          ? 90
          : batteryVoltage === 295
          ? 80
          : batteryVoltage >= 293
          ? 70
          : batteryVoltage >= 291
          ? 60
          : batteryVoltage >= 285
          ? 50
          : batteryVoltage >= 277
          ? 40
          : batteryVoltage >= 271
          ? 30
          : batteryVoltage >= 261
          ? 20
          : batteryVoltage >= 240
          ? 10
          : batteryVoltage >= 200
          ? 0
          : 100;
      // sendErrorReport(humdt, "humdt_parse");
      // sendErrorReport(JSON.parse(step8Done), "step8Donehu");
      // sendErrorReport(highHAlert, "highHAlert");
      // sendErrorReport(lowHAlert, "lowHAlert");
      if (
        temp !== 0 &&
        (temp > Number(highAlert) || temp < Number(lowAlert)) &&
        !visible &&
        !isConnecting &&
        isBleConnected &&
        step8Done
      ) {
        console.log('After Disconnecte 5555');
        // sendErrorReport(temp, "temp_alerts_higgh");
        const message =
          temp > Number(highAlert)
            ? `${translate('highAlertMsg1')} ${
                !isEmpty(childName) ? childName : translate('yourchild')
              }, ${translate('highAlertMsg2')}`
            : `${translate('lowAlertMsg1')} ${
                !isEmpty(childName) ? childName : translate('yourchild')
              }, ${translate('lowAlertMsg2')}`;
        if (alertTime.temp === 0 || durationTemp.minutes() >= 1) {
          console.log('After Disconnecte 6666');
          // sendErrorReport(temp, "temp_alerts_minitus");
          if (!isEmpty(nData)) {
            console.log('After Disconnecte 7777');
            setAlertData({
              // name: nData?.device_bluetooth_name,
              name:
                nData?.device_bluetooth_name === 'Baby_Auto1'
                  ? 'LINKO'
                  : nData?.device_bluetooth_name,
              title: `${translate('tempAlertTitle')}`,
              message,
              type: '',
            });
            setVisible(true);
            dispatch(setAlertTime({ ...alertTime, temp: moment() }));
            // addAlert("Temperature alert", message);
          }
        }
      } else if (
        humdt !== 0 &&
        (humdt > Number(highHAlert) || humdt < Number(lowHAlert)) &&
        !visible &&
        isBleConnected &&
        !isConnecting &&
        step8Done
      ) {
        console.log('After Disconnecte 8888');
        const message =
          humdt > Number(highHAlert)
            ? `${translate('highHumidity1')} ${
                !isEmpty(childName) ? childName : translate('yourchild')
              }, ${translate('highHumidity2')}`
            : `${translate('lowHumidity1')} ${
                !isEmpty(childName) ? childName : translate('yourchild')
              }, ${translate('lowHumidity2')}`;
        if (alertTime.hum === 0 || durationHum.minutes() >= 1) {
          console.log('After Disconnecte 99999');
          if (!isEmpty(nData)) {
            setAlertData({
              // name: nData?.device_bluetooth_name,
              name:
                nData?.device_bluetooth_name === 'Baby_Auto1'
                  ? 'LINKO'
                  : nData?.device_bluetooth_name,
              title: translate('humdtAlertTitle'),
              message,
              type: '',
            });
            setVisible(true);
            dispatch(setAlertTime({ ...alertTime, hum: moment() }));
            // addAlert("Temperature alert", message);
          }
        }
      } else if (battery <= 20 && !visible) {
        console.log('After Disconnecte 10010101');
        if (
          (alertTime?.voc === 0 || batteryAlert.minutes() >= 30) &&
          !visible &&
          step8Done
        ) {
          setAlertData({
            // name: nData?.device_bluetooth_name,
            name:
              nData?.device_bluetooth_name === 'Baby_Auto1'
                ? 'LINKO'
                : nData?.device_bluetooth_name,
            title: translate('batteryAlertTitle'),
            message: translate('batteryLowMsg'),
            type: '',
          });
          setVisible(true);
          dispatch(setAlertTime({ ...alertTime, voc: moment() }));
          addAlert('Battery alert', 'Battery is lower then 20%');
        }
      }
      const powerAlert = moment.duration(nowTime.diff(powerDetail.powerTime));
      if (powerAlert && powerAlert.seconds() >= 60) {
        console.log('After Disconnecte #####');
        setTimeout(() => {
          setAlertData({
            // name: nData?.device_bluetooth_name,
            name:
              nData?.device_bluetooth_name === 'Baby_Auto1'
                ? 'LINKO'
                : nData?.device_bluetooth_name,
            title: translate('powerDownTitle'),
            message: translate('powerDownMessage'),
            type: '',
          });
          setVisible(true);
          if (!appState) {
            pushNotification('Power Down', 'Powering down');
          }
          addAlert('Power Down', 'Powering down');
        }, 500);
      }
      if (
        temp !== 0 &&
        (temp > Number(highAlert) || temp < Number(lowAlert)) &&
        // !appState &&
        step8Done
      ) {
        console.log('After Disconnecte ---------->>>>1111');
        if (alertTime.temp === 0 || durationTemp.minutes() >= 1) {
          if (!isEmpty(nData)) {
            const message =
              temp > Number(highAlert)
                ? translate('highAlertMsg')
                : translate('lowTempAlertMsg');
            dispatch(setAlertTime({ ...alertTime, temp: moment() }));
            pushNotification(translate('tempAlertTitle'), message);
            addAlert(
              'Temperature alert',
              temp > Number(highAlert)
                ? `The temperature is high around ${
                    !isEmpty(childName) ? childName : 'your child'
                  } , please ensure they are safe and comfortable.`
                : `The temperature is low around ${
                    !isEmpty(childName) ? childName : 'your child'
                  }, please ensure they are safe and comfortable.`,
            );
          }
        }
      } else if (battery <= 20) {
        if (
          (alertTime.voc === 0 || batteryAlert.minutes() >= 10) &&
          !appState
        ) {
          console.log('After Disconnecte ---------->>>>22222');
          dispatch(setAlertTime({ ...alertTime, voc: moment() }));
          pushNotification(
            translate('batteryAlertTitle'),
            translate('batteryLowMsg'),
          );
          addAlert('Battery alert', 'Battery is lower then 20%');
        }
      }
    });
  }, [isCurrentActiveDevice, parseBleData]);

  useEffect(() => {
    console.log('After Disconnecte Comes to this function 5555');
    // console.log("left seat alert===>>hhhhh---", state.value);
    // sendErrorReport(state?.value, "state.value");
    const dName = find(
      connectedDeviceDetail,
      item => item?.product_id === state?.key && item?.connected === 1,
    );
    if (!isUndefined(dName)) {
      childName = dName?.nick_name;
    }
    // handle power alert
    if (powerDetail?.value !== state?.value) {
      setTimeout(() => {
        setPowerDetail({ powerTime: moment(), value: state.value });
      }, 500);
    }
    // sendErrorReport(step8Done, "step_8_done");
    // console.log("temp alertsss-===", step8Done);
    if (
      isBleConnected &&
      parseBleData?.SW > 0 &&
      // dName?.connected === 1 &&
      // toNumber(state?.value) > 0 &&
      !isConnecting &&
      !productConnect &&
      !tempModal &&
      !humdtModal &&
      !emergencyAlert &&
      step8Done
    ) {
      console.log('After Disconnecte ---->>>> 2222');
      setVisible(true);
      setAlertData({
        // name: dName?.device_bluetooth_name,
        name:
          dName?.device_bluetooth_name === 'Baby_Auto1'
            ? 'LINKO'
            : dName?.device_bluetooth_name,
        title: '',
        message: `${!isEmpty(childName) ? childName : 'Child'} ${translate(
          'inSeat',
        )}.\n${translate('productConnected')}`,
        type: '',
      });
      setProductConnect(true);
      // if (!appState) {
      pushNotification(
        `${translate('childLeftInSeatTitle', {
          child_name: childName || 'Child',
        })}`,
        `${!isEmpty(childName) ? childName : 'Child'} ${translate(
          'inSeat',
        )}, ${translate('productConnected')}.`,
      );
      // }

      addAlert(
        `${translate('childLeftInSeatTitle', {
          child_name: childName || 'Child',
        })}`,
        `${!isEmpty(childName) ? childName : 'Child'} ${translate(
          'inSeat',
        )}, ${translate('productConnected')}.`,
        'childConnect',
      );
    }
    // setTimeout(async () => {
    console.log(
      'left seat alert===>>',
      dName?.connected,
      toNumber(state?.value),
      alertData?.type,
      !isConnecting,
      step8Done,
    );
    if (
      isBleConnected &&
      parseBleData?.SW === 0 &&
      accessToken !== '' &&
      !tempModal &&
      !humdtModal &&
      !emergencyAlert &&
      alertData?.type !== 'leftChild' &&
      !isConnecting &&
      step8Done
    ) {
      console.log('After Disconnecte ---->>>> 3333');
      sendErrorReport(appState, '30 sec msg');
      console.log('left seat alert', state?.value);
      setAlertData({
        // name: dName?.device_bluetooth_name,
        name:
          dName?.device_bluetooth_name === 'Baby_Auto1'
            ? 'LINKO'
            : dName?.device_bluetooth_name,
        title: '',
        message: `${translate('leftSeatMessage', {
          child_name: childName || 'Child',
        })}`,
        type: '',
      });
      setVisible(true);
      setProductConnect(false);

      // if (!appState) {
      pushNotification(
        `${translate('childLeftSeatTitle', {
          child_name: childName || 'Child',
        })}`,
        `${translate('leftSeatMessage', {
          child_name: childName || 'Child',
        })}`,
      );
      // }
      addAlert(
        `${translate('childLeftSeatTitle', {
          child_name: childName || 'Child',
        })}`,
        `${translate('leftSeatMessage', {
          child_name: childName || 'Child',
        })}`,
        'leftchildseat',
      );
    }
    // }, 300);
  }, [state?.key, parseBleData, appState]);
  // }, [isChange]);

  // this functioon for disconnect child device
  /** this function for disconnect Child Device
   * @function disconnectChildDevice
   * @param {object} data device_id
   */
  async function disconnectChildDevice(data) {
    console.log('Device Deisconnced------');
    sendErrorReport(data, 'disconnect_item_data---');
    const headers = {
      'Content-Type': 'application/json',
      authorization: accessToken ? `Bearer ${accessToken}` : '',
    };
    const findIndexes = findIndex(
      connectedDeviceDetail,
      i => i?.product_id === data,
    );
    // if (!isEmpty(connectedDeviceDetail)) {
    //   connectedDeviceDetail.map((item, index) => {
    if (findIndexes > -1) {
      const data = {
        device_id: connectedDeviceDetail[findIndexes]?.device_id,
        product_id: connectedDeviceDetail[findIndexes]?.product_id,
        child_id: connectedDeviceDetail[findIndexes]?.child_id,
      };
      try {
        const response = await getApiData(
          BaseSetting.endpoints.disconnectChildDevice,
          'POST',
          data,
          headers,
        );
        console.log('responce--------', response);
        if (response.success) {
          sendErrorReport(
            connectedDeviceDetail[findIndexes],
            'disconnect_child_device_item',
          );
          sendErrorReport(data, 'disconnect_child_device_data');
          setTimeout(() => {
            AsyncStorage.setItem('isConnect', 'fal');
            dispatch(BluetoothActions.setcharacteristicID(''));
            dispatch(BluetoothActions.setServiceID(''));
          }, 1500);
        }
      } catch (error) {
        console.log('disconnect device error ===', error);
        sendErrorReport(error, 'disconnect_child_device');
      }
    }
  }
  const handleDisconnectedPeripheral = async data => {
    const returnArr = await getUpdatedList();
    const indx = findIndex(
      returnArr,
      i => i?.product_id === data?.peripheral && i.connected === 1,
    );
    console.log('return aarr', returnArr);
    sendErrorReport(returnArr, 'return_aarr');
    sendErrorReport(data, 'data_aarr');
    if (indx > -1 && isBleConnected) {
      console.log('Temp Alert  2222   ---------');
      sendErrorReport(indx, 'in_ble_index');
      dispatch(setIsConnected(false));
      setIsReadData(true);
      const peripheral = peripherals.get(data.peripheral);
      if (peripheral) {
        peripheral.connected = false;
        peripherals.set(peripheral.id, peripheral);
      }

      dispatch(setIsBleConnected(false));
      dispatch(BluetoothActions.setDeviceID(''));
      // setisConnecting(false);
      setstartRead(false);
      Toast.show(translate('disconnectedFromDevice'));
      // setTimeout(() => {
      sendErrorReport(parseBleData, 'parseBleData');
      sendErrorReport(parseBleData?.SW, 'parseBleData?.SW');
      // setTimeout(async () => {
      if (
        (parseBleData?.SW.toString() || parseBleData?.s1.toString()) === '0'
      ) {
        dispatch(BluetoothActions.setLeftChildAlert(true));
      }
      console.log('🚀 ~ //setTimeout ~ parseBleData:', parseBleData);
      if ((parseBleData?.SW ? parseBleData?.SW : parseBleData?.s1) >= 1) {
        if (Platform.OS === 'android') {
          const asyncTimeout = await AsyncStorage.getItem('async60secTimeout');
          console.log(
            '🚀 ~ //setTimeout ~ asyncTimeout:',
            asyncTimeout,
            typeof asyncTimeout,
          );
          if (asyncTimeout === 'false') {
            sendErrorReport(timer, '60secTimeOutfalse_true2');
            dispatch(BluetoothActions.setEmergencyAlert(true));
          } else if (timer < 60 && timer > 0) {
            sendErrorReport(timer, 'timer in between');
            // dispatch(BluetoothActions.setEmergencyAlert(true));
          } else {
            sendErrorReport(true, 'asyncTimeouttrue');
          }
        } else {
          console.log('comes After Disconnected ----');
          dispatch(BluetoothActions.setEmergencyAlert(true));
          sendErrorReport(timer, 'emergencyTrue1');
        }
        dispatch(BluetoothActions.setBleData({}));
        dispatch(BluetoothActions.setChildProductId({}));
      } else {
        sendErrorReport(timer, 'handle_disconnect_swwww');
      }
      await AsyncStorage.setItem('Disconnect', 'True');
      dispatch(BluetoothActions.setcharacteristicID(''));
      dispatch(BluetoothActions.setServiceID(''));
      // }, 500);
      disconnectChildDevice(data.peripheral);
    } else {
      dispatch(setIsBleConnected(false));
    }
  };

  /* Works fine in Background - Tested By Krunal */
  /** this function for handle Update Value For Characteristic or Seat
   * @function handleUpdateValueForCharacteristic
   * @param {object} data data.value
   */
  // const handleUpdateValueForCharacteristic = data => {
  //   console.log('After Disconnecte Comes to this function 6666');
  //   // console.log("data======", data);
  //   if (data) {
  //     console.log('After Disconnecte ---->>>> 444');
  //     const data1 = data.value;
  //     if (data1.length > 0) {
  //       data1.map(i => {
  //         if (data1[data1.length - 1] === 0) {
  //           data1.splice(data1.length - 1, 1);
  //         }
  //       });
  //     }
  //     const datas = bytesToString(data1);
  //     const peripheral = data?.peripheral;
  //     const dp = datas && JSON.parse(datas);
  //     const iCA1 =
  //       isCurrentActiveDevice[peripheral] &&
  //       JSON.parse(isCurrentActiveDevice[peripheral]);

  //     const obj = { ...isCurrentActiveDevice };
  //     obj[peripheral] = datas;
  //     try {
  //       const realObj = JSON.parse(datas); // ONLY TO CHECK IF DATA IS PROPER AND VALID
  //       if (isString(datas)) {
  //         console.log('After Disconnecte ---->>>> 55555');
  //         if (iCA1?.SW !== realObj?.SW) {
  //           // dispatch(BluetoothActions.setBleData(datas));
  //           // setSWState({
  //           //   key: data?.peripheral,
  //           //   value: realObj?.SW.toString(),
  //           // });
  //         }
  //         if (isObject(obj)) {
  //           setProductConnect(false);
  //           dispatch(setIsBleConnected(true));
  //           if (Platform.OS === 'android') {
  //             // sendErrorReport(true, "60secTimeOut-false");
  //             // dispatch(set60secTimeOut(false));
  //             // AsyncStorage.setItem("async60secTimeout", "false");
  //           }
  //           dispatch(BluetoothActions.setCurrentActiveDevice(obj));
  //         }
  //         if (Platform.OS === 'android') {
  //           bleManagerEmitter.removeAllListeners(
  //             'BleManagerDidUpdateValueForCharacteristic',
  //           );
  //         }
  //       }
  //       // sendErrorReport(realObj, "realObj");
  //       // sendErrorReport(iCA1, "iCA1");
  //       // sendBleDataToApi(datas, "LISTENER");
  //       // console.log("handleUpdateValueForCharacteristic success", realObj);
  //     } catch (err) {
  //       console.log('handleUpdateValueForCharacteristic catch', err);
  //       // Toast.show(`ERROR: Unreadable data from device`);
  //       sendErrorReport(data, 'unreadable_char');
  //     }
  //   }
  // };

  const handleUpdateValueForCharacteristic = data => {
    // console.log("handleUpdateValueForCharacteristic -> data", data);
    // console.log(
    //   `Received data from ${data.peripheral} characteristic ${data.characteristic}`,
    //   data.value
    // );
    if (data) {
      const datas = bytesToString(data.value);
      // console.log("CHECK --> handleUpdateValueForCharacteristic===", datas);
      try {
        // let realObj = JSON.parse(datas); //ONLY TO CHECK IF DATA IS PROPER AND VALID
        if (isString(datas)) {
          dispatch(BluetoothActions.setBleData(datas));
          sendErrorReport(datas, 'datas_update');
        }

        // sendBleDataToApi(datas, "LISTENER");
        // console.log("handleUpdateValueForCharacteristic success", realObj);
      } catch (err) {
        console.log('handleUpdateValueForCharacteristic catch', err);
        Toast.show('ERROR: Unreadable data from device');
        sendErrorReport(err, 'unreadable_char');
      }
    }
  };

  const handleBleState = args => {
    if (args.state === 'on' && !isBleConnected && lastDeviceId) {
      console.log(
        'BleManagerDidUpdateState args.state ===>',
        lastDeviceId,
        isBleConnected,
      );
      // BleManager.isPeripheralConnected(lastDeviceId, []).then(
      //   (isConnected) => {
      //     const index =
      //       connectedDeviceList &&
      //       findIndex(
      //         connectedDeviceList,
      //         (i) => i.product_id === lastDeviceId
      //       );
      //     if (isConnected) {
      //       console.log("Peripheral is connected 222!");
      //       dispatch(
      //         BluetoothActions.setActiveChildDetail(
      //           connectedDeviceList[index]
      //         )
      //       );
      //     } else {
      //       console.log("Peripheral is NOT connected 222!");
      //       dispatch(BluetoothActions.setDeviceID(lastDeviceId));
      //       dispatch(
      //         BluetoothActions.setActiveChildDetail(
      //           connectedDeviceList[index]
      //         )
      //       );
      //       readData();
      //     }
      //   }
      // );
    }
  };

  useEffect(() => {
    const subscription = manager.onStateChange(s => {
      console.log('🚀 ~ TempAlert ~ s:', s);
      if (
        s === 'PoweredOn' &&
        !isBleConnected &&
        lastDeviceId &&
        !emergencyAlert
      ) {
        BleManager.isPeripheralConnected(lastDeviceId, []).then(isConnected => {
          const index =
            connectedDeviceDetails &&
            findIndex(
              connectedDeviceDetails,
              i => i.product_id === lastDeviceId,
            );
          if (isConnected) {
            console.log('Peripheral is connected 222!');
            dispatch(
              BluetoothActions.setActiveChildDetail(
                connectedDeviceDetails[index],
              ),
            );
          } else {
            console.log('Peripheral is NOT connected 222!');
            dispatch(BluetoothActions.setDeviceID(lastDeviceId));
            dispatch(
              BluetoothActions.setActiveChildDetail(
                connectedDeviceDetails[index],
              ),
            );
            readData();
          }
        });
      }
    }, true);
    return () => subscription.remove();
  }, [manager]);

  // //! This effect to autoconnect the device when back in range.
  useEffect(() => {
    const intervalId = BackgroundTimer.setInterval(() => {
      if (!isBleConnected && lastDeviceId && !emergencyAlert) {
        console.log(
          '🚀 ~ TempAlert ~ isBleConnected:',
          isBleConnected,
          lastDeviceId,
        );
        BleManager.isPeripheralConnected(lastDeviceId, []).then(isConnected => {
          console.log('🚀 ~ TempAlert ~ isConnected:', isConnected);
          const index =
            connectedDeviceDetails &&
            findIndex(
              connectedDeviceDetails,
              i => i.product_id === lastDeviceId,
            );
          if (isConnected) {
            console.log('Peripheral is connected 111!');
            readData();
            dispatch(
              BluetoothActions.setActiveChildDetail(
                connectedDeviceDetails[index],
              ),
            );
          } else {
            console.log('Peripheral is NOT connected 111!');
            dispatch(BluetoothActions.setDeviceID(lastDeviceId));
            dispatch(
              BluetoothActions.setActiveChildDetail(
                connectedDeviceDetails[index],
              ),
            );
            readData();
          }
        });
      }
    }, 15000);

    return () => BackgroundTimer.clearInterval(intervalId); // cleanup on unmount
  }, [isBleConnected, lastDeviceId, emergencyAlert]);

  useEffect(() => {
    if (BPermissionOk) {
      const subscription = bleManagerEmitter.addListener(
        'BleManagerDidUpdateValueForCharacteristic',
        handleUpdateValueForCharacteristic,
      );

      return () => {
        subscription.remove();
      };
    }
  }, [isCurrentActiveDevice, isBleConnected, BPermissionOk]); // BPermissionOk

  useEffect(() => {
    // sendErrorReport(isBleConnected, "handleDisconnectedPerip_isble");
    // if (isBleConnected) {//for 30 sec time out issue added //now commented for handle disconnected going far from device on 21/4
    // if (BPermissionOk) {
    sendErrorReport(timer, 'handleDisconnectedPerip');
    const subscription = bleManagerEmitter.addListener(
      'BleManagerDisconnectPeripheral',
      throttle(handleDisconnectedPeripheral, 3000),
    );
    return () => {
      subscription?.remove();
    };
    // }
    // }
  }, [isBleConnected]); // BPermissionOk

  useEffect(() => {
    if (!locationDisclouser) {
      if (Platform.OS === 'android' && Platform.Version >= 23) {
        console.log('called---4');
        PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        ).then(result => {
          if (result) {
            console.log('Permission is OK');
            // sendErrorReport(true, "Fine_location");
          } else {
            PermissionsAndroid.request(
              PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
            ).then(res => {
              if (res) {
                console.log('User accept');
              } else {
                console.log('User refuse');
              }
            });
          }
        });
      } else {
        check(PERMISSIONS.IOS.LOCATION_WHEN_IN_USE).then(res => {
          if (res !== 'granted') {
            sendErrorReport(true, 'getCurrentLocation5');
            getCurrentLocation(false, '');
          }
        });
      }
    }
  }, [locationDisclouser]);
  useEffect(() => {
    if (BPermissionOk) {
      /* Listening to IOS Background events as per the docs - Not Tested */
      bleManagerEmitter.addListener(
        'BleManagerCentralManagerWillRestoreState',
        data => {
          console.log(
            'BLE ==> BleManagerCentralManagerWillRestoreState ===> ',
            data,
          );
        },
      );

      /* Listing to Characteristic updates - Works on IOS ForeGround and Background both - Tested by Krunal */
      // bleManagerEmitter.addListener(
      //   "BleManagerDidUpdateValueForCharacteristic",
      //   handleUpdateValueForCharacteristic
      // );

      bleManagerEmitter.addListener('BleManagerDidUpdateState', handleBleState);

      // this line comment for device connection issue
      BleManager.start({ showAlert: false });

      // bleManagerEmitter.addListener(
      //   "BleManagerDiscoverPeripheral",
      //   handleDiscoverPeripheral
      // );

      bleManagerEmitter.addListener(
        'BleManagerDisconnectPeripheral',
        throttle(handleDisconnectedPeripheral, 3000),
      );

      // bleManagerEmitter.addListener("BleManagerStopScan", handleStopScan);

      return () => {
        console.log('unmount');
      };
    }
  }, [BPermissionOk]);
  // []); //
  const loopConnect = async () =>
    new Promise((resolve, reject) => {
      let triedI = 0;
      let lastErr = '';
      console.log('in loop connect......');
      const curInt = setInterval(async () => {
        if (triedI > 4) {
          // sendErrorReport(lastErr, "reqRetrieveFinalErr");
          clearInterval(triedI);
          reject(lastErr);
        }
        await retrieveConnected(deviceID)
          .then(mtu => {
            clearInterval(curInt);
            // sendErrorReport(mtu, "reqtriedIIntThen");
            resolve(mtu);
          })
          .catch(err => {
            sendErrorReport(err, 'reqtriedIIntErr');
            lastErr = err;
            triedI++;
          });
      }, 1000);
    });

  /* To connect device => Returns a promise */
  const connectDevice = async () =>
    new Promise((resolve, reject) => {
      console.log('BLE ==> Connectingcdf', deviceID);
      if (!deviceID) {
        console.log('BLE ==> Connectedif');
        resolve(false);
      }
      BleManager.connect(deviceID)
        .then(async () => {
          console.log('BLE ==> Connected');
          sendErrorReport(deviceID, 'deviceID_true');
          const isRetConnect = await loopConnect();
          if (isRetConnect) {
            console.log('isRetConnect');
            sendErrorReport(deviceID, 'reqRetrieve-<<');
            resolve(isRetConnect);
          } else {
            console.log('isRetConnect else');
            resolve(true);
          }
          // resolve(true);
        })
        .catch(error => {
          // Failure code
          sendErrorReport(error, 'connect_device');
          resolve(false);
        });
      setTimeout(() => {
        resolve(false);
      }, 6000);
    });

  const reqMTU = async () =>
    new Promise((resolve, reject) => {
      let currentTry = 0;
      let lastErr = '';
      const curInt = setInterval(async () => {
        if (currentTry > 4) {
          // sendErrorReport(lastErr, "reqMTUFinalErr");
          clearInterval(curInt);
          dispatch(BluetoothActions.setHomeDeviceClick(false));
          dispatch(BluetoothActions.setIsConnectLoad(false));
          reject(lastErr);
        }
        await BleManager.requestMTU(deviceID, 512)
          .then(mtu => {
            clearInterval(curInt);
            // sendErrorReport(mtu, "reqMTUIntThen");
            resolve(mtu);
          })
          .catch(err => {
            // sendErrorReport(err, "reqMTUIntErr");
            lastErr = err;
            currentTry++;
          });
      }, 1000);
    });

  const readData = async () => {
    let connected = null;
    try {
      const isAlreadyConnected = await retrieveConnected(deviceID);
      if (!isAlreadyConnected) {
        connected = await connectDevice(deviceID);
      } else {
        connected = true;
      }
    } catch (err) {
      console.log('CHECK --> BLE ==> coonect ==> Error 1', err);
      sendErrorReport(err, 'read_data');
    }
    // Success code
    // console.log('CHECK --> Connected');
    // setconnnectedID(id);
    if (isEmpty(deviceList)) {
      console.log('device list is empty');
      sendErrorReport(deviceList?.length, 'deviceListEmpty');
    }
    if (connected) {
      setTimeout(async () => {
        console.log('CHECK --> Before MTU');
        if (Platform.OS === 'android') {
          // await BleManager.requestMTU(deviceID, deviceID !== '80:EC:CA:CD:A1:DB' ? 58 : 512)
          await reqMTU()
            .then(mtu => {
              // Success code
              sendErrorReport(deviceID, 'deviceID_requestMTU');
              console.log(`CHECK --> MTU size changed to ${mtu} bytes`);
              dispatch(BluetoothActions.setRequestMTU(false));
              return true;
            })
            .catch(error => {
              // Failure code
              console.log('CHECK --> MTU', error);
              sendErrorReport(error, 'request_MTU');
              dispatch(BluetoothActions.setIsConnectLoad(false));
              dispatch(BluetoothActions.setHomeDeviceClick(false));
              dispatch(BluetoothActions.setClickAddQr(false));
              dispatch(BluetoothActions.setRequestMTU(true));
              return true;
            });
        }
        console.log('CHECK --> Before Retreive');
        await BleManager.retrieveServices(deviceID)
          .then(async peripheralData => {
            console.log(
              'CHECK --> Retrieved peripheral services 1',
              peripheralData,
            );
            const char = peripheralData.characteristics;
            const mainChar = char[char.length - 1];
            try {
              const stopped = await BleManager.stopNotification(
                deviceID,
                mainChar?.service,
                mainChar?.characteristic,
              );

              console.log('Stoppeddd ====> ', stopped);
            } catch (err) {
              console.log('stopped errr ===> ', err);
              sendErrorReport(err, 'Stoppeddd_notification');
            }
            setTimeout(() => {
              BleManager.startNotification(
                deviceID,
                mainChar?.service,
                mainChar?.characteristic,
              )
                .then(async () => {
                  console.log(
                    `CHECK --> Started notification on ${deviceID} ${mainChar?.characteristic}`,
                  );
                })
                .catch(error => {
                  console.log('CHECK --> Notification error', error);
                  sendErrorReport(error, 'Start_notification');
                });
            }, 1500);

            if (
              currentScreen?.name === 'QRScanner' ||
              currentScreen?.name === 'Connect'
            ) {
              setTimeout(() => {
                dispatch(BluetoothActions.setIsFromScanner(false));
                dispatch(BluetoothActions.setIsConnectLoad(false));
                dispatch(BluetoothActions.setHomeDeviceClick(false));
                dispatch(BluetoothActions.setClickAddQr(false));
                setstartRead(true);
                setIsReadData(false);
                // Torch.switchState(false);
                sendErrorReport(false, 'connected1');
                Toast.show(translate('CONNECTED'));
              }, 1500);
            } else {
              if (timer > 0 && timer < 60) {
              } else {
                sendErrorReport(false, 'emer_false4');
                dispatch(setEmergencyAlert(false));
              }
              getAutoConnect(deviceID);
              dispatch(BluetoothActions.setHomeDeviceClick(false));
              dispatch(BluetoothActions.setIsConnectLoad(false));
              setstartRead(true);
              setIsReadData(false);
              // Torch.switchState(false);
              if (!emergencyAlert) {
                Toast.show(translate('CONNECTED'));
              } // if auto connect api fails, it should not show connected so this line is shifted in auto connect api success
            }
            return true;
          })
          .catch(e => {
            console.log('CHECK --> error retrive data');
            // Torch.switchState(false);
            dispatch(BluetoothActions.setIsConnectLoad(false));
            dispatch(BluetoothActions.setHomeDeviceClick(false));
            dispatch(BluetoothActions.setClickAddQr(false));
            dispatch(BluetoothActions.setChildProductId({}));
            Toast.show(translate('awakeDeviceTxt'));
            sendErrorReport(e, 'unable_retrieve_service');
            return true;
          });
      }, 1000);
    } else {
      console.log('CHECK --> not connected');
      if (isReadData) {
        sendErrorReport(isReadData, 'isread_data_if');
        await getChildInfo(false); // after deleting assigned device it was getting auto connect,
        // and passed false because from auto connect also this api is calling and going to read data function

        // Torch.switchState(false);
        dispatch(BluetoothActions.setIsConnectLoad(false));
        dispatch(BluetoothActions.setHomeDeviceClick(false));
        setIsReadData(false);
      } else {
        sendErrorReport(isReadData, 'isread_data_else');
        // Torch.switchState(false);
        dispatch(BluetoothActions.setIsConnectLoad(false));
        dispatch(BluetoothActions.setHomeDeviceClick(false));
        dispatch(BluetoothActions.setChildProductId({}));
        Toast.show(translate('awakeDeviceTxt'));
      }
    }
  };

  async function getAutoConnect(id) {
    sendErrorReport(id, 'autoConnect_id');

    const findData = connectedDeviceDetail.findIndex(i => i.product_id === id);

    sendErrorReport(
      connectedDeviceDetail[findData],
      'connectedDeviceDetail[findData]',
    );
    const headers = {
      'Content-Type': 'application/json',
      authorization: accessToken ? `Bearer ${accessToken}` : '',
    };
    try {
      const response = await getApiData(
        BaseSetting.endpoints.autoConn,
        'POST',
        {
          product_id: connectedDeviceDetail[findData].product_id,
          child_id: connectedDeviceDetail[findData].child_id,
        },
        headers,
      );
      // sendErrorReport(response, "response_autoConnect_childId");

      if (response.success) {
        // Toast.show(translate("CONNECTED"));
        getChildInfo(true);
        getDeviceList();
      } else {
        // Toast.show("error===>>>", response.message);
      }
    } catch (error) {
      sendErrorReport(JSON.parse(error), 'autoConnectTemp');
      console.log('feed post error ===', error);
    }
  }

  useEffect(() => {
    const appStateSubscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => {
      appStateSubscription.remove();
    };
  }, []);
  // this function for update alert list in db
  /** this function for update alert list in db
   * @function addAlert
   * @param {object} data title, body
   */
  async function addAlert(title, body, type = '') {
    const headers = {
      'Content-Type': 'application/json',
      authorization: accessToken ? `Bearer ${accessToken}` : '',
    };

    if (type === 'emergency') {
      sendErrorReport(isFlashLight, 'setisFlasg');
      setIsFlashlight(!isFlashLight);
    }

    const data = {
      title: title || '',
      body: body || '',
      app_state: appState ? 'active' : 'background',
      platform: Platform.OS,
      type,
    };
    sendErrorReport(data, 'add_alert_api_param');
    try {
      await getApiData(BaseSetting.endpoints.addAlert, 'POST', data, headers);
    } catch (error) {
      sendErrorReport(error, 'add_alert_api_error');
      console.log('add alert error ===', error);
    }
  }

  // BackgroundTimer.runBackgroundTimer(async () => {
  //   // useEffect(() => {
  //   console.log("🚀 ~ file: index.js ~ line 977 ~ //useEffect ~ timer", timer);
  //   if (
  //     timer > 0
  //     && isFlashLight
  //       && !isBleConnected
  //       // && lastDeviceId
  //   ) {
  //     Torch.switchState(isFlashLight);
  //   }
  //   // }, []);
  // }, 3000);

  const handleAppStateChange = nextAppState => {
    // console.log(`App State: ${nextAppState}`);
    if (nextAppState === 'active') {
      setAppState(true);
    } else {
      setAppState(false);
    }
  };

  // this function for get current location
  async function getCurrentLocation(id, click = '') {
    console.log('🚀 ~ getCurrentLocation ~ id:', id);
    console.log('called----1');
    GetLocation.getCurrentPosition({
      enableHighAccuracy: true,
      timeout: !appState ? 15000 : 0,
    })
      .then(location => {
        console.log('🚀 ~ getCurrentLocation ~00000 getLocation:', location);
        AsyncStorage.setItem('setLocation', JSON.stringify(location));
        if (id) {
          console.log('in1--->', id);
          // sendErrorReport(location, "SMS_location");
          // sendErrorReport(id, "SMS_id111");

          sendSMS(location, id, click);
        }
      })
      .catch(async error => {
        const getLocation = await AsyncStorage.getItem('setLocation');
        sendErrorReport(JSON.parse(getLocation), 'get_location');
        sendErrorReport(id, 'SMS_id122');
        const { code, message } = error;
        if (
          getLocation !== '' &&
          id &&
          code !== 'UNAVAILABLE' &&
          Platform.OS === 'android'
        ) {
          sendSMS(JSON.parse(getLocation), id, click);
        }
        if (getLocation !== '' && id && Platform.OS === 'ios') {
          sendSMS(JSON.parse(getLocation), id, click);
        }
        sendErrorReport(code, 'SMS_code');
        if (code === 'UNAVAILABLE') {
          Toast.show(
            'Please enable your location service to send emergency alert.',
          );
          GetLocation.openSettings();
        }
      });
  }
  const setbgProcess = () => {
    console.log('comes in StopBg POrocess');
    BackgroundTimer.clearInterval(interval);
    BackgroundTimer.stopBackgroundTimer();
    clearInterval(interval);
    setTimer(0);
    dispatch(setEmergencyAlert(false));
    Torch.switchState(false);
    setIsSend(false);
    setSound(false);
    setVisible(false);
    dispatch(BluetoothActions.setActiveChildDetail({}));
  };

  // this function for send SMS for emergency alert
  /** this function for send SMS for emergency alert
   * @function sendSMS
   * @param {object} data latitude, longitude, child_id
   */
  async function sendSMS(location, id, click = '') {
    console.log(
      'msg send 111 ---->',
      location.latitude,
      location.longitude,
      click,
    );
    const headers = {
      'Content-Type': 'application/json',
      authorization: accessToken ? `Bearer ${accessToken}` : '',
    };

    if (location.latitude && location.longitude) {
      try {
        const dt = {
          latitude: location.latitude,
          longitude: location.longitude,
          child_id: id || activeChildDetail.id,
          lang_code: languageData?.languageData || 'es',
          btn: click || '',
          app_state: appState ? 'active' : 'background',
        };
        sendErrorReport(dt, 'sms_param');
        const response = await getApiData(
          BaseSetting.endpoints.sendEmergencyMessageToUser,
          'POST',
          dt,
          headers,
        );
        sendErrorReport(response, 'sms_resp');
        if (response.success) {
          if (click === 'click') {
            setVisible(true);
            setAlertData({
              name: 'LINKO',
              title: '',
              message: `${translate('sendSMSAlert', {
                child_name: childName || 'Child',
              })}`,
              type: '',
            });
            sendErrorReport(timer, 'msg sent2');
            Toast.show('Message Sent');
            // sendErrorReport(timer, "emer_false");
            sendErrorReport(timer, 'clear4');
            BackgroundTimer.clearInterval(interval);
            BackgroundTimer.stopBackgroundTimer();
            clearInterval(interval);
            setTimer(60);
            dispatch(BluetoothActions.settimerValue(60));
            dispatch(setEmergencyAlert(false));
            Torch.switchState(false);
            setIsSend(false);
            setSound(false);
            dispatch(BluetoothActions.setActiveChildDetail({}));
            if (Platform.OS === 'android') {
              dispatch(set60secTimeOut(false));
              sendErrorReport(false, '60secTimeOutClick');
              AsyncStorage.setItem('async60secTimeout', 'false');
            }
          } else {
            console.log('comes to this Condition 1sms send-------');
            sendErrorReport(timer, 'clickElse');

            if (timer === 0 && Platform.OS === 'ios') {
              sendErrorReport(timer, 'timerValue0');
              setVisible(true);
              setAlertData({
                name: 'LINKO',
                title: '',
                message: `${translate('sendSMSAlert', {
                  child_name: childName || 'Child',
                })}`,
                type: '',
              });
              Toast.show('Message Sent');
            }

            interval = BackgroundTimer.setTimeout(async () => {
              console.log('test background -----------', timer);
              Torch.switchState(false);
              setSound(false);
              const SMSSentAndroidAsync = await AsyncStorage.getItem(
                'SMSSentAndroidBg',
              );
              console.log('SMSSentAndroidBgn', SMSSentAndroidAsync);
              sendErrorReport(true, '60timer');
              if (SMSSentAndroidAsync === 'true') {
                sendErrorReport(true, 'smsSentAndroidBg43');
              }

              if (
                Platform.OS === 'android' &&
                cancelSMS === false &&
                SMSSentAndroidAsync === 'true'
              ) {
                sendErrorReport(timer, 'clear5');
                setbgProcess();
              } else if (Platform.OS === 'ios') {
                console.log('sms comes into this condition');
                sendErrorReport(timer, 'clear6');
                setbgProcess();
              }

              if (
                Platform.OS === 'android' &&
                cancelSMS === false &&
                SMSSentAndroidAsync === 'true'
              ) {
                AsyncStorage.setItem('SMSSentAndroidBg', 'false');
                dispatch(BluetoothActions.setSMSSentAndroidBg(false));
                sendErrorReport(timer, '6000secAppState');
                dispatch(set60secTimeOut(true));
                AsyncStorage.setItem('async60secTimeout', 'true');
                // if (isSend) {
                //   setVisible(true);
                //   setAlertData({
                //     name: 'LINKO',
                //     title: '',
                //     message: `${translate('sendSMSAlert', {
                //       child_name: childName || 'Child',
                //     })}`,
                //     type: '',
                //   });
                //   sendErrorReport(timer, 'msg sent3');
                //   Toast.show('Message Sent');
                // }
              }
            }, 60000);

            // setTimeout(() => {
            //   sendErrorReport(appState, "6000secAppState");
            //   if (Platform.OS === "android") {
            //     clearInterval(interval);
            //     setTimer(0);
            //     sendErrorReport(true, "60timer");
            //   }
            //   dispatch(setEmergencyAlert(false));
            //   Torch.switchState(false);
            //   setIsSend(false);
            //   setSound(false);
            //   dispatch(BluetoothActions.setActiveChildDetail({}));
            //   // addAlert("Message sent", "Emergency Message Sent");
            // }, 60000);
          }
        } else {
          sendErrorReport(true, 'clickElse2');
          console.log('Message Not Sent');
        }
      } catch (error) {
        console.log('disconnect device error ===', error);
        Toast.show('Something went wrong while sending sms');
        sendErrorReport(error, 'send_sms');
      }
    }
  }

  useEffect(() => {
    // sendErrorReport(timer, "timer=");
    console.log('timer---====---', timer);
    if (timer === 0) {
      // stopTimer();
      console.log('timer---====---timerrrr');
      sendErrorReport(timer, 'clear7');
      AsyncStorage.setItem('async60secTimeout', 'true');
      BackgroundTimer.clearInterval(interval);
      BackgroundTimer.stopBackgroundTimer();
      sendErrorReport(timer, 'emer_timeOut');
      setEmergencyAlertTimeout(true);
    }
  }, [timer]);

  useEffect(() => {
    console.log(
      'After Disconnecte Comes to this function ----->>>>2222',
      parseBleData,
      parseBleData?.SW,
      isBleConnected,
      isLeftChildAlert,
      appState,
    );
    if (parseBleData?.SW === 0 && !appState) {
      console.log('comesinto this condition 222222');
      setAlertData({
        name: 'LINKO',
        title: `${translate('deviceDisconnect')}`,
        message: `${translate('disconnectSeat', {
          child_name: activeChildDetail?.nick_name || 'Child',
        })}`,
        type: '',
      });
      addAlert(
        `${translate('deviceDisconnect')}`,
        `${translate('disconnectSeat', {
          child_name: activeChildDetail?.nick_name || 'Child',
        })}`,
        'disconnect',
      );
      BackgroundTimer.clearInterval(interval);
      BackgroundTimer.stopBackgroundTimer();
    }
    if (parseBleData?.SW === 0 && appState) {
      console.log('comes into this condition 33333');
      setAlertData({
        name: 'LINKO',
        title: `${translate('deviceDisconnect')}`,
        message: `${translate('disconnectSeat', {
          child_name: activeChildDetail?.nick_name || 'Child',
        })}`,
        type: '',
      });
      // addAlert(
      //   `${translate('deviceDisconnect')}`,
      //   `${translate('disconnectSeat', { child_name: childName || 'Child' })}`,
      //   'disconnect',
      // );
      setVisible(true);
      Torch.switchState(true);
      dispatch(setDeviceDisconnect(true));
    }
    console.log('state value===', parseBleData?.SW);
    if (parseBleData?.SW >= 1) {
      console.log('comes tot thos ondition-------');
      sendErrorReport(timer, 'andBg');
      setAlertData({
        name: 'LINKO',
        title: `${translate('leftSeatTitle', {
          child_name: activeChildDetail?.nick_name || 'Child',
        })}`,
        message: ` ${
          !isEmpty(activeChildDetail?.nick_name)
            ? activeChildDetail?.nick_name
            : `${translate('child')}`
        } ${translate('childLeftInSeatMessage')}`,
        type: 'leftChild',
      });
      Torch.switchState(true);
      dispatch(setDeviceDisconnect(true));

      if (Platform.OS === 'android') {
        console.log('android_bg');
        sendErrorReport(timer, 'andBg');
        dispatch(BluetoothActions?.settimerValue(timer));
      } else {
        // startTimer();
        if (smsSentAndroidBg === false) {
          sendErrorReport(timer, 'smsSentAndroidBg');
          setTimer(60);
        }
        // interval = BackgroundTimer.setInterval(() => {
        //   setTimer(p => {
        //     console.log('piiiiii----------', p);

        //     return p - 1;
        //   });
        // }, 1000);
        sendErrorReport(timer, 'timer22');
        dispatch(BluetoothActions?.settimerValue(timer));
      }
      setVisible(true);
      setSound(true);
      addAlert(
        `${translate('leftSeatTitle', {
          child_name: activeChildDetail?.nick_name || 'Child',
        })}`,
        `${
          !isEmpty(activeChildDetail?.nick_name)
            ? activeChildDetail?.nick_name
            : `${translate('child')}`
        } ${translate('emergencyAlertMessage')}`,
        'emergency',
      );
    }
  }, [emergencyAlert, isBleConnected, isLeftChildAlert, parseBleData]);

  useEffect(() => {
    console.log('After Disconnecte Comes to this function ----->>>>3333');
    const childData2 = find(
      connectedDeviceDetail,
      item => item?.product_id === state?.key && item?.connected === 1,
    );
    if (
      !appState &&
      !isBleConnected &&
      emergencyAlert &&
      parseBleData?.SW === 0
    ) {
      if (Platform.OS === 'ios') {
        console.log('comes to this condition eeeeeeee');
        sendErrorReport(true, 'getCurrentLocation1');
        getCurrentLocation(childData2?.child_id, '');
      }
    }
  }, [emergencyAlert, isBleConnected, parseBleData]);

  useEffect(() => {
    console.log('OPENING Temperature Modal ==>', tempModal);
    if (tempModal) {
      setAlertData({
        // name: deviceName,
        name: deviceName === 'Baby_Auto1' ? 'LINKO' : deviceName,
        title: translate('temperatureAlertTitle'),
        message: '',
        type: 'temp',
      });
      setVisible(true);
    }
  }, [tempModal]);

  useEffect(() => {
    console.log('OPENING Humidity Modal ==>', humdtModal);
    if (humdtModal) {
      setAlertData({
        // name: deviceName,
        name: deviceName === 'Baby_Auto1' ? 'LINKO' : deviceName,
        title: translate('humidityAlertTitle'),
        message: '',
        type: 'humdt',
      });
      setVisible(true);
    }
  }, [humdtModal]);

  async function cancelSendSms(id) {
    const headers = {
      'Content-Type': 'application/json',
      authorization: accessToken ? `Bearer ${accessToken}` : '',
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.updateMessageSendRecord,
        'POST',
        { child_id: id },
        headers,
      );
      if (response.success) {
        sendErrorReport(false, 'emer_false1');
        sendErrorReport(timer, 'clear8');
        BackgroundTimer.clearInterval(interval);
        BackgroundTimer.stopBackgroundTimer();
        clearInterval(interval);
        setTimer(60);
        dispatch(BluetoothActions.settimerValue(60));
        dispatch(setEmergencyAlert(false));
        setIsSend(false);
        setSound(false);
        Torch.switchState(false);
      } else {
        console.log('Not with responsible');
      }
      // setVisible(false);
    } catch (error) {
      console.log('disconnect device error ===>>', error);
      Toast.show('Something went wrong while cancel sms');
      sendErrorReport(error, 'cancel_sms');
    }
  }

  useEffect(() => {
    if (emergencyAlertTimeout) {
      if (emergencyAlert) {
        console.log('dddddd emergency sms---->');
        setVisible(pre => !pre);
        // Torch.switchState(false);
        sendErrorReport(timer, 'clear1');
        BackgroundTimer.clearInterval(interval);
        BackgroundTimer.stopBackgroundTimer();
        setTimer(60);
        // sendErrorReport(timer, "emergencyAlertTimeout");
        setEmergencyAlertTimeout(false);
        setIsChange(false);
        const childData2 = find(
          connectedDeviceDetail,
          item => item?.product_id === state?.key,
        );
        // const sentAndroidBg = await AsyncStorage.getItem("smsSentAndroidBg"); //mmmcheck this cause isse after timeout
        if (Platform.OS === 'android' && !appState) {
          sendErrorReport(timer, 'doNotTime');
          dispatch(setEmergencyAlert(false));
          Torch.switchState(false);
          setIsSend(false);
          setSound(false);
          dispatch(BluetoothActions.setActiveChildDetail({}));
          AsyncStorage.setItem('SMSSentAndroidBg', 'false');
          dispatch(BluetoothActions.setSMSSentAndroidBg(false));
          sendErrorReport(timer, '6000secAppState');
          dispatch(set60secTimeOut(true));
          AsyncStorage.setItem('async60secTimeout', 'true');
          setVisible(true);
          setAlertData({
            name: 'LINKO',
            title: '',
            message: `${translate('sendSMSAlert', {
              child_name: childName || 'Child',
            })}`,
            type: '',
          });
          sendErrorReport(timer, 'msg sent1');
          Toast.show('Message Sent');
        } else {
          sendErrorReport(getCurrentLocation, 'getCurrentLocation9');
          getCurrentLocation(childData2?.child_id, appState ? 'click' : '');
        }
        // else if (sentAndroidBg === false) {
        //   sendErrorReport(true, "getCurrentLocation2");
        //   getCurrentLocation(childData2?.child_id, appState ? "click" : "");
        // } else {
        //   sendErrorReport(sentAndroidBg, "sentAndroidBg");
        // }
        setIsSend(true);
        setIsChange(false);
        setSound(false);
        // Torch.switchState(false);
      }
    }
  }, [emergencyAlertTimeout]);

  function handleModal(type) {
    setVisible(pre => !pre);
    // Torch.switchState(false);

    setIsChange(false);
    if (type === 'ok') {
      setVisible(false);
      Torch.switchState(false);
      // Torch.switchState(false);
    }
    if (type === 'ok' && alertData?.type === 'leftChild') {
      setVisible(false);
      Torch.switchState(false);
      sendErrorReport(timer, 'clear2');
      BackgroundTimer.clearInterval(interval);
      BackgroundTimer.stopBackgroundTimer();
      clearInterval(interval);
      setTimer(60);
      // sendErrorReport(timer, "handle_modal");
      setEmergencyAlertTimeout(false);
      const childData2 = find(
        connectedDeviceDetail,
        item => item?.product_id === state?.key,
      );
      sendErrorReport(timer, 'getCurrentLocation3');
      getCurrentLocation(childData2?.child_id, 'click');
      setIsSend(true);
      setIsChange(false);
      setSound(false);
      Torch.switchState(false);
    }

    if (type === 'cancel' && alertData?.type === 'leftChild') {
      sendErrorReport(timer, 'clear3');
      BackgroundTimer.clearInterval(interval);
      BackgroundTimer.stopBackgroundTimer();
      clearInterval(interval);
      setTimer(60);
      sendErrorReport(timer, 'handle_modal2');
      setEmergencyAlertTimeout(false);
      AsyncStorage.setItem('async60secTimeout', 'false');
      dispatch(BluetoothActions.setcancelSMS(true));
      dispatch(BluetoothActions.setSMSSentAndroidBg(false));
      cancelSendSms(activeChildDetail?.id);
      setstartRead(false);
      setIsSend(false);
      setIsChange(false);
      setSound(false);
      Torch.switchState(false);
    }

    if (type === 'ok' && alertData.type === 'temp') {
      dispatch(setTempModal(false));
      setIsChange(false);
      dispatch(setIsConvert(false));
    }

    if (type === 'cancel' && alertData.type === 'temp') {
      dispatch(setTempModal(false));
      // dispatch(setIsConvert(true));
    }

    if (alertData.type === 'humdt') {
      dispatch(setHumdtModal(false));
      setIsChange(false);
    }
    // setAlertData({name: "", title: "", message: "", type: "" });
    if (emergencyAlert) {
      sendErrorReport(false, 'emer_false2');
      dispatch(setEmergencyAlert(false));
      setIsChange(false);
      setSound(false);
      clearInterval(interval);
      Torch.switchState(false);
    }
  }

  useEffect(() => {
    if (!appState && !isBleConnected && parseBleData?.SW > 0) {
      const childData2 = find(
        connectedDeviceDetail,
        item => item?.product_id === state?.key && item?.connected === 1,
      );
      if (!isUndefined(childData2)) {
        childName = childData2?.nick_name;
      }
      // save redux android bg called and dont call again from timeout

      console.log('Comnnnected0----------', appState);
      dispatch(BluetoothActions.setSMSSentAndroidBg(true));
      AsyncStorage.setItem('SMSSentAndroidBg', 'true');
      dispatch(BluetoothActions.setcancelSMS(false));
      setTimer(60);
      sendErrorReport(true, 'getCurrentLocation4_true3');
      getCurrentLocation(childData2?.child_id, '');
      dispatch(BluetoothActions.setEmergencyAlert(true)); // added 1march sound is not on from bg
      // addAlert(
      //   `${translate('childLeftInSeatTitle', {
      //     child_name: childName || 'Child',
      //   })}`,
      //   `${
      //     !isEmpty(childName) ? childName : `${translate('child')}`
      //   } ${translate('emergencyAlertMessage')}`,
      //   'emergency',
      // );
      dispatch(setIsConnected(false));
    }
    // if (
    //   !appState
    //   && isBleConnected
    //   && Number(state?.value) === 0
    //    && Platform.OS === "android") {
    //   addAlert(
    //     `${translate("childLeftSeatTitle", {
    //       child_name: childName || "Child",
    //     })}`,
    //     `${translate("leftSeatMessage", {
    //       child_name: childName || "Child",
    //     })}`,
    //     "leftchildseat"
    //   );
    // }
  }, [isBleConnected]);

  function pushNotification(title, message) {
    // PushNotification.localNotification({
    //   channelId: "chillBaby_channel_id",
    //   title, // (optional)
    //   message, // (required)
    //   ignoreInForeground: false,
    //   importance: "high",
    //   priority: "high",
    //   visibility: "public",
    //   smallIcon: require("../../assets/images/logo.png"),
    //   largeIcon: require("../../assets/images/logo.png"),
    //   playSound: true,
    //   soundName: "default",
    // });
    // if (
    //   title === `${translate("childLeftInSeatTitle")}` &&
    //   !isBleConnected &&
    //   !appState
    // ) {
    //   // setTimeout(() => {
    //   getCurrentLocation(activeChildDetail?.id, "");
    //   sendErrorReport(activeChildDetail?.id, "SMS_ID_sent");
    //   // }, 60000);
    // }
  }

  useEffect(() => {
    const obj = {
      title: alertData.title,
      message: alertData.message,
      type: alertData.type,
    };
    dispatch(setAlertDataRedux(obj));
  }, [alertVisible, alertData]);

  const currentRoute = navRef?.getCurrentRoute()?.name || '';
  const dontShowAlertOn = [
    'SplashScreen',
    'Walkthrough',
    'RedirectLS',
    'Login',
    'Signup',
    'ForgotPassword',
    'Otp',
    'ChildInfo',
    'QRScanner',
    'bleList',
  ].includes(currentRoute);
  const alertVisible = (visibleRef?.current && !dontShowAlertOn) || false;

  const playRing = () => (
    <Video
      source={require('../../config/siren.wav')}
      ref={playerRef}
      audioOnly
      poster=""
      paused={!sound}
      ignoreSilentSwitch="ignore"
      repeat
      volume={100}
      muted={false}
      playInBackground
      onEnd={() => {
        if (Platform.OS !== 'ios') {
          setSound(false);
        }
      }}
    />
  );

  return (
    <>
      {/* {console.log('TempAlert', alertVisible)} */}
      <CAlert
        visible={alertVisible}
        onRequestClose={w => handleModal(w)}
        onCancelPress={() => handleModal('cancel')}
        onOkPress={() => handleModal('ok')}
        type={alertData?.type !== '' ? alertData?.type : 'tempAlert'}
        alertName={alertData?.name}
        alertTitle={alertData?.title}
        alertMessage={alertData?.message}
        agreeTxt={
          alertData.type === 'leftChild'
            ? translate('sendSMS')
            : translate('alertOkBtn')
        }
        cancelTxt={
          alertData.type === 'leftChild'
            ? translate('responsibleAdult')
            : translate('alertCancelBtn')
        }
        timerValue={timer}
        getData={() => {
          if (Platform.OS === 'android') {
            setEmergencyAlertTimeout(true);
          } else {
            setTimer(0);
          }
          console.log('check timer stop!!!!!');
        }}
      />
      {playRing()}
    </>
  );
};

export default TempAlert;
