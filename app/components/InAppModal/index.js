/* eslint-disable no-nested-ternary */
/* eslint-disable indent */
/* eslint-disable quotes */
import { isArray, isEmpty, isObject, isString } from "lodash";
import React from "react";
import {
  Image,
  Modal,
  Text,
  TouchableOpacity,
  View,
  Linking,
} from "react-native";
import Icon from "react-native-vector-icons/Ionicons";
import BaseColor from "../../config/colors";

export default function InAppModal(props) {
  const {
    visible,
    button,
    title,
    image,
    position,
    onClose = () => {},
    detail,
  } = props;
  const btnArr = isString(button) ? JSON.parse(button) : button;
  const titObj = isString(title) ? JSON.parse(title) : title;

  function handleUrl(item) {
    const url = item?.button_url ? item?.button_url : "";
    if (url !== "") {
      Linking.canOpenURL(url).then((supported) => {
        if (!supported) {
          console.error("No handler for URL:", url);
        } else {
          Linking.openURL(url);
        }
      });
    }
    onClose();
  }

  const campaignType =
    isObject(detail) && !isEmpty(detail) ? detail.campaign_type : "";

  return (
    <Modal
      visible={visible}
      style={{ flex: 1, padding: 16 }}
      transparent
      animationType="fade"
    >
      <TouchableOpacity
        activeOpacity={1}
        style={{
          flex: 1,
          backgroundColor: "#00000050",
          justifyContent:
            position === "bottom"
              ? "flex-end"
              : position === "top"
              ? "flex-start"
              : "center",
          alignItems: "center",
          padding: 16,
          paddingTop: position === "top" ? 55 : 44,
        }}
      >
        <View
          style={{
            backgroundColor: "#fafafa",
            // padding: 12,
            borderRadius: 22,
            width: "100%",
            height: position === "full" ? "100%" : "50%",
            justifyContent: "space-around",
          }}
        >
          <TouchableOpacity
            style={{
              height: 35,
              width: 35,
              borderRadius: 18,
              backgroundColor: BaseColor.blueDark,
              position: "absolute",
              right: -10,
              top: -15,
              alignItems: "center",
              justifyContent: "center",
              zIndex: 100,
            }}
            onPress={onClose}
          >
            <Icon
              name="md-close"
              style={{ color: BaseColor.whiteColor, fontSize: 20 }}
            />
          </TouchableOpacity>
          {isObject(detail) &&
          !isEmpty(detail) &&
          campaignType !== "feed_post" &&
          campaignType !== "in_app_message" ? (
            <View
              style={{
                width: "100%",
                flexDirection: "row",
                alignItems: "center",
                padding: 12,
                paddingBottom: 0,
              }}
            >
              {detail?.icon_url ? (
                <Image
                  source={{ uri: detail.icon_url }}
                  style={{ height: 35, width: 35, borderRadius: 18 }}
                />
              ) : (
                <View
                  style={{
                    height: 35,
                    width: 35,
                    borderRadius: 18,
                    backgroundColor: "#ccc",
                  }}
                />
              )}
              <Text style={{ marginLeft: 10, fontSize: 18, fontWeight: "500" }}>
                {detail?.post_title || ""}
              </Text>
            </View>
          ) : campaignType === "feed_post" ? null : (
            <Text
              style={{
                textAlign:
                  titObj?.text_align !== "" ? titObj?.text_align : "center",
                color: titObj?.text_color !== "" ? titObj?.text_color : "#000",
                fontSize:
                  titObj?.text_size !== ""
                    ? Number(titObj?.text_size || 16)
                    : 16,
                padding: 12,
                paddingBottom: 0,
              }}
            >
              {titObj?.text_name}
            </Text>
          )}
          <Image
            style={{
              flex: 1,
              width: "100%",
              marginVertical: campaignType === "feed_post" ? 0 : 12,
              borderTopLeftRadius: campaignType === "feed_post" ? 22 : 0,
              borderTopRightRadius: campaignType === "feed_post" ? 22 : 0,
            }}
            source={{
              uri: image,
            }}
            resizeMode="cover"
          />
          {(isObject(detail) &&
            !isEmpty(detail) &&
            campaignType !== "in_app_message") ||
          campaignType === "feed_post" ? (
            <View style={{ marginVertical: 8, paddingHorizontal: 12 }}>
              <Text style={{ color: "#000", fontSize: 15, marginBottom: 10 }}>
                {campaignType === "feed_post"
                  ? detail.post_title
                  : detail?.post_subtitle || ""}
              </Text>
              <Text style={{ color: "#d3d3d3", fontSize: 13 }}>
                {detail?.message || ""}
              </Text>
            </View>
          ) : null}
          <View style={{ paddingHorizontal: 12, paddingBottom: 12 }}>
            {isArray(btnArr) &&
              !isEmpty(btnArr) &&
              btnArr.map((item, index) => (
                <TouchableOpacity
                  key={index}
                  style={{
                    backgroundColor:
                      item?.background_color !== ""
                        ? item?.background_color
                        : BaseColor.alertRed,
                    alignItems:
                      item?.button_text_align !== ""
                        ? item?.button_text_align
                        : "center",
                    padding: 12,
                    borderRadius: 12,
                    marginVertical: 8,
                  }}
                  onPress={() => handleUrl(item)}
                >
                  <Text
                    style={{
                      color:
                        item?.button_text_color !== ""
                          ? item?.button_text_color
                          : "#fafafa",
                      fontSize:
                        item?.button_text_size !== ""
                          ? Number(item?.button_text_size || 16)
                          : 16,
                    }}
                  >
                    {item.button_text || "Button 1"}
                  </Text>
                </TouchableOpacity>
              ))}
          </View>
        </View>
      </TouchableOpacity>
    </Modal>
  );
}
