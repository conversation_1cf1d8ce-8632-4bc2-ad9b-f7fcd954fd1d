/**
 * CarPlay Test Button Component
 * For testing CarPlay notifications and alerts functionality
 */

import React from 'react';
import { View, TouchableOpacity, Text, StyleSheet, Platform, Alert } from 'react-native';
import CarPlayService from '../services/CarPlayService';

const CarPlayTestButton = () => {
  
  const testCarPlayNotification = () => {
    if (Platform.OS !== 'ios') {
      Alert.alert('CarPlay Test', 'CarPlay is only available on iOS devices');
      return;
    }

    if (!CarPlayService.isCarPlayConnected()) {
      Alert.alert('CarPlay Test', 'CarPlay is not connected. Please connect your device to a CarPlay-enabled vehicle or simulator.');
      return;
    }

    // Test basic notification
    CarPlayService.showNotification({
      title: '🍼 Baby Monitor Test',
      message: 'This is a test notification from ChillBaby app',
      type: 'info'
    });
  };

  const testCarPlayAlert = () => {
    if (Platform.OS !== 'ios') {
      Alert.alert('CarPlay Test', 'CarPlay is only available on iOS devices');
      return;
    }

    if (!CarPlayService.isCarPlayConnected()) {
      Alert.alert('CarPlay Test', 'CarPlay is not connected. Please connect your device to a CarPlay-enabled vehicle or simulator.');
      return;
    }

    // Test alert dialog
    CarPlayService.showAlert({
      title: '⚠️ Test Alert',
      message: 'This is a test alert from ChillBaby app',
      actions: [
        {
          title: 'Test Action',
          onPress: () => console.log('Test action pressed')
        }
      ]
    });
  };

  const testBabyAlert = () => {
    if (Platform.OS !== 'ios') {
      Alert.alert('CarPlay Test', 'CarPlay is only available on iOS devices');
      return;
    }

    if (!CarPlayService.isCarPlayConnected()) {
      Alert.alert('CarPlay Test', 'CarPlay is not connected. Please connect your device to a CarPlay-enabled vehicle or simulator.');
      return;
    }

    // Test baby monitoring alert
    CarPlayService.showBabyAlert({
      type: 'temperature_high',
      temperature: 38.5,
      timestamp: new Date().toISOString()
    });
  };

  const checkCarPlayStatus = () => {
    const isConnected = CarPlayService.isCarPlayConnected();
    Alert.alert(
      'CarPlay Status', 
      `CarPlay is ${isConnected ? 'connected' : 'not connected'}`,
      [
        {
          text: 'OK',
          onPress: () => console.log('CarPlay status checked:', isConnected)
        }
      ]
    );
  };

  if (Platform.OS !== 'ios') {
    return null; // Don't show on Android
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>CarPlay Test Controls</Text>
      
      <TouchableOpacity style={styles.button} onPress={checkCarPlayStatus}>
        <Text style={styles.buttonText}>Check CarPlay Status</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.button} onPress={testCarPlayNotification}>
        <Text style={styles.buttonText}>Test Notification</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.button} onPress={testCarPlayAlert}>
        <Text style={styles.buttonText}>Test Alert</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.button} onPress={testBabyAlert}>
        <Text style={styles.buttonText}>Test Baby Alert</Text>
      </TouchableOpacity>

      <Text style={styles.instructions}>
        To test CarPlay functionality:{'\n'}
        1. Connect your iOS device to a CarPlay-enabled vehicle or use Xcode CarPlay simulator{'\n'}
        2. Tap the buttons above to test different CarPlay features{'\n'}
        3. Check the CarPlay display for notifications and alerts
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#f5f5f5',
    margin: 10,
    borderRadius: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
    color: '#333',
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 12,
    borderRadius: 8,
    marginVertical: 5,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  instructions: {
    marginTop: 15,
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
});

export default CarPlayTestButton;
