import React, { useState } from 'react';
// import { View, Text } from 'react-native';
import OTPInputView from '@twotalltotems/react-native-otp-input';
import { TouchableOpacity } from 'react-native';
import { useTheme } from '@react-navigation/native';
import styles from './styles';
import BaseColors from '../../config/colors';

export default function OtpComponent(props) {
  const colors = useTheme();
  const BaseColor = colors.colors;

  const {
    onCodeFilled,
    noOfBox = 4,
    otpWrapperStyle,
    codeInputFieldStyle,
    codeInputHighlightStyle,
    onCodeChanged,
    onotpPress,
    code,
  } = props;

  const [clearCode, setclearCode] = useState(false);
  // const [code, setcode] = useState();

  return (
    <>
      <OTPInputView
        style={{
          height: 100,
          fontSize: 20,
          paddingHorizontal: 30,
          justifyContent: 'center',
          alignItems: 'center',
          ...otpWrapperStyle,
        }}
        editable={false}
        code={code}
        pinCount={noOfBox}
        // autoFocusOnLoad
        codeInputFieldStyle={{
          ...styles.underlineStyleBase,
          backgroundColor: BaseColor.whiteColor,
          color: BaseColors.blackColor,
          borderWidth: 1,
          borderRadius: 12,
          borderColor: BaseColors.blackColor,
          ...codeInputFieldStyle,
        }}
        codeInputHighlightStyle={{
          ...styles.underlineStyleHighLighted,
          backgroundColor: BaseColor.whiteColor,
          color: BaseColors.blackColor,
          borderWidth: 1,
          borderRadius: 12,
          borderColor: BaseColors.blackColor,
          ...codeInputHighlightStyle,
        }}
        clearInputs={clearCode}
        onCodeFilled={(code) => {
          onCodeFilled(code);
          setclearCode(true);
          setTimeout(() => {
            setclearCode(false);
            // setcode();
          }, 1000);
        }}
        onCodeChanged={(code) => {
          onCodeChanged(code);
          // setcode(code);
        }}
      />
      <TouchableOpacity
        style={{
          backgroundColor: 'transparent',
          height: 100,
          width: '100%',
          position: 'absolute',
        }}
        onPress={onotpPress}
      />
    </>
  );
}
