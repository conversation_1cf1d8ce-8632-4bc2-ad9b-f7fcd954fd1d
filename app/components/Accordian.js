import { useTheme } from '@react-navigation/native';
import React, { useState } from 'react';
import {
  LayoutAnimation,
  Platform,
  Text,
  TouchableOpacity,
  UIManager,
  View,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { FontFamily } from '../config/typography';

export default function Accordian(props) {
  const colors = useTheme();
  const BaseColor = colors.colors;

  const { data, title } = props;

  const [expanded, setexpanded] = useState(false);

  if (Platform.OS === 'android') {
    UIManager.setLayoutAnimationEnabledExperimental(true);
  }

  const toggleExpand = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setexpanded(!expanded);
  };

  return (
    <View>
      <TouchableOpacity
        // ref={this.accordian}
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          height: 56,
          paddingLeft: 25,
          paddingRight: 18,
          alignItems: 'center',
          backgroundColor: BaseColor.blueDark,
        }}
        onPress={() => toggleExpand()}
      >
        <Text
          style={{
            fontSize: 14,

            fontWeight: 'bold',
            color: BaseColor.black90,
            fontFamily: FontFamily.default,
          }}
        >
          {title}
        </Text>
        <Icon
          name={expanded ? 'keyboard-arrow-up' : 'keyboard-arrow-down'}
          size={30}
          color={BaseColor.textGrey}
        />
      </TouchableOpacity>
      <View style={{ height: 1, color: BaseColor.whiteColor, width: '100%' }} />
      {expanded && (
        <View style={{ backgroundColor: BaseColor.black40, padding: 16 }}>
          <Text>{data}</Text>
        </View>
      )}
    </View>
  );
}
