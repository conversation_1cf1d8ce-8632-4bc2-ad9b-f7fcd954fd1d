import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Dimensions,
  Animated,
  Platform,
  Alert,
} from 'react-native';
import FAIcon from 'react-native-vector-icons/FontAwesome';
import LinearGradient from 'react-native-linear-gradient';
// import Animated from 'react-native-reanimated';
import { useTheme } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import { log } from 'react-native-reanimated';
import { isEmpty } from 'lodash';
import { CustomIcon } from '../config/LoadIcons';
import { FontFamily } from '../config/typography';
import BaseColors from '../config/colors';
import { translate } from '../lang/Translate';
import BaseSetting from '../config/setting';
import { getApiData } from '../utils/apiHelper';
import { sendErrorReport } from '../utils/commonFunction';
import CAlert from '../components/CAlert';

const TabBar = ({ state, descriptors, navigation }) => {
  const colors = useTheme();
  const BaseColor = colors.colors;

  useEffect(() => {
    setspring(state.index);
    setObject(state.routes[state.index]);
  }, [state]);
  const { isBleConnected, lastDeviceId } = useSelector(
    (bleState) => bleState.bluetooth
  );
  const token = useSelector((state) => state.auth.accessToken);
  const [isConnected, setIsConnected] = useState(true);
  const [tempAlert, setTempAlert] = useState({
    type: '',
    bool: false,
    title: '',
    message: '',
  });

  const selectedIndex = state.index;
  // const selectedObject = state.routes[state.index];
  const [selectedObject, setObject] = useState(state.routes[state.index]);

  const [translateValue, setTranslateValue] = useState(new Animated.Value(0));

  const focusedOptions = descriptors[state.routes[state.index].key].options;

  if (focusedOptions.tabBarVisible === false) {
    return null;
  }

  const tabWidth = Dimensions.get('window').width - 32;
  const sTabWidth = tabWidth / 3;

  const setspring = (index) => {
    console.log(index);
    const tabWidth = sTabWidth;
    Animated.spring(translateValue, {
      toValue: 1 + index * tabWidth,
      velocity: 10,
      useNativeDriver: true,
    }).start();
  };

  return (
    <View
      style={{
        flexDirection: 'row',
        width: tabWidth,
        // margin: 16,
        backgroundColor: BaseColors.whiteColor,
        justifyContent: 'center',
        alignItems: 'center',
        // borderTopEndRadius: 20,
        // borderBottomStartRadius: 20,
        borderRadius: 20,
        // overflow: 'hidden',
        borderWidth: 0.5,
        borderColor: BaseColor.textGrey,
        // shadowColor: '#000',
        // shadowOffset: {
        //   width: 0,
        //   height: 6,
        // },
        // shadowOpacity: 0.37,
        // shadowRadius: 7.49,
        // elevation: 12,
        alignSelf: 'center',
        marginBottom: Platform.OS === 'android' ? 30 : 50,
      }}
    >
      {state.routes.map((route, index) => {
        const { options } = descriptors[route.key];
        const label = options.tabBarLabel !== undefined
          ? options.tabBarLabel
          : options.title !== undefined
            ? options.title
            : route.name;

        const isFocused = state.index === index;

        const onPress = () => {
          const event = navigation.emit({
            type: 'tabPress',
            target: 1,
            canPreventDefault: true,
          });

          if (!isFocused && !event.defaultPrevented) {
            navigation.navigate(route.name);
          }
        };

        const onLongPress = () => {
          navigation.emit({
            type: 'tabLongPress',
            target: route.key,
          });
        };

        return (
          <View
            key={`tab_${label}`}
            style={{
              backgroundColor: BaseColors.witeColor,
              flex: 1,
              // borderBottomStartRadius: label === 'DEVICES' ? 20 : 0,
              // borderTopEndRadius: label === 'HOME' ? 40 : 0,
              borderRedius: 20,
              width: sTabWidth,
            }}
          >
            <TouchableOpacity
              accessibilityRole="button"
              accessibilityState={isFocused ? { selected: true } : {}}
              accessibilityLabel={options.tabBarAccessibilityLabel}
              testID={options.tabBarTestID}
              onPress={() => {
                if (
                  (!isBleConnected)
                  && route.name === translate('dashboard')
                ) {
                  setTempAlert({
                    type: '',
                    bool: true,
                    title: translate('noProductsConnected'),
                    message: '',
                  });
                } else {
                  setObject(route);
                  setspring(index);
                  onPress();
                }
              }}
              onLongPress={onLongPress}
              style={{
                flex: 1,
                height: 80,
                // backgroundColor: isFocused
                //   ? BaseColor.blueDark
                //   : BaseColor.whiteColor,
                // justifyContent: "space-evenly",
                // alignItems: "center",
                // borderBottomStartRadius: 40,
                borderRadius: 20,
                overflow: 'hidden',
              }}
            >
              <LinearGradient
                start={{ x: 0, y: 0 }}
                end={{ x: 0, y: 1 }}
                colors={
                  // isFocused
                  //   ? [BaseColor.blueLight, BaseColor.blueDark] :
                  [BaseColor.whiteColor, BaseColor.whiteColor]
                }
                style={{
                  flex: 1,
                  height: 80,
                  backgroundColor: isFocused
                    ? BaseColor.tabBackground
                    : BaseColor.whiteColor,
                  justifyContent: 'space-evenly',
                  alignItems: 'center',
                  overflow: 'hidden',
                }}
              >
                <CustomIcon
                  name={
                    label === `${translate('devices')}`
                      ? 'swap1'
                      : label === `${translate('dashboard')}`
                        ? 'user1'
                        : 'home1'
                  }
                  size={20}
                  color={isFocused ? BaseColor.whiteColor : BaseColor.textGrey}
                />
                <Text
                  style={{
                    color: isFocused
                      ? BaseColor.whiteColor
                      : BaseColor.textGrey,
                    fontSize: 12,
                    fontFamily: FontFamily.default,
                    fontWeight: 'bold',
                    letterSpacing: 1,
                    textAlign: 'center',
                  }}
                >
                  {label}
                </Text>
                {isFocused ? (
                  <FAIcon
                    name="circle"
                    size={8}
                    color={
                      isFocused ? BaseColor.whiteColor : BaseColor.textGrey
                    }
                  />
                ) : null}
              </LinearGradient>
            </TouchableOpacity>
          </View>
        );
      })}
      <Animated.View
        style={{
          position: 'absolute',
          left: -1,
          right: 0,
          bottom: 0,
          top: 0,
          alignItems: 'center',
          justifyContent: 'center',
          width: sTabWidth,
          borderRadius: 20,
          // borderTopEndRadius: 40,
          overflow: 'hidden',
          transform: [{ translateX: translateValue }],
        }}
      >
        <LinearGradient
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 1 }}
          colors={
            true
              ? [BaseColors.tabBackground, BaseColors.tabBackground]
              : [BaseColor.whiteColor, BaseColor.whiteColor]
          }
          style={{
            width: sTabWidth,
            height: 80,
            backgroundColor: true ? BaseColor.blueDark : BaseColor.whiteColor,
            justifyContent: 'space-evenly',
            alignItems: 'center',
            overflow: 'hidden',
          }}
        >
          <CustomIcon
            name={
              selectedObject.name === `${translate('devices')}`
                ? 'swap1'
                : selectedObject.name === `${translate('dashboard')}`
                  ? 'user1'
                  : 'home1'
            }
            size={20}
            color={true ? BaseColor.whiteColor : BaseColor.textGrey}
          />
          <Text
            style={{
              color: true ? BaseColor.whiteColor : BaseColor.textGrey,
              fontSize: 12,
              fontFamily: FontFamily.default,
              fontWeight: 'bold',
              letterSpacing: 1,
              textAlign: 'center',
            }}
          >
            {selectedObject.name}
          </Text>
          {true ? (
            <FAIcon
              name="circle"
              size={8}
              color={true ? BaseColor.whiteColor : BaseColor.textGrey}
            />
          ) : null}
        </LinearGradient>
      </Animated.View>
      <CAlert
        visible={tempAlert.bool} // Only visible if there are no product connected
        type={tempAlert.type}
        onCancelPress={() => setTempAlert({
          type: '',
          bool: false,
          title: '',
          message: '',
        })}
        alertTitle={translate(tempAlert.title)}
        alertMessage={translate(tempAlert.message)}
        onOkPress={() => setTempAlert({
          type: '',
          bool: false,
          title: '',
          message: '',
        })}
      />
    </View>
  );
};

export default TabBar;
