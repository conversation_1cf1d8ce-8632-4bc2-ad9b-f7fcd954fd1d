/* eslint-disable quotes */
/* eslint-disable no-undef */
const devMode = __DEV__;

// const baseUrl = devMode
//   ? 'http://192.168.2.112:8090/'
//   : 'https://dev-api.chillbaby.tech/';
const baseUrl = 'https://api.chillbaby-test.io/';
// const baseUrl = 'http://192.168.1.76:1337/';
const BaseSetting = {
  name: 'ChillB<PERSON>',
  displayName: 'ChillBaby',
  appVersionCode: '1',
  // bugsnagApiKey: "97983f80d92e9c63fa56df79f1252515",
  baseUrl,
  socketUrl: baseUrl,
  api: `${baseUrl}api/`,
  // ? 'http://192.168.0.137/kashtah/kahstahApi/backend/v1/'
  // : `${baseUrl}/api/`,
  shareEndPoint: baseUrl,
  endpoints: {
    signUp: 'user/signup',
    login: 'user/login',
    childProfile: 'user/add-child',
    gender: 'user/update-gender',
    forgotPassword: 'user/forgot-password',
    updatePassword: 'user/update-password',
    otp: 'user/check-otp',
    addToken: 'user/add-token',
    deleteToken: 'delete-token',
    getUserChild: 'getUserChild',
    sendOtp: 'user/send-otp',
    productList: 'getProductDetails',
    sendPackage: 'getTokenByPackage',
    sendNotifi: 'sendUserNotification',
    getPost: 'campaigns/get-post',
    uploadChatAttachment: 'uploadChatAttachment',
    getAppChat: 'chat/get-app-chat',
    insertAttachment: 'insertAttachment',
    getLanguageList: 'getLanguageList',
    getFeed: 'campaigns/add-action',
    addUserProduct: 'addUserProduct',
    updateChild: 'user/update-child',
    getDevice: 'brand_devices/get-device',
    connectedDevice: 'getUserConnectedDevices',
    getFeedPost: 'campaigns/get-feed-post',
    addAction: 'campaigns/add-action',
    getAlerts: 'alerts/get-alerts',
    addAlert: 'alerts/add-alert',
    clearAlerts: 'alerts/clear-alerts',
    notificationStatus: 'user/change-notification-status',
    editDeviceChild: 'editDeviceChild',
    deleteChildDevice: 'deleteChildDevice',
    faqsList: 'brand_faqs/get-list',
    getProductCategories: 'getProductCategories',
    disconnectChildDevice: 'disconnectChildDevice',
    setUserLogActiveTime: 'setUserLogActiveTime',
    sendEmergencyMessageToUser: 'sendEmergencyMessageToUser',
    saveDeviceDataInfo: 'saveDeviceDataInfo',
    saveErrorLog: 'saveErrorLog',
    campaignlike: 'campaigns/campaign-like',
    addproductaction: 'product/add-product-action',
    readNotification: 'readNotification',
    getUserAlertCount: 'getUserAlertCount',
    removeChild: 'user/remove-child',
    autoConn: 'autoConnectChildDevice',
    deleteAccount: 'user/delete-account',
    getDeviceConnectStatus: 'getDeviceConnectStatus',
    userManual: 'brand_devices/user-manual-list',
    updateMessageSendRecord: 'updateMessageSendRecord',
    ecomProductList: 'e-commerce/app-product-list',
    ecomCatalougeList: 'e-commerce/collections-list',
    addEcomProductAction: 'e-commerce/product-action',
  },
};

export default BaseSetting;
