/* eslint-disable no-case-declarations */
/* eslint-disable no-console */
import { find, findIndex, isEmpty } from 'lodash';
import types from './actions';
import { store } from '../../store/configureStore';

const initialState = {
  deviceID: '',
  lastDeviceId: '',
  characteristicID: '',
  serviceID: '',
  lowAlert: '8',
  highAlert: '26',
  lowHAlert: '30',
  highHAlert: '60',
  alertTime: {
    temp: 0,
    hum: 0,
    air: 0,
    voc: 0,
  },
  bleData: {},
  currentBleData: {},
  isBleConnected: false,
  emergencyAlert: false,
  isLeftChildAlert: false,
  activeChildDetail: {},
  connectedDeviceDetail: [],
  deviceDetail: [],
  isCurrentActiveDevice: {},
  tempModal: false,
  humdtModal: false,
  activeDeviceId: {},
  swiperKey: '',
  connectedDeviceDetails: [],
  isConnected: false,
  isConvert: false,
  isConnecting: false,
  isHomeDeviceClick: false,
  isClickAddQr: false,
  isSetMtu: true,
  bleDeviceList: [],
  isSkipShow: false,
  isChildProductId: {},
  is60secTimeOut: false,
  smsSentAndroidBg: false,
  timerVal: 60,
  cancelSMS: false,
  deletedRecent: false,
  isFromScanner: false,
  alertData: {},
  deviceDisconnect: false,
  carplayState: false,
};

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case types.SET_DEVICE_ID:
      return {
        ...state,
        deviceID: action.deviceID,
      };
    case types.SET_LAST_DEVICE_ID:
      return {
        ...state,
        lastDeviceId: action.lastDeviceId,
      };
    case types.SET_BLEDATA:
      return {
        ...state,
        bleData: action.bleData,
      };
    case types.SET_ISBLECONNECTED:
      return {
        ...state,
        isBleConnected: action.isBleConnected,
      };
    case types.SET_LOWALERT:
      return {
        ...state,
        lowAlert: action.lowAlert,
      };
    case types.SET_LOWHALERT:
      return {
        ...state,
        lowHAlert: action.lowHAlert,
      };
    case types.SET_ALERTTIME:
      return {
        ...state,
        alertTime: action.alertTime,
      };

    case types.SET_HIGHALERT:
      return {
        ...state,
        highAlert: action.highAlert,
      };
    case types.SET_HIGHHALERT:
      return {
        ...state,
        highHAlert: action.highHAlert,
      };
    case types.SET_CHARACTERISTIC_ID:
      return {
        ...state,
        characteristicID: action.characteristicID,
      };
    case types.SET_SERVICE_ID:
      return {
        ...state,
        serviceID: action.serviceID,
      };
    case types.SET_EMERGENCY_ALERT:
      return {
        ...state,
        emergencyAlert: action.emergencyAlert,
      };
    case types.SET_LEFT_CHILD_ALERT:
      return {
        ...state,
        isLeftChildAlert: action.isLeftChildAlert,
      };
    case types.SET_CONNECTED_DEVICE_DETAIL:
      return {
        ...state,
        connectedDeviceDetail: action.connectedDeviceDetail,
      };
    case types.SET_DEVICE_DETAIL:
      return {
        ...state,
        deviceDetail: action.deviceDetail,
      };
    case types.SET_ACTIVE_CHILD_DETAIL:
      return {
        ...state,
        activeChildDetail: action.activeChildDetail,
      };
    case types.SET_TEMP_MODAL:
      return {
        ...state,
        tempModal: action.tempModal,
      };
    case types.SET_HUMDT_MODAL:
      return {
        ...state,
        humdtModal: action.humdtModal,
      };
    case types.SET_CURRENT_ACTIVE_DEVICE:
      return {
        ...state,
        isCurrentActiveDevice: action.isCurrentActiveDevice,
      };
    case types.SET_ACTIVE_DEVICE_ID:
      return {
        ...state,
        activeDeviceId: action.activeDeviceId,
      };
    case types.SET_CURRENT_BLEDATA:
      return {
        ...state,
        currentBleData: action.currentBleData,
      };
    case types.SET_SWIPER_KEY:
      return {
        ...state,
        swiperKey: action.swiperKey,
      };
    case types.CONNECTED_DEVICE_DETAIL:
      return {
        ...state,
        connectedDeviceDetails: action.connectedDeviceDetails,
      };
    case types.SET_IS_CONNECTED:
      return {
        ...state,
        isConnected: action.isConnected,
      };
    case types.SET_CONVERT_TEMP:
      return {
        ...state,
        isConvert: action.isConvert,
      };
    case types.SET_CONNECTING:
      return {
        ...state,
        isConnecting: action.isConnecting,
      };
    case types.SET_HOME_DEVICE_CLICK:
      return {
        ...state,
        isHomeDeviceClick: action.isHomeDeviceClick,
      };
    case types.SET_CLICK_ADD_QR:
      return {
        ...state,
        isClickAddQr: action.isClickAddQr,
      };
    case types.SET_REQUEST_MTU:
      return {
        ...state,
        isSetMtu: action.isSetMtu,
      };
    case types.SET_BLE_DEVICE_LIST:
      return {
        ...state,
        bleDeviceList: action.bleDeviceList,
      };
    case types.SET_SKIP_SHOW:
      return {
        ...state,
        isSkipShow: action.isSkipShow,
      };
    case types.SET_CHILD_PRODUCT_ID:
      return {
        ...state,
        isChildProductId: action.isChildProductId,
      };
    case types.SET_60_SEC_TIMEOUT:
      return {
        ...state,
        is60secTimeOut: action.is60secTimeOut,
      };
    case types.SET_SMS_SENT_ANDROID_BG:
      return {
        ...state,
        smsSentAndroidBg: action.smsSentAndroidBg,
      };
    case types.SET_TIMER_VALUE:
      return {
        ...state,
        timerVal: action.timerVal,
      };
    case types.SET_CANCEL_SMS:
      return {
        ...state,
        cancelSMS: action.cancelSMS,
      };
    case types.SET_DELETED_RECENT:
      return {
        ...state,
        deletedRecent: action.deletedRecent,
      };
    case types.SET_IS_FROM_SCANNER:
      return {
        ...state,
        isFromScanner: action.isFromScanner,
      };
    case types.SET_ALERT_DATA_REDUX:
      return {
        ...state,
        alertData: action.alertData,
      };
    case types.SET_DEVICE_DISCONNECT:
      return {
        ...state,
        deviceDisconnect: action.deviceDisconnect,
      };
    case types.SET_CARPLAY_STATE:
      return {
        ...state,
        carplayState: action.carplayState,
      };
    default:
      return state;
  }
}
