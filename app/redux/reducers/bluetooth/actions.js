/* eslint-disable quotes */
const actions = {
  SET_DEVICE_ID: 'auth/SET_DEVICE_ID',
  SET_LAST_DEVICE_ID: 'auth/SET_LAST_DEVICE_ID',
  SET_CHARACTERISTIC_ID: 'auth/SET_CHARACTERISTIC_ID',
  SET_SERVICE_ID: 'auth/SET_SERVICE_ID',
  SET_LOWALERT: 'auth/SET_LOWALERT',
  SET_HIGHALERT: 'auth/SET_HIGHALERT',
  SET_LOWHALERT: 'auth/SET_LOWHALERT',
  SET_HIGHHALERT: 'auth/SET_HIGHHALERT',
  SET_ALERTTIME: 'auth/SET_ALERTTIME',
  SET_BLEDATA: 'auth/SET_BLEDATA',
  SET_ISBLECONNECTED: 'auth/SET_ISBLECONNECTED',
  SET_EMERGENCY_ALERT: 'alert/SET_EMERGENCY_ALERT',
  SET_CONNECTED_DEVICE_ID: 'device/SET_CONNECTED_DEVICE_ID',
  SET_CONNECTED_DEVICE_NAME: 'device/SET_CONNECTED_DEVICE_NAME',
  SET_ACTIVE_CHILD_DETAIL: 'device/SET_ACTIVE_CHILD_DETAIL',
  SET_CONNECTED_DEVICE_DETAIL: 'device/SET_CONNECTED_DEVICE_DETAIL',
  SET_TEMP_MODAL: 'device/SET_TEMP_MODAL',
  SET_HUMDT_MODAL: 'device/SET_HUMDT_MODAL',
  SET_CURRENT_ACTIVE_DEVICE: 'device/SET_CURRENT_ACTIVE_DEVICE',
  SET_ACTIVE_DEVICE_ID: 'device/SET_ACTIVE_DEVICE_ID',
  SET_CURRENT_BLEDATA: 'device/SET_CURRENT_BLEDATA',
  SET_SWIPER_KEY: 'device/SET_SWIPER_KEY',
  SET_DEVICE_DETAIL: 'device/SET_DEVICE_DETAIL',
  SET_IS_CONNECTED: 'device/SET_IS_CONNECTED',
  SET_CONVERT_TEMP: 'device/SET_CONVERT_TEMP',
  SET_CONNECTING: 'device/SET_CONNECTING',
  SET_HOME_DEVICE_CLICK: 'device/SET_HOME_DEVICE_CLICK',
  SET_LEFT_CHILD_ALERT: 'device/SET_LEFT_CHILD_ALERT',
  CONNECTED_DEVICE_DETAIL: 'device/CONNECTED_DEVICE_DETAIL',
  SET_CLICK_ADD_QR: 'device/SET_CLICK_ADD_QR',
  SET_REQUEST_MTU: 'device/SET_REQUEST_MTU',
  SET_BLE_DEVICE_LIST: 'device/SET_BLE_DEVICE_LIST',
  SET_SKIP_SHOW: 'device/SET_SKIP_SHOW',
  SET_CHILD_PRODUCT_ID: 'device/SET_CHILD_PRODUCT_ID',
  SET_60_SEC_TIMEOUT: 'device/SET_60_SEC_TIMEOUT',
  SET_SMS_SENT_ANDROID_BG: 'device/SET_SMS_SENT_ANDROID_BG',
  SET_TIMER_VALUE: 'device/SET_TIMER_VALUE',
  SET_CANCEL_SMS: 'device/SET_CANCEL_SMS',
  SET_DELETED_RECENT: 'device/SET_DELETED_RECENT',
  SET_IS_FROM_SCANNER: 'device/SET_IS_FROM_SCANNER',
  SET_ALERT_DATA_REDUX: 'device/SET_ALERT_DATA_REDUX',
  SET_DEVICE_DISCONNECT: 'device/SET_DEVICE_DISCONNECT',
  SET_CARPLAY_STATE: 'device/SET_CARPLAY_STATE',

  setDeviceID: deviceID => dispatch =>
    dispatch({
      type: actions.SET_DEVICE_ID,
      deviceID,
    }),

  setLastDeviceId: lastDeviceId => dispatch =>
    dispatch({
      type: actions.SET_LAST_DEVICE_ID,
      lastDeviceId,
    }),

  setIsBleConnected: isBleConnected => dispatch =>
    dispatch({
      type: actions.SET_ISBLECONNECTED,
      isBleConnected,
    }),

  setBleData: bleData => dispatch =>
    dispatch({
      type: actions.SET_BLEDATA,
      bleData,
    }),

  setAlertTime: alertTime => dispatch =>
    dispatch({
      type: actions.SET_ALERTTIME,
      alertTime,
    }),

  setLowAlert: lowAlert => dispatch =>
    dispatch({
      type: actions.SET_LOWALERT,
      lowAlert,
    }),

  setHighAlert: highAlert => dispatch =>
    dispatch({
      type: actions.SET_HIGHALERT,
      highAlert,
    }),

  setLowHAlert: lowHAlert => dispatch =>
    dispatch({
      type: actions.SET_LOWHALERT,
      lowHAlert,
    }),
  setHighHAlert: highHAlert => dispatch =>
    dispatch({
      type: actions.SET_HIGHHALERT,
      highHAlert,
    }),
  setcharacteristicID: characteristicID => dispatch =>
    dispatch({
      type: actions.SET_CHARACTERISTIC_ID,
      characteristicID,
    }),

  setServiceID: serviceID => dispatch =>
    dispatch({
      type: actions.SET_SERVICE_ID,
      serviceID,
    }),

  setEmergencyAlert: emergencyAlert => dispatch =>
    dispatch({
      type: actions.SET_EMERGENCY_ALERT,
      emergencyAlert,
    }),

  setLeftChildAlert: isLeftChildAlert => dispatch =>
    dispatch({
      type: actions.SET_LEFT_CHILD_ALERT,
      isLeftChildAlert,
    }),

  setClickAddQr: isClickAddQr => dispatch =>
    dispatch({
      type: actions.SET_CLICK_ADD_QR,
      isClickAddQr,
    }),

  setRequestMTU: isSetMtu => dispatch =>
    dispatch({
      type: actions.SET_REQUEST_MTU,
      isSetMtu,
    }),

  setConnectedDeviceDetail: connectedDeviceDetail => dispatch =>
    dispatch({
      type: actions.SET_CONNECTED_DEVICE_DETAIL,
      connectedDeviceDetail,
    }),
  setHomeDeviceClick: isHomeDeviceClick => dispatch =>
    dispatch({
      type: actions.SET_HOME_DEVICE_CLICK,
      isHomeDeviceClick,
    }),

  setDeviceDetail: deviceDetail => dispatch =>
    dispatch({
      type: actions.SET_DEVICE_DETAIL,
      deviceDetail,
    }),

  setActiveChildDetail: activeChildDetail => dispatch =>
    dispatch({
      type: actions.SET_ACTIVE_CHILD_DETAIL,
      activeChildDetail,
    }),

  setTempModal: tempModal => dispatch =>
    dispatch({
      type: actions.SET_TEMP_MODAL,
      tempModal,
    }),
  setHumdtModal: humdtModal => dispatch =>
    dispatch({
      type: actions.SET_HUMDT_MODAL,
      humdtModal,
    }),
  setCurrentActiveDevice: isCurrentActiveDevice => dispatch =>
    dispatch({
      type: actions.SET_CURRENT_ACTIVE_DEVICE,
      isCurrentActiveDevice,
    }),

  setActiveDeviceId: activeDeviceId => dispatch =>
    dispatch({
      type: actions.SET_ACTIVE_DEVICE_ID,
      activeDeviceId,
    }),

  setCurrentBleData: currentBleData => dispatch =>
    dispatch({
      type: actions.SET_CURRENT_BLEDATA,
      currentBleData,
    }),

  setConnectedDeviceDetails: connectedDeviceDetails => dispatch =>
    dispatch({
      type: actions.CONNECTED_DEVICE_DETAIL,
      connectedDeviceDetails,
    }),

  setIsConnected: isConnected => dispatch =>
    dispatch({
      type: actions.SET_IS_CONNECTED,
      isConnected,
    }),
  setSwiperKey: swiperKey => dispatch =>
    dispatch({
      type: actions.SET_SWIPER_KEY,
      swiperKey,
    }),
  setIsConvert: isConvert => dispatch =>
    dispatch({
      type: actions.SET_CONVERT_TEMP,
      isConvert,
    }),
  setIsConnectLoad: isConnecting => dispatch =>
    dispatch({
      type: actions.SET_CONNECTING,
      isConnecting,
    }),
  setBleDeviceList: bleDeviceList => dispatch =>
    dispatch({
      type: actions.SET_BLE_DEVICE_LIST,
      bleDeviceList,
    }),
  setSkipShow: isSkipShow => dispatch =>
    dispatch({
      type: actions.SET_SKIP_SHOW,
      isSkipShow,
    }),
  setChildProductId: isChildProductId => dispatch =>
    dispatch({
      type: actions.SET_CHILD_PRODUCT_ID,
      isChildProductId,
    }),
  set60secTimeOut: is60secTimeOut => dispatch =>
    dispatch({
      type: actions.SET_CHILD_PRODUCT_ID,
      is60secTimeOut,
    }),
  setSMSSentAndroidBg: smsSentAndroidBg => dispatch =>
    dispatch({
      type: actions.SET_CHILD_PRODUCT_ID,
      smsSentAndroidBg,
    }),
  settimerValue: timerVal => dispatch =>
    dispatch({
      type: actions.SET_TIMER_VALUE,
      timerVal,
    }),
  setcancelSMS: cancelSMS => dispatch =>
    dispatch({
      type: actions.SET_CANCEL_SMS,
      cancelSMS,
    }),
  setDeletedRecent: deletedRecent => dispatch =>
    dispatch({
      type: actions.SET_DELETED_RECENT,
      deletedRecent,
    }),
  setIsFromScanner: isFromScanner => dispatch =>
    dispatch({
      type: actions.SET_IS_FROM_SCANNER,
      isFromScanner,
    }),
  setAlertDataRedux: alertData => dispatch =>
    dispatch({
      type: actions.SET_ALERT_DATA_REDUX,
      alertData,
    }),
  setDeviceDisconnect: deviceDisconnect => dispatch =>
    dispatch({
      type: actions.SET_DEVICE_DISCONNECT,
      deviceDisconnect,
    }),
  setCarPlayState: carplayState => dispatch =>
    dispatch({
      type: actions.SET_CARPLAY_STATE,
      carplayState,
    }),
};

export default actions;
