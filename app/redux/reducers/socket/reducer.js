import types from './actions';

const initialState = {
  socketData: {},
  notificationData: {},
  IOSocket: null,
  chatData: {},
  typing: false,
  typingData: {},
  totalMsgCount: 0,
};

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case types.SET_SOCKET_DATA:
      console.log(`${types.SET_SOCKET_DATA} => `);
      return {
        ...state,
        IOSocket: action.data,
        socketData: action.socketData,
      };
    case types.SET_RECEIVED_CHAT_DATA:
      console.log(`${types.SET_RECEIVED_CHAT_DATA} ==>`);
      // CAlert(JSON.stringify(action.chatData));
      console.log('action.chatData =>');
      console.log(action.chatData);
      return {
        ...state,
        chatData: action.chatData,
      };

    case types.CLEAR_CHAT_DATA:
      console.log(`${types.CLEAR_CHAT_DATA} => `);
      return {
        ...state,
        chatData: {},
      };

    case types.SET_TYPING:
      console.log(`${types.SET_TYPING} => `);
      return {
        ...state,
        typing: action.typing,
        typingData: action.typingData,
      };

    case types.SET_TOTAL_MSG_COUNT:
      console.log(`${types.SET_TOTAL_MSG_COUNT} => `);
      return {
        ...state,
        totalMsgCount: action.data,
      };
    case types.SET_NOTIFICATION_DATA:
      console.log(`${types.SET_NOTIFICATION_DATA} => `);
      return {
        ...state,
        notificationData: action.notificationData,
      };
    default:
      return state;
  }
}
