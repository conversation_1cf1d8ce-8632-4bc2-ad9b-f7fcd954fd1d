/* eslint-disable quotes */
/* eslint-disable import/no-cycle */
import { isObject, isUndefined } from "lodash";
import BaseSetting from "../../../config/setting";
import { getApiData } from "../../../utils/apiHelper";
import { sendErrorReport, setUserLog } from "../../../utils/commonFunction";
// import authActions from "../auth/actions";

const io = require("sails.io.js")(require("socket.io-client"));

if (io) {
  io.sails.url = BaseSetting.socketUrl;
  io.sails.autoConnect = false;
  io.sails.useCORSRouteToGetCookie = false;
}
const actions = {
  SET_SOCKET_DATA: "socket/SET_SOCKET_DATA",
  // SET_NOTIFICATION_DATA: 'socket/SET_NOTIFICATION_DATA',
  EMIT_SOCKET: "socket/EMIT_SOCKET",
  SET_RECEIVED_CHAT_DATA: "socket/SET_RECEIVED_CHAT_DATA",
  CLEAR_CHAT_DATA: "socket/CLEAR_CHAT_DATA",
  SET_TYPING: "socket/SET_TYPING",
  SET_TOTAL_MSG_COUNT: "socket/SET_TOTAL_MSG_COUNT",

  initilization: () => (dispatch, getState) => {
    const { IOSocket } = getState().socket;
    const { accessToken } = getState().auth;
    console.log("token===>>>", accessToken);
    if (!IOSocket) {
      const socket = io.sails.connect();
      const headers = {
        "Content-Type": "application/json",
        authorization: accessToken ? `Bearer ${accessToken}` : "",
      };
      const options = {
        method: "POST",
        url: "/api/chat/connect",
        headers,
      };
      // const options = {
      //   method: 'POST',
      //   url: 'http://192.168.0.137:8090',
      //   headers,
      // };
      console.log("options", options);
      socket.request(options, (resData) => {
        console.log("Sails responded with connect: ", resData);
        dispatch({
          type: actions.SET_SOCKET_DATA,
          data: socket,
          socketData:
            isObject(resData) && isObject(resData.data) ? resData.data : null,
        });

        if (resData?.data?.socket_id) {
          setUserLog(resData?.data?.socket_id, accessToken);
        }
      });
      // RECIEVE NOTIFICATION
      // socket.on('notification', (noti) => {
      //   console.log('Socket Notification===>', noti);
      //   if (isObject(noti) && !isEmpty(noti)) {
      //     dispatch({
      //       type: actions.SET_NOTIFICATION_DATA,
      //       data: noti,
      //       notificationData: noti,
      //     });
      //   }
      // });

      socket.on("message", (data) => {
        console.log("socket Receive Data =======>");
        console.log("data===>");
        console.log(data);
        const { token } = getState().auth;
        dispatch(actions.onReceive(data));
      });

      socket.on("isTyping", (data) => {
        console.log("socket receiveTyping =======>");
        console.log("data===>");
        console.log(data);
        dispatch(actions.onReceiveTyping(data, "start"));
      });

      socket.on("stopTyping", (data) => {
        console.log("socket stopTyping =======>");
        console.log("data===>");
        console.log(data);
        dispatch(actions.onReceiveTyping(data, "stop"));
      });

      // Disconnect
      socket.on("disconnect", () => {
        console.log("SOCKET DISCONNECTED ====>");
        dispatch({
          type: actions.SET_SOCKET_DATA,
          data: null,
          socketData: null,
        });
      });
    }
  },

  emit: (event, data, callback) => (dispatch, getState) => {
    const socket = io.sails.connect();
    const { socketObj } = getState().socket;
    const { accessToken } = getState().auth;
    console.log(socketObj);
    if (socketObj !== null) {
      console.log(`${event} => emit`);
      console.log(data);
      const optionsReq = {
        url: event,
        data,
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          authorization: accessToken ? `Bearer ${accessToken}` : "",
        },
      };
      console.log("optionsReq????", optionsReq);
      socket.request(optionsReq, (resData) => {
        console.log("Sails responded with: ", resData);
        console.log("resData????", resData);

        if (callback) {
          callback(resData);
        }
        if (
          (event === "userconnect" || event === "getUnreadMessageTotalCount") &&
          !isUndefined(resData)
        ) {
          dispatch(actions.setTotalMsgCount(resData));
        }
      });
      // socketObj.emit(event, data, (callBackData) => {
      //   console.log('callBackData===>?');
      //   console.log(callBackData);
      //   if (callback) {
      //     callback(callBackData);
      //   }
      //   if (
      //     (event === 'userconnect' || event === 'getUnreadMessageTotalCount')
      //     && !_.isUndefined(callBackData)
      //   ) {
      //     dispatch(actions.setTotalMsgCount(callBackData));
      //   }
      // });
    }
  },

  onReceive: (chatData) => (dispatch, getState) => {
    if (isObject(chatData)) {
      const cdata = chatData;
      cdata.move = "chat";
      console.log(cdata);
      dispatch({
        type: actions.SET_RECEIVED_CHAT_DATA,
        chatData: cdata,
      });
    }
  },

  sendRequest: (url, data = null, callBack = () => {}) => (
    dispatch,
    getState,
  ) => {
    const { IOSocket } = getState().socket;
    console.log("🚀 ~ file: actions.js ~ line 73 ~ IOSocket", IOSocket);
    const { token } = getState().auth.accessToken;
    const headers = {
      "Content-Type": "application/json",
      authorization: token ? `Bearer ${token}` : "",
    };
    console.log("REQUEST URL===>", url);
    console.log("REQUEST data===>", data);
    if (token && headers) headers.Authorization = `Bearer ${token}`;
    const optionsReq = {
      url,
      data,
      method: "POST",
      headers,
    };
    if (IOSocket) {
      IOSocket.request(optionsReq, (resData) => {
        console.log("Sails responded with: ", resData);
        if (resData && resData.message === "Unauthorized") {
          setTimeout(() => {
            getApiData("delete-token", "post", { token })
              .then((result) => {
                if (result) {
                  console.log(".then -> result", result);
                  // dispatch(authActions.setToken({}));
                  // dispatch(filterActions.setDocFilter({}));
                  // dispatch(filterActions.setSharedDocFilter({}));
                  // dispatch(filterActions.setTemplateDocFilter({}));
                  // dispatch(filterActions.setWorkFlowDocFilter({}));
                  // dispatch(filterActions.setSharedWorkFlowDocFilter({}));
                  // dispatch(filterActions.setReportFilter({}));
                  // dispatch(authActions.setPlanShown(false));
                }
              })
              .catch((err) => {
                console.log(
                  `🚀 ~ file: actions.js ~ line 96 ~ .then ~ resul Error: ${err}`,
                );
                sendErrorReport(err, "socket_send_req");
              });
          }, 500);
        } else {
          callBack(resData);
        }
      });
    }
  },

  setTotalMsgCount: (data) => (dispatch) => {
    dispatch({
      type: actions.SET_TOTAL_MSG_COUNT,
      data,
    });
  },

  onReceiveTyping: (data, type) => (dispatch, getState) => {
    dispatch({
      type: actions.SET_TYPING,
      typing: type === "start",
      typingData: isObject(data) ? data : {},
    });
  },

  clearChatData: () => (dispatch, getState) => {
    dispatch({
      type: actions.CLEAR_CHAT_DATA,
    });
  },

  setSocketData: (socketData) => (dispatch) =>
    dispatch({
      type: actions.SET_SOCKET_DATA,
      socketData,
    }),
};

export default actions;
