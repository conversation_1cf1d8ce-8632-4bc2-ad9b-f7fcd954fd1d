{"text": "TEXT", "welcomeToBaby": "Welcome to BabyAuto", "functionalComfortable": "Happy and comfortable trips with all the safety that they deserve", "login": "LOGIN", "signup": "Signup", "loginscreen": "<PERSON><PERSON>", "loginUser": "ENTER MOBILE / USER NAME", "loginPassword": "PASSWORD", "loginRemember": "REMEMBER ME", "loginBtn": "LOGIN", "loginForgot": "FORGOT PASSWORD", "loginAccount": "Don't Have An Account?", "loginToSignup": "SIGNUP", "forgotScreen": "FORGOT PASSWORD?", "forgotEmail": "Enter the Email associated with your account.", "forgotInput": "PHONE NUMBER", "forgotBtn": "RESET", "otpScreen": "We Sent a code", "otpText": "Enter the Code sent to your Phone number and Email", "otpEmail": "Enter the OTP sent to associated Email", "otpBtn": "SUBMIT", "otpResend": "RESEND", "genderScreen": "SELECT GENDER", "genderFemale": "Female", "genderMale": "Male", "qrScannerScreen": "DEVICES", "qrScannerText": "Using your phones camera, scan your\nunique QR code which can be found on your\nBaby Auto.", "qrScannerLink": "Click here if you are using non SMART product", "connectScreen": "Connected!", "connectSuccessText": "You Have Successfully connected The\nDevice", "noChild": "No Child Profile Found", "pleaseAdd": "Please add a child profile to continue", "childProfileScreen": "CHILD PROFILE", "chooseProfileText": "CHOOSE PROFILE", "addNew": "+ ADD NEW", "tapToAddText": "Tap to add new profile", "infoTitle": "INFO", "nickName": "NICKNAME", "age": "AGE", "fewWordInputText": "FEW WORDS ABOUT ME", "height": "HEIGHT (cm)", "weight": "WEIGHT (kg) ", "devices": "DEVICES", "dashboard": "DASHBOARD", "home": "HOME", "deviceThreeText": "Device Connected", "activeDevice": "Active", "deactivedDevice": "Deactivated", "tapToAddNewProfile": "Tap To Add New Profile", "homeAddSmartDevice": " Add SMART Device", "homeAlertMsg": "The car seat is loose or not connected properly, Please ensure the safety before continuing journey", "homeAlertTitle": "<PERSON> Seat Alert", "alertOkBtn": "OK", "alertCancelBtn": "CANCEL", "homeVideoGuideText": "Top tip for installing a car seat", "settingScreen": "SETTINGS", "tempText": "Temperature setting", "pushText": "Push Notification", "darkmodeText": "Dark Mode", "customerText": "Customer Service", "tempC": "C", "tempF": "F", "modeOn": "On", "modeOff": "Off", "about": "About", "support": "FAQ'S & Support", "privacypolicy": "Privacy Policy", "version": "VERSION 1.0", "alertScreen": "ALERTS", "tempAlert": "High Temperature Alert", "tempAlertText": "11 Min <PERSON><PERSON>", "strollerAlert": "<PERSON>'s Stroller", "strollerText": "Today, 11:22PM", "strollerMsg": "<PERSON>", "babyInSeatAlert": "Baby In Seat Alert", "babyInSeatTime": "2Hr Ago", "babyOutOfSeatAlert": "<PERSON> Out of Seat", "babyOutOfSeatTime": "2Hr Ago", "listenIntoAlert": "Listen Into The Alert", "connectedDeviceName": "<PERSON><PERSON>", "charlie": "<PERSON>", "smartcushion": "SMART cushion", "chatScreenTitle": "<PERSON> - Baby Auto", "chatInputText": "Type An Answer", "fullName": "FULL NAME", "updatePasswordScreen": "UPDATE PASSWORD", "newPassword": "New Password", "confirmPassword": "Confirm Password", "logout": "Logout", "emailId": "EMAIL", "agreeTo": "Agree to", "termNCondition": "terms & conditions", "whatsNew": "WHAT'S NEW", "dob": "Birthday", "faqScreen": "FAQ'S & Support", "selectChild": "Select Child", "deleteDevice": "DELETE DEVICE", "changeDevice": "CHANGE DEVICE", "editDevice": "EDIT DEVICE", "assignDevice": "ASSIGN DEVICE", "children": "CHILDREN", "products": "Products", "productsCatalouge": "Product Catalouge", "productDetail": "Product Detail", "technicalDrawing": "Product video", "faqNManuals": "FAQ & Manuals", "agree": "Agree", "ticketClosed": "Your ticket has been closed by service provider", "selectLang": "Select Language", "temperatureAlertTitle": "Enter Low & High Temperature Alert Value", "humidityAlertTitle": "Enter Low & High Humidity Alert Value", "emergencyAlertTitle": "Emergency Alert", "emergencyAlertMessage": "Your child is still in their seat, please ensure they are with a responsible adult", "noAlerts": "No Alerts", "highAlertMsg": "The temperature is high around your child, please ensure they are safe and comfortable.", "highAlertMsg1": "The temperature is high around", "highAlertMsg2": "please ensure they are safe and comfortable.", "lowTempAlertMsg": "The temperature is low around, please ensure they are safe and comfortable.", "lowAlertMsg1": "The temperature is low around", "lowAlertMsg2": "please ensure they are safe and comfortable.", "tempAlertTitle": "Temperature alert", "humdtAlertTitle": "Humidity alert", "batteryAlertTitle": "Battery alert", "batteryLowMsg": "Battery is lower then 25%", "noPosts": "No Posts", "noDevice": "No Devices", "urlError": "Requested URL is not valid", "powerDownTitle": "Power Down", "powerDownMessage": "Powering down", "productDesc": "Product Description", "addDeviceTitle": "ADD DEVICE", "camera": "CAMERA", "scanQRText": "Using your phones camera. Scan your unique QR code which can be found on your Baby Auto Booklet.", "qrLinkText": "Click here if you are using non SMART product", "childLeftInSeatTitle": "{{child_name}} is in Seat", "childInSeatTitle": "<PERSON>", "childLeftInSeatMessage": "is still in their car seat. Please ensure they are with a responsible adult.", "changeSeatToForward": "Due to your child's weight you've entered, please set the child seat to forward facing position", "changeSeatToRearFace": "Due to your child's weight you've entered, please set the child seat to rear facing position", "fanOn": "Fan is on", "fanOff": "Fan is off", "fanTitle": "Fan", "tempTitle": "TEMPERATURE", "humidityTitle": "HUMIDITY", "carSeatText": "STATUS", "noProducts": "No Products", "noProductsConnected": "No products connected", "contactname": "CONTACT NAME", "contactnumber": "CONTACT NUMBER", "emergencycontact": "EMERGENCY CONTACT", "leftSeatTitle": "{{child_name}} left in seat", "leftSeatMessage": "{{child_name}} is out of seat and the system will power down in 30 seconds", "sendSMS": "Send Emergency SMS", "responsibleAdult": "With Responsible Adult", "productConnected": "SMART cushion connected", "noCameraAcces": "No access to camera. To scan code allow us to use your camera in Settings > Privacy > Camera", "openSettings": "Tap to open Settings", "deleteProfile": "Delete Profile", "editProfile": "Edit Profile", "emergencyFeatures": "and messages are enabled for the app.", "emergencySettings": "Settings > Location >  make sure location", "leftEmergency": "Emergency Left in seat", "MyQRcode": "My QR code", "deleteAccount": "Delete account", "delete": "Delete", "child": "Child", "inSeat": "in Seat", "allowPairing": "Allow pairing", "allowPairingText": "Press the button to allow another phone to connect to the cushion.", "introTitle1": "Welcome to Babyauto", "introTitle2": "Connecting your device", "introTitle3": "Your digital partner", "introText11": "Enjoy our smart products. They will", "introText12": "become the perfect complement for", "introText13": "your baby travels even more safely.", "introText21": "During set-up you will need to locate", "introText22": "your personal QR code to scan with", "introText23": "you phone which will link to your", "introText24": "new Babyauto smart device.", "introText31": "You can use your Babyauto app to", "introText32": "contact our customer service team and", "introText33": "even discover our latest news.", "yourchild": "your child", "highHumidity1": "The humidity is high around", "highHumidity2": "please ensure they are safe and comfortable.", "lowHumidity1": "The humidity is low around", "lowHumidity2": "please ensure they are safe and comfortable.", "seatBatteryAlert": "Battery Alert", "seatBatteryAlertMsg": "We recommend changing the battery as it will soon be low", "locationSettingsAlert": "SETTINGS", "enterName": "Please enter FullName", "enterPasswrd": "Please enter Password", "passwrdValid": "Password must contain 8-15 characters, 1 x Upper case, 1 x Lower case, 1 x number and 1 x special character such as !,?,&", "enterEmail": "Please enter email", "enterEmailvalid": "Please enter valid Email", "enterPhone": "Please enter Phone number", "enterPhonevalid": "Please enter valid Phone number", "accept": "Please accept Terms and Conditions", "enterOTP": "Enter One Time Password", "enterNickname": "Please enter Nickname", "selectDOB": "Please Select DOB", "selectHeight": "Please select Height", "selectWeight": "Please select Weight", "enterContactName": "Please enter contact name", "enterContactNumber": "Please enter contact number", "entervalidContactNumber": "Please enter valid contact number", "addImage": "Please Add Profile Picture", "turnOnBle": "Please turn on your bluetooth", "cannotFindDevice": "Can't find any device. Please try again", "connecting": "Connecting...", "childLeftSeatTitle": "{{child_name}} left seat", "childDeleted": "Child record deleted successfully", "sendSMSAlert": "{{child_name}}'s emergency SMS sent", "userManual": "My User manual", "awakeDeviceTxt": "Please ensure cushion is awake and in range", "CONNECTED": "CONNECTED", "dataNotFound": "Data Not Found", "connectBtn": "Connect", "disconnectSeat": "{{child_name}}'s device has disconnected", "deviceDisconnect": "Device disconnect", "locationSettingsTitle": "Allow location and emergency messages", "searchingQRCode": "Searching for QR code...", "readingQRCode": "Reading QR code...", "skip": "<PERSON><PERSON>", "disconnectedFromDevice": "Disconnected from device", "connectToYourBabyAuto": "Connect to device", "taxIncluded": "Taxes included. Shipping costs are calculated on the payment screen.", "maintainPressure": "Push on Linko® cushion to turn on and maintain pressure throughout the set up", "oneLowerCase": "At least one lower case letter", "oneUpperCase": "At least one upper case letter", "oneNumber": "At least one number", "oneSp": "At least one special character", "twLong": "At least twelve character", "pswdReq": "Password requirements:", "locationDis": "BabyAuto collects location data to enable sending emergency SMS, handling from smart safety cushion even when the app is closed or not in use.", "clickOn": "Click on ", "addnew": "add new ", "buttonConnectLinko": "button to connect your Linko®", "childProPopup": "Complete your child profile for the Linko® and add up to 2 emergency contacts in case of an emergency or child left in car.", "childProPopup2": "Once you have entered all the details press the green tick in the top right hand corner", "step2": "Unzip the cushion cover on your Linko® to and battery plastic tab", "step3a": "keep the QR code facing you and lean on the Linko® with your elbow applying pressure to active.", "step3b": "You will need to apply pressure to Linko® throughout the process until its complete", "step4": "Next you will need to scan the QR code on your cushion", "step4b1": "If QR code scan fails please press the ", "step4b2": " button at the bottom of the page", "step5": "Press next and Swipe down on the screen to show your Linko® then press connect", "step5b": "If your Linko® does not appear in the list try troubleshooting options to resolve any issues", "step5c": "Check Bluetooth is switched on by pressing the settings button below", "step5d": "Open up the battery compartment and reposition battery then carefully add battery cover", "step5e": "Apply pressure to Linko® ensuring a minimum on 2kg to activate the pressure sensor", "step6": "Press next and then the orange tick to confirm", "step7": "Press next and then the green tick in the top right hand corner to assign Linko® to your child profile", "step8a": "CONGRATULATIONS!", "step8b": "You have successfully connected your Linko®. You can release the pressure from Linko®", "step8c": "Click on the highlighted image above to go to the dashboard", "Start": "Start", "Next": "Next", "Settings": "Settings", "Back": "Back", "Troubleshoot": "Troubleshoot", "Finish": "Finish", "stepAllowBT": "to Bluetooth and location when the app requests. You will also need good mobile and data coverage", "allowAccess": "allow access", "please": "Please,", "troubleText": "If your device does not appear in the list above then please click troubleshoot below", "troubleshoot": "Troubleshoot", "belowIssues": "To Connect to your device please check the below issues:", "issue1": "Battery is installed in the device.", "issue2": "Your Bluetooth is turned on and access has been given to the app in your phone settings.", "issue3": "Location services is switched on.", "issue4": "That you push down or place your child on the device during the entire set up process.", "tryagain": "Try again", "contactus": "Contact us", "press_to_connect": "Press to connect"}