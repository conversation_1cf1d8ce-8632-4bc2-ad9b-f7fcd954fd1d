{"text": "ТЕКСТ", "welcomeToBaby": "Добро пожаловать в BabyAuto", "functionalComfortable": "Функциональный и удобный, но, прежде всего, безопасный", "login": "АВТОРИЗОВАТЬСЯ", "signup": "РЕГИСТР", "loginscreen": "Авторизоваться", "loginUser": "ВВЕДИТЕ МОБИЛЬНЫЙ / ИМЯ ПОЛЬЗОВАТЕЛЯ", "loginPassword": "ПАРОЛЬ", "loginRemember": "ЗАПОМНИ МЕНЯ", "loginBtn": "АВТОРИЗОВАТЬСЯ", "loginForgot": "ЗАБЫЛ ПАРОЛЬ", "loginAccount": "Нет учетной записи?", "loginToSignup": "ЗАРЕГИСТРИРОВАТЬСЯ", "forgotScreen": "ЗАБЫЛ ПАРОЛЬ?", "forgotEmail": "Введите адрес электронной почты, связанный с вашей учетной записью.", "forgotInput": "ТЕЛЕФОННЫЙ НОМЕР", "forgotBtn": "СБРОС НАСТРОЕК", "otpScreen": "МЫ ОТПРАВИЛИ КОД", "otpText": "Введите OTP, отправленный на связанный номер телефона", "otpEmail": "Введите OTP, отправленный на связанный адрес электронной почты", "otpBtn": "ПРЕДСТАВИТЬ", "otpResend": "ОТПРАВИТЬ", "genderScreen": "ВЫБЕРИТЕ ПОЛ", "genderFemale": "женский", "genderMale": "Мужчина", "qrScannerScreen": "УСТРОЙСТВА", "qrScannerText": "Используя камеру вашего телефона, отсканируйте свой\nуникальный QR-код, который можно найти на вашем\nДетские Авто.", "qrScannerLink": "Щелкните здесь, если вы пользуетесь продуктом, отличным от SMART.", "connectScreen": "Связанный!", "noChild": "Профиль ребенка не найден", "pleaseAdd": "Пожалуйста, добавьте профиль ребенка, чтобы продолжить", "connectSuccessText": "Вы успешно подключили\nУстройство", "childProfileScreen": "ДЕТСКИЙ ПРОФИЛЬ", "chooseProfileText": "ВЫБЕРИТЕ ПРОФИЛЬ", "addNew": "+ ДОБАВИТЬ НОВОЕ", "tapToAddText": "Нажмите, чтобы добавить новый профиль", "infoTitle": "ИНФОРМАЦИЯ", "nickName": "NICKNAME", "age": "ВОЗРАСТ", "fewWordInputText": "НЕСКОЛЬКО СЛОВ обо мне", "height": "ВЫСОТА (см)", "weight": "ВЕС (кг)", "devices": "УСТРОЙСТВА", "dashboard": "ПРИБОРНАЯ ПАНЕЛЬ", "home": "ДОМ", "deviceThreeText": "Подключено 1 устройство", "activeDevice": "Активный", "deactivedDevice": "Деактивировано", "tapToAddNewProfile": "Нажмите, чтобы добавить новый профиль", "homeAddSmartDevice": " Добавить устройство SMART", "homeAlertMsg": "Автокресло плохо закреплено или не подсоединено должным образом, пожалуйста, убедитесь в безопасности перед продолжением поездки.", "homeAlertTitle": "Предупреждение о автокресле", "alertOkBtn": "ОК", "alertCancelBtn": "ОТМЕНИТЬ", "homeVideoGuideText": "Лучший совет по установке автокресла", "settingScreen": "НАСТРОЙКИ", "tempText": "Установка температуры", "pushText": "Отправить уведомление", "darkmodeText": "Темный режим", "customerText": "Обслуживание клиентов", "tempC": "C", "tempF": "F", "modeOn": "На", "modeOff": "Выключенный", "about": "О", "support": "Часто задаваемые вопросы и поддержка", "privacypolicy": "Политика конфиденциальности", "version": "ВЕРСИЯ 1.0", "alertScreen": "ПРЕДУПРЕЖДЕНИЯ", "tempAlert": "Предупреждение о высокой температуре", "tempAlertText": "11 мин назад", "strollerAlert": "Коляска Чарли", "strollerText": "Сегодня, 23:22", "strollerMsg": "Крик оповещения", "babyInSeatAlert": "Предупреждение о ребенке в сиденье", "babyInSeatTime": "2 ч. Назад", "babyOutOfSeatAlert": "Ребенок вне места", "babyOutOfSeatTime": "2 ч. Назад", "listenIntoAlert": "Слушайте оповещение", "connectedDeviceName": "Майки", "charlie": "<PERSON>а<PERSON><PERSON>и", "smartcushion": "Подушка SMART", "chatScreenTitle": "Алекс - Бэби Авто", "chatInputText": "Введите ответ", "fullName": "ФИО", "updatePasswordScreen": "ОБНОВИТЬ ПАРОЛЬ", "newPassword": "Новый пароль", "confirmPassword": "Подтвердить Пароль", "logout": "Выйти", "emailId": "ЭЛЕКТРОННОЕ ПИСЬМО", "agreeTo": "СОГЛАСИТЬСЯ С", "termNCondition": "УСЛОВИЯ И УСЛОВИЯ", "whatsNew": "КАКИЕ НОВОСТИ", "dob": "Дата рождения", "faqScreen": "Часто задаваемые вопросы и поддержка", "selectChild": "Выбрать ребенка", "deleteDevice": "УДАЛИТЬ УСТРОЙСТВО", "changeDevice": "ИЗМЕНИТЬ УСТРОЙСТВО", "editDevice": "ИЗМЕНИТЬ УСТРОЙСТВО", "assignDevice": "НАЗНАЧИТЬ УСТРОЙСТВО", "children": "ДЕТИ", "products": "Продукты", "productsCatalouge": "Каталог продукции", "productDetail": "Информация о продукте", "technicalDrawing": "Видео о продукте", "faqNManuals": "FAQ и руководства", "agree": "Дать согласие", "ticketClosed": "Ваш билет был закрыт поставщиком услуг", "selectLang": "Выберите язык", "temperatureAlertTitle": "Введите значение предупреждения о низкой и высокой температуре", "humidityAlertTitle": "Введите значение предупреждения о низкой и высокой влажности", "emergencyAlertTitle": "Экстренное оповещение", "emergencyAlertMessage": "Ваш ребенок все еще сидит на своем месте, пожалуйста, убедитесь, что он находится с ответственным взрослым", "noAlerts": "Нет предупреждений", "highAlertMsg": "Вокруг вашего ребенка высокая температура, убедитесь, что ему комфортно и безопасно.", "highAlertMsg1": "Температура вокруг высокая", "highAlertMsg2": "Пожалуйста, убедитесь, что они безопасны и удобны.", "lowTempAlertMsg": "Вокруг вашего ребенка низкая температура, убедитесь, что ему комфортно и безопасно.", "lowAlertMsg1": "Температура вокруг низкая", "lowAlertMsg2": "Пожалуйста, убедитесь, что они безопасны и удобны.", "tempAlertTitle": "Предупреждение о температуре", "humdtAlertTitle": "Оповещение о влажности", "batteryAlertTitle": "Предупреждение о разряде батареи", "batteryLowMsg": "Батарея ниже 25%", "noPosts": "Нет сообщений", "noDevice": "Нет устройств", "urlError": "Запрошенный URL недействителен", "powerDownTitle": "Выключить", "powerDownMessage": "Выключение", "productDesc": "описание продукта", "addDeviceTitle": "ДОБАВИТЬ УСТРОЙСТВО", "camera": "КАМЕРА", "scanQRText": "Используя камеру вашего телефона. Отсканируйте свой уникальный QR-код, который можно найти на вашем Baby Auto.", "qrLinkText": "Щелкните здесь, если вы пользуетесь продуктом, отличным от SMART.", "childLeftInSeatTitle": "Детское сиденье", "childInSeatTitle": "Детское сиденье", "childLeftInSeatMessage": "Ваш ребенок все еще сидит в автокресле. Пожалуйста, убедитесь, что они находятся с ответственным взрослым.", "changeSeatToForward": "Из-за веса вашего ребенка, который вы указали, установите детское кресло в положение лицом вперед.", "changeSeatToRearFace": "Из-за веса вашего ребенка, который вы указали, установите детское кресло в положение \"спинкой вперед\".", "fanOn": "Вентилятор включен", "fanOff": "Вентилятор выключен", "fanTitle": "Поклонник", "tempTitle": "ТЕМПЕРАТУРА", "humidityTitle": "ВЛАЖНОСТЬ", "carSeatText": "СТАТУС", "noProducts": "Нет продуктов", "noProductsConnected": "Нет подключенных продуктов", "contactname": "КОНТАКТНОЕ ЛИЦО", "contactnumber": "КОНТАКТНЫЙ ТЕЛЕФОН", "emergencycontact": "ЭКСТРЕННЫЙ КОНТАКТ", "leftSeatTitle": "Ребенок покинул сиденье", "leftSeatMessage": "Ребенок не сидит на сиденье, и система отключится через 30 секунд.", "sendSMS": "Отправить экстренное SMS", "responsibleAdult": "С ответственным взрослым", "productConnected": "Подушка SMART подключена, ребенок в сиденье.", "noCameraAcces": "Нет доступа к камере. Чтобы отсканировать код, разрешите нам использовать вашу камеру в «Настройки»> «Конфиденциальность»> «Камера».", "openSettings": "Нажмите, чтобы открыть настройки", "deleteProfile": "Удалить профиль", "editProfile": "Редактировать профиль", "emergencyFeatures": "и сообщения включены для приложения.", "emergencySettings": "Настройки> Местоположение> Убедиться, что местоположение", "leftEmergency": "Аварийный левый на сиденье", "MyQRcode": "Мой QR код", "deleteAccount": "Удалить аккаунт", "delete": "Удалить", "child": "Ребенок", "inSeat": "в сиденье", "allowPairing": "Разрешить сопряжение", "allowPairingText": "Нажмите кнопку, чтобы другой телефон мог подключиться к подушке.", "introTitle1": "Добро пожаловать в детскую машину", "introTitle2": "Подключение вашего устройства", "introTitle3": "Ваш цифровой партнер", "introText11": "Наслаждайтесь нашими умными продуктами. Они будут", "introText12": "стать идеальным дополнением для", "introText13": "ваш ребенок путешествует еще безопаснее.", "introText21": "Во время настройки вам нужно найти", "introText22": "ваш личный QR-код для сканирования", "introText23": "ваш телефон, который будет связан с вашим", "introText24": "новое смарт-устройство Babyauto.", "introText31": "Вы можете использовать приложение Babyauto для", "introText32": "свяжитесь с нашей службой поддержки клиентов и", "introText33": "даже узнать наши последние новости.", "yourchild": "Твой ребенок", "highHumidity1": "Вокруг высокая влажность", "highHumidity2": "Пожалуйста, убедитесь, что они безопасны и удобны.", "lowHumidity1": "Влажность вокруг низкая", "lowHumidity2": "Пожалуйста, убедитесь, что они безопасны и удобны.", "seatBatteryAlert": "Предупреждение о батарее", "seatBatteryAlertMsg": "Мы рекомендуем заменить батарею, так как она скоро разрядится", "locationSettingsAlert": "НАСТРОЙКИ", "enterName": "Пожалуйста, введите полное имя", "enterPasswrd": "Пожалуйста введите пароль", "passwrdValid": "Пароль должен содержать 8-15 символов, 1 верхний регистр, 1 нижний регистр, 1 цифру и 1 специальный символ, например !,?,&", "enterEmail": "Пожалуйста, введите адрес электронной почты", "enterEmailvalid": "Пожалуйста, введите действительный адрес электронной почты", "enterPhone": "Пожалуйста, введите номер телефона", "enterPhonevalid": "Пожалуйста введите правильный номер телефона", "accept": "Пожалуйста, примите Условия использования", "enterOTP": "Введите одноразовый пароль", "enterNickname": "Пожалуйста, введите псевдоним", "selectDOB": "Пожалуйста, выберите Дата рождения", "selectHeight": "Пожалуйста, выберите Высота", "selectWeight": "Пожалуйста, выберите Вес", "enterContactName": "Пожалуйста, введите контактное имя", "enterContactNumber": "Пожалуйста, введите контактный номер", "entervalidContactNumber": "Пожалуйста, введите действительный контактный номер", "addImage": "Пожалуйста, добавьте изображение профиля", "turnOnBle": "Пожалуйста, включите Bluetooth", "cannotFindDevice": "Не могу найти ни одно устройство. Пожалуйста, попробуйте еще раз", "connecting": "Подключ...", "childLeftSeatTitle": "{{child_name}} покинул место", "childDeleted": "Дочерняя запись успешно удалена", "sendSMSAlert": "Экстренное SMS от {{child_name}} отправлено", "userManual": "Мое руководство пользователя", "awakeDeviceTxt": "Пожалуйста, убедитесь, что подушка не спит и находится в пределах досягаемости", "CONNECTED": "СВЯЗАННЫЙ", "dataNotFound": "Данные не найдены", "connectBtn": "Соединять", "disconnectSeat": "{{child_name}} устройство отключено", "deviceDisconnect": "Отключение устройства", "locationSettingsTitle": "Разрешить местоположение и экстренные сообщения", "searchingQRCode": "Поиск QR-кода...", "readingQRCode": "Чтение QR-кода...", "skip": "Пропускать", "disconnectedFromDevice": "Отключено от устройства", "connectToYourBabyAuto": "Подключиться к устройству", "taxIncluded": "Налоги включены. Стоимость доставки рассчитывается на экране оплаты.", "maintainPressure": "Нажмите на подушку Linko®, чтобы включить и поддерживать давление на протяжении всей установки.", "oneLowerCase": "Хотя бы одна строчная буква", "oneUpperCase": "Хотя бы одна заглавная буква", "oneNumber": "Хотя бы один номер", "oneSp": "Хотя бы один специальный символ", "twLong": "Не менее двенадцати символов", "pswdReq": "Требования к паролю:", "locationDis": "BabyAuto собирает данные о местоположении, чтобы можно было отправлять экстренные SMS-сообщения, управлять ими с помощью умной подушки безопасности, даже когда приложение закрыто или не используется.", "clickOn": "Нажмите на", "addnew": "Добавить новое", "buttonConnectLinko": "кнопку для подключения Linko®", "childProPopup": "Заполните свой профиль ребенка для Linko® и добавьте до 2 экстренных контактов на случай чрезвычайной ситуации или ребенка, оставшегося в машине.", "childProPopup2": "После того, как вы ввели все данные, нажмите зеленую галочку в правом верхнем углу.", "step2": "Расстегните молнию на чехле Linko® и пластиковом язычке аккумулятора.", "step3a": "Держите QR-код лицом к себе и обопритесь на Linko®, надавив локтем на активный.", "step3b": "Вам нужно будет оказывать давление на Linko® на протяжении всего процесса, пока он не завершится.", "step4": "Далее вам нужно будет отсканировать QR-код на подушке.", "step4b1": "Если сканирование QR-кода не удалось, нажмите кнопку", "step4b2": " кнопка внизу страницы", "step5": "Нажмите «Далее» и проведите пальцем вниз по экрану, чтобы отобразить Linko®, затем нажмите «Подключиться».", "step5b": "Если ваш Linko® не отображается в списке, попробуйте варианты устранения неполадок, чтобы решить любые проблемы.", "step5c": "Убедит<PERSON><PERSON><PERSON>, что <PERSON>tooth включен, нажав кнопку настроек ниже", "step5d": "Откройте батарейный отсек и переместите батарею, затем осторожно установите крышку батарейного отсека.", "step5e": "Приложите давление к Linko®, обеспечив минимум 2 кг, чтобы активировать датчик давления.", "step6": "Нажмите «Далее», а затем оранжевую галочку для подтверждения.", "step7": "Нажмите «Далее», а затем зеленую галочку в правом верхнем углу, чтобы назначить Linko® вашему дочернему профилю.", "step8a": "ПОЗДРАВЛЯЕМ!", "step8b": "Вы успешно подключили Linko®. Вы можете снять давление с Linko®", "step8c": "Нажмите на выделенное изображение выше, чтобы перейти на панель инструментов.", "Start": "Начинать", "Next": "Следующий", "Settings": "Настройки", "Back": "Назад", "Troubleshoot": "Устранение неполадок", "Finish": "Заканчивать", "stepAllowBT": "к Bluetooth и местоположению, когда приложение запрашивает. Вам также понадобится хорошее покрытие мобильной связи и данных.", "allowAccess": "разрешить доступ", "please": "Пожалуйста,", "troubleText": "Если ваше устройство не отображается в списке выше, нажмите «Устранение неполадок» ниже.", "troubleshoot": "Устранение неполадок", "belowIssues": "Чтобы подключиться к вашему устройству, проверьте следующие проблемы:", "issue1": "Аккумулятор установлен в устройстве.", "issue2": "Ва<PERSON> <PERSON>tooth включен, и доступ к приложению в настройках вашего телефона открыт.", "issue3": "Службы определения местоположения включены.", "issue4": "То, что вы нажимаете или кладете ребенка на устройство в течение всего процесса настройки.", "tryagain": "Попробуйте еще раз", "contactus": "Связаться с нами"}