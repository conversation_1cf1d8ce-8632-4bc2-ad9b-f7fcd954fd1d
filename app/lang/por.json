{"text": "TEXTO", "welcomeToBaby": "Bem-vindo a BabyAuto", "functionalComfortable": "Funcional e confortável, mas acima de tudo seguro", "login": "CONECTE-SE", "signup": "REGISTRO", "loginscreen": "Conecte-se", "loginUser": "DIGITE O NOME DO MOBILE / USUÁRIO", "loginPassword": "SENHA", "loginRemember": "LEMBRE DE MIM", "loginBtn": "CONECTE-SE", "loginForgot": "ESQUECEU A SENHA", "loginAccount": "Não tem uma conta?", "loginToSignup": "INSCREVER-SE", "forgotScreen": "ESQUECEU A SENHA?", "forgotEmail": "Digite o e-mail associado à sua conta.", "forgotInput": "NÚMERO DE TELEFONE", "forgotBtn": "REDEFINIR", "otpScreen": "ENVIAMOS UM CÓDIGO", "otpText": "Digite o OTP enviado para o número de telefone associado", "otpEmail": "Digite o OTP enviado para o e-mail associado", "otpBtn": "ENVIAR", "otpResend": "REENVIAR", "genderScreen": "SELECIONE O GÊNERO", "genderFemale": "Fêmea", "genderMale": "<PERSON><PERSON>", "qrScannerScreen": "DISPOSITIVOS", "noChild": "Perfil de criança não encontrado", "pleaseAdd": "Por favor, adicione um perfil de criança para continuar", "qrScannerText": "Usando a câmera do seu telefone, verifique o seu\ncódigo QR exclusivo que pode ser encontrado em seu\nBaby Auto.", "qrScannerLink": "Clique aqui se você estiver usando um produto não SMART", "connectScreen": "Conectado!", "connectSuccessText": "Você conectou com sucesso o\nDispositivo", "childProfileScreen": "PERFIL DA CRIANÇA", "chooseProfileText": "ESCOLHER O PERFIL", "addNew": "+ ADICIONE NOVO", "tapToAddText": "Toque para adicionar novo perfil", "infoTitle": "INFO", "nickName": "APELIDO", "age": "ERA", "fewWordInputText": "ALGUMAS PALAVRAS SOBRE MIM", "height": "ALTURA (cm)", "weight": "PESO (kg)", "devices": "DISPOSITIVOS", "dashboard": "PAINEL DE CONTROLE", "home": "CASA", "deviceThreeText": "dispositivo conectado", "activeDevice": "Ativo", "deactivedDevice": "Desativado", "tapToAddNewProfile": "Toque para adicionar novo perfil", "homeAddSmartDevice": " Adicionar dispositivo SMART", "homeAlertMsg": "O assento do carro está solto ou não conectado corretamente, por favor, garanta a segurança antes de continuar a viagem", "homeAlertTitle": "Alerta de assento de carro", "alertOkBtn": "OK", "alertCancelBtn": "CANCELAR", "homeVideoGuideText": "Dica principal para instalar um assento de carro", "settingScreen": "DEFINIÇÕES", "tempText": "Configuração de temperatura", "pushText": "Notificação Push", "darkmodeText": "<PERSON><PERSON> es<PERSON>ro", "customerText": "Atendimento ao Cliente", "tempC": "C", "tempF": "F", "modeOn": "Sobre", "modeOff": "Fora", "about": "Cerca de", "support": "Perguntas frequentes e suporte", "privacypolicy": "Política de Privacidade", "version": "VERSÃO 1.0", "alertScreen": "ALERTAS", "tempAlert": "Alerta de alta temperatura", "tempAlertText": "11 minutos atrás", "strollerAlert": "Carrinho de criança de charlie", "strollerText": "Hoje, 23h22", "strollerMsg": "Alerta de choro", "babyInSeatAlert": "<PERSON><PERSON>a de bebê no assento", "babyInSeatTime": "2 horas atrás", "babyOutOfSeatAlert": "<PERSON><PERSON><PERSON> fora do assento", "babyOutOfSeatTime": "2 horas atrás", "listenIntoAlert": "Ouça o alerta", "connectedDeviceName": "<PERSON><PERSON>", "charlie": "<PERSON>", "smartcushion": "Almofada SMART", "chatScreenTitle": "<PERSON> - Baby Auto", "chatInputText": "Digite uma resposta", "fullName": "NOME COMPLETO", "updatePasswordScreen": "ATUALIZAR SENHA", "newPassword": "Nova Senha", "confirmPassword": "Confirm<PERSON> a <PERSON>", "logout": "<PERSON><PERSON>", "emailId": "O EMAIL", "agreeTo": "CONCORDAR COM", "termNCondition": "TERMOS E CONDIÇÕES", "whatsNew": "O QUE HÁ DE NOVO", "dob": "DOB", "faqScreen": "Perguntas frequentes e suporte", "selectChild": "Selecione Criança", "deleteDevice": "APAGAR DISPOSITIVO", "changeDevice": "TROCAR DISPOSITIVO", "editDevice": "EDITAR DISPOSITIVO", "assignDevice": "ATRIBUIR DISPOSITIVO", "children": "CRIANÇAS", "products": "<PERSON><PERSON><PERSON>", "productDetail": "Detalhes do produto", "technicalDrawing": "Vídeo do produto", "faqNManuals": "FAQ e manuais", "agree": "Concordar", "ticketClosed": "Seu tíquete foi fechado pelo provedor de serviços", "selectLang": "Selecione o idioma", "temperatureAlertTitle": "Insira o valor de alerta de temperatura baixa e alta", "humidityAlertTitle": "Insira o valor de alerta de baixa e alta umidade", "emergencyAlertTitle": "Alerta de Emergência", "emergencyAlertMessage": "Seu filho ainda está no assento, certifique-se de que ele esteja com um adulto responsável", "noAlerts": "Sem alertas", "highAlertMsg": "A temperatura ao redor do seu filho está alta, certifique-se de que ele é seguro e confortável.", "highAlertMsg1": "A temperatura é alta em torno", "highAlertMsg2": "por favor, certifique-se de que eles são seguros e confortáveis.", "lowTempAlertMsg": "A temperatura ao redor do seu filho está baixa, certifique-se de que ele é seguro e confortável.", "lowAlertMsg1": "A temperatura é baixa em torno", "lowAlertMsg2": "certifique-se de que eles são seguros e confortáveis.", "tempAlertTitle": "Alerta de temperatura", "humdtAlertTitle": "Alerta de umidade", "batteryAlertTitle": "Alerta de bateria", "batteryLowMsg": "A bateria está abaixo de 25%", "noPosts": "Sem postagens", "noDevice": "Sem dispositivos", "urlError": "URL solicitado não é válido", "powerDownTitle": "<PERSON><PERSON><PERSON>", "powerDownMessage": "Desligando", "productDesc": "Descrição do produto", "addDeviceTitle": "ADICIONAR DISPOSITIVO", "camera": "CÂMERA", "scanQRText": "Usando a câmera do seu telefone. Digitalize o seu código QR exclusivo, que pode ser encontrado no seu Baby Auto.", "qrLinkText": "Clique aqui se você estiver usando um produto não SMART", "childLeftInSeatTitle": "Cadeira de criança", "childInSeatTitle": "Assento infantil", "childLeftInSeatMessage": "Seu filho ainda está no assento do carro. Certifique-se de que estão com um adulto responsável.", "changeSeatToForward": "Devido ao peso do seu filho que você inseriu, coloque a cadeira para crianças na posição voltada para a frente", "changeSeatToRearFace": "Devido ao peso do seu filho que você inseriu, defina a cadeira para criança na posição voltada para trás", "fanOn": "Ventilador ligado", "fanOff": "Ventilador desligado", "fanTitle": "Ventilador", "tempTitle": "TEMPERATURA", "humidityTitle": "UMIDADE", "carSeatText": "ASSENTO DO CARRO", "noProducts": "Sem produtos", "noProductsConnected": "Nenhum produto conectado", "contactname": "NOME DE CONTATO", "contactnumber": "NÚMERO DE CONTATO", "emergencycontact": "CONTATO DE EMERGÊNCIA", "leftSeatTitle": "Criança saiu do assento", "leftSeatMessage": "A criança está fora da cadeira e o sistema será desligado em 30 segundos", "sendSMS": "Enviar SMS de Emergência", "responsibleAdult": "Com Adulto Responsável", "productConnected": "Almofada SMART conectada, Criança no assento.", "noCameraAcces": "Sem acesso à câmera. Para ler o código, permita-nos usar sua câmera em Configurações> Privacidade> Câmera", "openSettings": "Toque para abrir as configuraçõesn", "deleteProfile": "Apagar o Perfil", "editProfile": "<PERSON><PERSON>", "emergencyFeatures": "Para os recursos de esquerda no assento de emergência, vá para", "emergencySettings": "Configurações> Localização> Ao usar o aplicativo", "leftEmergency": "Emergência esquerda no assento", "MyQRcode": "Meu código QR", "deleteAccount": "Deletar conta", "delete": "Excluir", "child": "<PERSON><PERSON><PERSON>", "inSeat": "no assento", "allowPairing": "<PERSON><PERSON><PERSON>", "allowPairingText": "Pressione o botão para permitir que outro telefone se conecte à almofada.", "introTitle1": "Bem-vindo ao carro do bebê", "introTitle2": "Conectando seu dispositivo", "introTitle3": "Seu parceiro digital", "introText11": "Aproveite nossos produtos inteligentes. Elas vão", "introText12": "se tornar o complemento perfeito para", "introText13": "seu bebê viaja com ainda mais segurança.", "introText21": "Durante a configuração, você precisará localizar", "introText22": "seu código QR pessoal para digitalizar com", "introText23": "o seu telefone que irá ligar ao seu", "introText24": "novo dispositivo inteligente Babyauto.", "introText31": "Você pode usar seu aplicativo Babyauto para", "introText32": "entre em contato com nossa equipe de atendimento ao cliente e", "introText33": "até mesmo descobrir nossas últimas notícias.", "yourchild": "seu filho", "highHumidity1": "A umidade é alta ao redor", "highHumidity2": "por favor, certifique-se de que eles são seguros e confortáveis.", "lowHumidity1": "A umidade é baixa ao redor", "lowHumidity2": "por favor, certifique-se de que eles são seguros e confortáveis.", "seatBatteryAlert": "Alerta de bateria", "seatBatteryAlertMsg": "Recomendamos trocar a bateria, pois em breve estará fraca", "locationSettingsAlert": "Definições", "enterName": "Please enter FullName", "enterPasswrd": "Please enter Password", "passwrdValid": "Password must contain 8-15 characters, 1 x Upper case, 1 x Lower case, 1 x number and 1 x special character such as !,?,&", "enterEmail": "Please enter email", "enterEmailvalid": "Please enter valid Email", "enterPhone": "Please enter Phone number", "enterPhonevalid": "Please enter valid Phone number", "accept": "Please accept Terms and Conditions", "enterOTP": "Enter One Time Password", "enterNickname": "Please enter Nickname", "selectDOB": "Please Select DOB", "selectHeight": "Please select Height", "selectWeight": "Please select Weight", "enterContactName": "Please enter contact name", "enterContactNumber": "Please enter contact number", "entervalidContactNumber": "Please enter valid contact number", "addImage": "Please Add Profile Picture", "turnOnBle": "Please turn on your bluetooth", "cannotFindDevice": "Can't find any device. Please try again", "connecting": "Connecting...", "childLeftSeatTitle": "{{child_name}} left seat", "childDeleted": "Child record deleted successfully", "sendSMSAlert": "{{child_name}}'s emergency SMS sent", "userManual": "My User manual", "awakeDeviceTxt": "Please ensure cushion is awake and in range", "CONNECTED": "CONNECTED", "dataNotFound": "Data Not Found", "connectBtn": "Connect", "disconnectSeat": "{{child_name}}'s device has disconnected", "deviceDisconnect": "Device disconnect", "locationSettingsTitle": "Allow location and emergency messages", "searchingQRCode": "Searching for QR code...", "readingQRCode": "Reading QR code...", "skip": "<PERSON><PERSON>", "disconnectedFromDevice": "Disconnected from device", "connectToYourBabyAuto": "Connect to device", "taxIncluded": "Taxes included. Shipping costs are calculated on the payment screen.", "maintainPressure": "Push on Linko® cushion to turn on and maintain pressure throughout the set up", "oneLowerCase": "At least one lower case letter", "oneUpperCase": "At least one upper case letter", "oneNumber": "At least one number", "oneSp": "At least one special character", "twLong": "At least twelve character", "pswdReq": "Password requirements:", "locationDis": "BabyAuto collects location data to enable sending emergency SMS, handling from smart safety cushion even when the app is closed or not in use.", "clickOn": "Click on ", "addnew": "add new ", "buttonConnectLinko": "button to connect your Linko®", "childProPopup": "Complete your child profile for the Linko® and add up to 2 emergency contacts in case of an emergency or child left in car.", "childProPopup2": "Once you have entered all the details press the green tick in the top right hand corner", "step2": "Unzip the cushion cover on your Linko® to and battery plastic tab", "step3a": "keep the QR code facing you and lean on the Linko® with your elbow applying pressure to active.", "step3b": "You will need to apply pressure to Linko® throughout the process until its complete", "step4": "Next you will need to scan the QR code on your cushion", "step4b1": "If QR code scan fails please press the ", "step4b2": " button at the bottom of the page", "step5": "Press next and Swipe down on the screen to show your Linko® then press connect", "step5b": "If your Linko® does not appear in the list try troubleshooting options to resolve any issues", "step5c": "Check Bluetooth is switched on by pressing the settings button below", "step5d": "Open up the battery compartment and reposition battery then carefully add battery cover", "step5e": "Apply pressure to Linko® ensuring a minimum on 2kg to activate the pressure sensor", "step6": "Press next and then the orange tick to confirm", "step7": "Press next and then the green tick in the top right hand corner to assign Linko® to your child profile", "step8a": "CONGRATULATIONS!", "step8b": "You have successfully connected your Linko®. You can release the pressure from Linko®", "step8c": "Click on the highlighted image above to go to the dashboard", "Start": "Start", "Next": "Next", "Settings": "Settings", "Back": "Back", "Troubleshoot": "Troubleshoot", "Finish": "Finish", "stepAllowBT": "to Bluetooth and location when the app requests. You will also need good mobile and data coverage", "allowAccess": "allow access", "please": "Please,", "troubleText": "If your device does not appear in the list above then please click troubleshoot below", "troubleshoot": "Troubleshoot", "belowIssues": "To Connect to your device please check the below issues:", "issue1": "Battery is installed in the device.", "issue2": "Your Bluetooth is turned on and access has been given to the app in your phone settings.", "issue3": "Location services is switched on.", "issue4": "That you push down or place your child on the device during the entire set up process.", "tryagain": "Try again", "contactus": "Contact us"}