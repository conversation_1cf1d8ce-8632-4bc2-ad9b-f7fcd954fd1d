/**
 * CarPlay Component for handling notifications and alerts
 * Integrates with react-native-carplay for CarPlay functionality
 */

import React, { useEffect, useState, useCallback, useRef } from 'react';
import { NativeEventEmitter, NativeModules, View } from 'react-native';
import {
  CarPlay,
  GridTemplate,
  AlertTemplate,
  ActionSheetTemplate,
  InformationTemplate,
  ListTemplate,
} from 'react-native-carplay';
import { useSelector } from 'react-redux';
import { sendErrorReport } from '../utils/commonFunction';

// Safe destructuring to avoid undefined errors
const MaxRCTCarPlayNotificationManager =
  NativeModules.MaxRCTCarPlayNotificationManager || null;

if (!MaxRCTCarPlayNotificationManager) {
  console.log(
    'Available modules:',
    Object.keys(NativeModules).filter(key => key.includes('CarPlay')),
  );
}

const CarPlayComponent = props => {
  const { navigation } = props;
  const user = useSelector(state => state.auth.userData);
  const { alertData, activeChildDetail } = useSelector(
    state => state.bluetooth,
  );
  const pendingNotification = useRef(null);
  const [carPlayConnected, setCarPlayConnected] = useState(CarPlay.connected);
  const [carPlayForeground, setCarPlayForeground] = useState(false);
  const carPlayForegroundRef = useRef(carPlayForeground);

  useEffect(() => {
    carPlayForegroundRef.current = carPlayForeground;
    console.log(`🔄 CarPlay foreground state changed to: ${carPlayForeground}`);
    sendErrorReport(carPlayForeground, 'carplay_foreground_state_change');
  }, [carPlayForeground]);

  // Add this useEffect right after the component initialization
  useEffect(() => {
    // Check and sync CarPlay connection status on component mount
    const checkCarPlayConnection = () => {
      if (CarPlay.connected !== carPlayConnected) {
        setCarPlayConnected(CarPlay.connected);
        sendErrorReport(CarPlay.connected, 'carplay_connected');

        // If CarPlay is connected and we have a pending notification, show it
        if (CarPlay.connected && pendingNotification.current) {
          handleNotification(pendingNotification.current);
          pendingNotification.current = null;
        }
      }
    };

    // Check immediately
    checkCarPlayConnection();

    // Also trigger the native check for connection
    if (CarPlay.bridge && CarPlay.bridge.checkForConnection) {
      CarPlay.bridge.checkForConnection();
    }
  }, []); // Empty dependency array - only run on mount

  // Add this useEffect to monitor state changes
  // Simplified connection status sync - only on mount
  useEffect(() => {
    const syncConnectionStatus = () => {
      if (CarPlay.connected !== carPlayConnected) {
        console.log(`Syncing CarPlay connection state: ${CarPlay.connected}`);
        setCarPlayConnected(CarPlay.connected);
        sendErrorReport(CarPlay.connected, 'carplay_connected_sync');

        // If CarPlay is connected on mount, set foreground state
        if (CarPlay.connected) {
          setTimeout(() => {
            console.log(
              '🔄 Setting foreground state for already connected CarPlay',
            );
            setCarPlayForeground(true);
          }, 300);
        }
      }
    };

    // Check immediately on mount
    syncConnectionStatus();

    // Trigger native connection check
    if (CarPlay.bridge && CarPlay.bridge.checkForConnection) {
      CarPlay.bridge.checkForConnection();
    }
  }, []); // Only run on mount

  // Connection handlers
  const onConnect = useCallback(() => {
    sendErrorReport('true', 'carplay_connected_onconnect');
    setCarPlayConnected(true);

    // CarPlay starts in foreground when connected, set initial state
    setTimeout(() => {
      console.log('🔄 Setting initial CarPlay foreground state on connection');
      setCarPlayForeground(true);

      if (pendingNotification.current) {
        console.log('Processing queued notification after connection');
        handleNotification(pendingNotification.current);
        pendingNotification.current = null;
      }
    }, 600); // Wait for native initialization to complete
  }, []); // Remove handleNotification dependency to avoid stale closure

  const onDisconnect = useCallback(() => {
    sendErrorReport('false', 'carplay_connected_onDisconnect');
    setCarPlayConnected(false);
    setCarPlayForeground(false); // Reset foreground state on disconnect

    // Clear any pending notifications
    if (pendingNotification.current) {
      console.log('Clearing pending notification due to CarPlay disconnect');
      pendingNotification.current = null;
    }
  }, []); // Remove handleNotification dependency to avoid stale closure

  useEffect(() => {
    if (alertData && alertData.message) {
      sendErrorReport(alertData, 'alertData_carplay');
      handleNotification({
        title: alertData?.title,
        message: alertData?.message,
        type: 'alert',
        actions: alertData?.actions || [],
      });
    }
  }, [alertData, handleNotification]);

  // Handle notifications
  const handleNotification = useCallback(
    notification => {
      sendErrorReport(notification, 'carplay_notification');
      console.log('🚀 ~ CarPlayComponent ~ notification:', notification);
      sendErrorReport(
        CarPlay.connected,
        'carplay_connected_handleNotification',
      );

      try {
        const { title, message, type = 'alert', actions = [] } = notification;
        // Use CarPlay.connected as the primary source of truth
        const isCarPlayReady = CarPlay.connected;
        console.log('🚀 ~ CarPlayComponent ~ isCarPlayReady:', isCarPlayReady);

        // if (!isCarPlayReady) {
        //   pendingNotification.current = notification;
        //   return;
        // }

        sendErrorReport(carPlayForeground, 'carplay_foreground');
        if (!carPlayForegroundRef.current) {
          if (MaxRCTCarPlayNotificationManager) {
            MaxRCTCarPlayNotificationManager.sendCarPlayNotification(
              title || 'Notification',
              message,
            )
              .then(res => {
                sendErrorReport(res, 'carplay_banner_send');
                console.log('✅ CarPlay notification result:', res);
              })
              .catch(err => {
                sendErrorReport(err, 'carplay_banner_send_error');
                console.error('❌ Error sending CarPlay notification:', err);
              });
          } else {
            console.warn('❌ CarPlay notification manager not available');
          }
          return;
        }

        if (type === 'alert' && carPlayForegroundRef.current) {
          const alertTemplate = new AlertTemplate({
            titleVariants: [message],
            actions:
              actions.length > 0
                ? actions
                : [
                    {
                      id: 'ok',
                      title: 'OK',
                    },
                  ],
            onActionButtonPressed: ({ id }) => {
              CarPlay.dismissTemplate();
              if (notification.onActionPressed) {
                notification.onActionPressed(id);
              }
            },
          });

          sendErrorReport(notification, 'carplay_notification_alert');

          // Try to present the template
          try {
            CarPlay.presentTemplate(alertTemplate, true);
            console.log('✅ AlertTemplate presented successfully');
          } catch (error) {
            console.error('❌ Failed to present AlertTemplate:', error);
          }
        } else if (type === 'actionSheet') {
          console.log('📋 Creating ActionSheetTemplate');

          const actionSheetTemplate = new ActionSheetTemplate({
            title: title,
            message: message,
            actions:
              actions.length > 0
                ? actions
                : [
                    {
                      id: 'ok',
                      title: 'OK',
                    },
                    {
                      id: 'cancel',
                      title: 'Cancel',
                      style: 'cancel',
                    },
                  ],
            onActionButtonPressed: ({ id }) => {
              CarPlay.dismissTemplate();
              if (notification.onActionPressed) {
                notification.onActionPressed(id);
              }
            },
          });

          try {
            CarPlay.presentTemplate(actionSheetTemplate, true);
            console.log('✅ ActionSheetTemplate presented successfully');
          } catch (error) {
            console.error('❌ Failed to present ActionSheetTemplate:', error);
          }
        }
      } catch (error) {
        console.error('❌ Error handling CarPlay notification:', error);
        console.error('Error stack:', error.stack);
      }
    },
    [carPlayConnected],
  );

  // This is for Know State of CarPlay
  useEffect(() => {
    const carPlayEmitter = new NativeEventEmitter(
      NativeModules.CarPlayEventEmitter,
    );

    const handleForeground = debounce(() => {
      console.log('✅ CarPlay foreground event received');
      sendErrorReport('true', 'CarPlay_foreground_detected');
      setCarPlayForeground(true);

      if (pendingNotification.current && CarPlay.connected) {
        console.log('Processing queued notification on foreground');
        handleNotification(pendingNotification.current);
        pendingNotification.current = null;
      }
    }, 200); // Reduced debounce for better responsiveness

    const handleBackground = debounce(() => {
      console.log('✅ CarPlay background event received');
      sendErrorReport('true', 'CarPlay_background_detected');
      setCarPlayForeground(false);
    }, 200); // Reduced debounce for better responsiveness

    const fg = carPlayEmitter.addListener(
      'carPlayForeground',
      handleForeground,
    );
    const bg = carPlayEmitter.addListener(
      'carPlayBackground',
      handleBackground,
    );

    return () => {
      fg.remove();
      bg.remove();
      // Cancel any pending debounced calls
      handleForeground.cancel();
      handleBackground.cancel();
    };
  }, []);

  // Set up CarPlay event listeners
  useEffect(() => {
    const emt = new NativeEventEmitter(NativeModules.RNCarPlay);
    const connectListener = emt.addListener('didConnect', onConnect);

    // Set up notification listener
    const notificationEmitter = new NativeEventEmitter(
      NativeModules.MaxRCTCarPlayNotificationManager,
    );

    const notificationListener = notificationEmitter.addListener(
      'carPlayNotification',
      notification => {
        handleNotification(notification);
      },
    );

    CarPlay.registerOnConnect(onConnect);
    CarPlay.registerOnDisconnect(onDisconnect);

    return () => {
      connectListener.remove();
      notificationListener?.remove();
      CarPlay.unregisterOnConnect(onConnect);
      CarPlay.unregisterOnDisconnect(onDisconnect);
    };
  }, [onConnect, onDisconnect]);

  // Set up main grid template
  useEffect(() => {
    const infoTemplate = new InformationTemplate({
      title: `ChillBaby${user?.full_name ? ` - ${user.full_name}` : ''}`,
      subtitle: 'Your personalized dashboard in CarPlay',
      actions: [], // you can add actions if needed
      items: [
        {
          title: 'Welcome',
          detail: user?.full_name || 'Guest User',
        },
        {
          title: 'Status',
          detail: carPlayConnected
            ? '✅ Connected to CarPlay'
            : '❌ Not Connected',
        },
      ],
    });

    CarPlay.setRootTemplate(infoTemplate);
  }, [carPlayConnected, user, handleNotification]);

  return <View></View>;
};

export default CarPlayComponent;
