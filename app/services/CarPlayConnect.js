/**
 * CarPlay Component for handling notifications and alerts
 * Integrates with react-native-carplay for CarPlay functionality
 */
import React, { useEffect, useState, useCallback, useRef } from 'react';
import {
  NativeEventEmitter,
  NativeModules,
  View,
  AppState,
} from 'react-native';
import { CarPlay, InformationTemplate } from 'react-native-carplay';
import { useSelector } from 'react-redux';
import { sendErrorReport } from '../utils/commonFunction';
import CarPlayComponent from './CarPlayComponent';
import CarPlayBackground from './CarPlayBackground';

const CarPlayConnect = props => {
  const { carPlayInForeground } = props;
  console.log(
    '🟢 🟢 🟢 🟢  ~ CarPlayConnect ~ carPlayInForeground:',
    carPlayInForeground,
  );
  const user = useSelector(state => state.auth.userData);
  const pendingNotification = useRef(null);

  // State management for CarPlay connection and app states
  const [carPlayConnected, setCarPlayConnected] = useState(CarPlay.connected);

  // Add this useEffect right after the component initialization
  useEffect(() => {
    // Check and sync CarPlay connection status on component mount
    const checkCarPlayConnection = () => {
      if (CarPlay.connected !== carPlayConnected) {
        setCarPlayConnected(CarPlay.connected);
        sendErrorReport(CarPlay.connected, 'carplay_connected');

        // If CarPlay is connected and we have a pending notification, show it
        if (CarPlay.connected && pendingNotification.current) {
          //   handleNotification(pendingNotification.current);
          pendingNotification.current = null;
        }
      }
    };

    // Check immediately
    checkCarPlayConnection();

    // Also trigger the native check for connection
    if (CarPlay.bridge && CarPlay.bridge.checkForConnection) {
      CarPlay.bridge.checkForConnection();
    }
  }, []); // Empty dependency array - only run on mount

  // Add this useEffect to monitor state changes
  useEffect(() => {
    // If there's a mismatch, log it
    if (CarPlay.connected !== carPlayConnected) {
      console.warn('⚠️ State mismatch detected!');
      console.log('  - Native says:', CarPlay.connected);
      console.log('  - State says:', carPlayConnected);
    }
  }, [carPlayConnected]);

  // Connection handlers
  const onConnect = useCallback(() => {
    sendErrorReport('true', 'carplay_connected_onconnect');
    setCarPlayConnected(true);
    // Don't assume foreground state on connection - let the native events handle it
    console.log(
      '🔌 CarPlay connected - waiting for foreground/background event',
    );

    if (pendingNotification.current) {
      // Use setTimeout to ensure state has updated
    }
  }, []); // Remove handleNotification dependency to avoid stale closure

  const onDisconnect = useCallback(() => {
    sendErrorReport('false', 'carplay_connected_onDisconnect');
    setCarPlayConnected(false);
    // Reset CarPlay foreground state when disconnected
    console.log('🔌 CarPlay disconnected - resetting foreground state');
  }, []); // Remove handleNotification dependency to avoid stale closure

  // Set up CarPlay event listeners
  useEffect(() => {
    const emt = new NativeEventEmitter(NativeModules.RNCarPlay);
    const connectListener = emt.addListener('didConnect', onConnect);

    // Set up notification listener
    const notificationEmitter = new NativeEventEmitter(
      NativeModules.MaxRCTCarPlayNotificationManager,
    );

    const notificationListener = notificationEmitter.addListener(
      'carPlayNotification',
      notification => {
        console.log('🚀 ~ CarPlayConnect ~ notification:', notification);
      },
    );

    CarPlay.registerOnConnect(onConnect);
    CarPlay.registerOnDisconnect(onDisconnect);

    return () => {
      connectListener.remove();
      notificationListener?.remove();
      CarPlay.unregisterOnConnect(onConnect);
      CarPlay.unregisterOnDisconnect(onDisconnect);
    };
  }, [onConnect, onDisconnect]);

  // Conditional rendering logic based on CarPlay connection and CarPlay foreground/background state
  const renderCarPlayComponent = useCallback(() => {
    console.log(
      '🟢 ✅ 🟢 ✅ 🟢 ✅ 🟢  ~ CarPlayConnect ~ carPlayInForeground:',
      carPlayInForeground,
    );
    // SECOND: If CarPlay is connected, check foreground/background state
    if (carPlayInForeground) {
      console.log(
        '✅ CarPlay foreground + connected - rendering CarPlayComponent',
      );
      return <CarPlayComponent carPlayInForeground={carPlayInForeground} />;
    }
    if (!carPlayInForeground) {
      console.log(
        '🎒 CarPlay background + connected - rendering CarPlayBackground',
      );
      return <CarPlayBackground carPlayInForeground={carPlayInForeground} />;
    }
    return null;
  }, [carPlayConnected, carPlayInForeground]);

  // Set up CarPlay information template when connected
  useEffect(() => {
    if (!carPlayConnected) return;

    const infoTemplate = new InformationTemplate({
      title: `ChillBaby${user?.full_name ? ` - ${user.full_name}` : ''}`,
      subtitle: 'Your personalized dashboard in CarPlay',
      actions: [], // you can add actions if needed
      items: [
        {
          title: 'Welcome',
          detail: user?.full_name || 'Guest User',
        },
        {
          title: 'Status',
          detail: carPlayConnected
            ? '✅ Connected to CarPlay'
            : '❌ Not Connected',
        },
      ],
    });

    CarPlay.setRootTemplate(infoTemplate);
  }, [carPlayConnected, user, carPlayInForeground]);

  // Log state changes for debugging
  useEffect(() => {
    console.log(
      `🔄 State Update - CarPlay: ${
        carPlayConnected ? 'Connected' : 'Disconnected'
      },  CarPlay Mode: ${carPlayInForeground ? 'Foreground' : 'Background'}`,
    );
  }, [carPlayConnected, carPlayInForeground]);

  return <View>{renderCarPlayComponent()}</View>;
};

export default CarPlayConnect;
