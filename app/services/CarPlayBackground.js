/**
 * CarPlay Component for handling notifications and alerts
 * Integrates with react-native-carplay for CarPlay functionality
 */

import React, { useEffect, useState, useCallback, useRef } from 'react';
import { NativeModules, View } from 'react-native';
import { useSelector } from 'react-redux';
import { sendErrorReport } from '../utils/commonFunction';

// Safe destructuring to avoid undefined errors
const MaxRCTCarPlayNotificationManager =
  NativeModules.MaxRCTCarPlayNotificationManager || null;

if (!MaxRCTCarPlayNotificationManager) {
  console.log(
    'Available modules:',
    Object.keys(NativeModules).filter(key => key.includes('CarPlay')),
  );
}

const CarPlayBackground = props => {
  const { carPlayInForeground } = props;
  console.log(
    '👽 👽 👽 👽 back ground ~ CarPlayBackground ~ carPlayInForeground:',
    carPlayInForeground,
  );
  const { alertData, carplayState } = useSelector(state => state.bluetooth);

  useEffect(() => {
    if (alertData && alertData.message) {
      sendErrorReport(alertData, 'alertData_carplay');
      if (carPlayInForeground) {
        return;
      }
      handleNotification({
        title: alertData?.title,
        message: alertData?.message,
        type: 'alert',
        actions: alertData?.actions || [],
      });
    }
  }, [alertData, carPlayInForeground]);

  // Handle notifications
  const handleNotification = useCallback(notification => {
    sendErrorReport(notification, 'carplay_notification');
    try {
      const { title, message, type = 'alert', actions = [] } = notification;
      // Use CarPlay.connected as the primary source of truth
      sendErrorReport(carplayState, 'carplay_foreground');
      if (MaxRCTCarPlayNotificationManager && !carPlayInForeground) {
        MaxRCTCarPlayNotificationManager.sendCarPlayNotification(
          title || 'Notification',
          message,
        )
          .then(res => {
            sendErrorReport(res, 'carplay_banner_send');
            console.log('✅ CarPlay notification result:', res);
          })
          .catch(err => {
            sendErrorReport(err, 'carplay_banner_send_error');
            console.error('❌ Error sending CarPlay notification:', err);
          });
      } else {
        console.warn('❌ CarPlay notification manager not available');
      }
    } catch (error) {
      console.error('❌ Error handling CarPlay notification:', error);
      console.error('Error stack:', error.stack);
    }
  }, []);
};

export default CarPlayBackground;
