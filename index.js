import {AppRegistry, LogBox, Text, View} from 'react-native';
import {name as appName} from './app.json';
import messaging from '@react-native-firebase/messaging';
import {store} from './app/redux/store/configureStore';
import NotificationAction from './app/redux/reducers/notification/actions';
import {enableScreens} from 'react-native-screens';

enableScreens(true);

LogBox.ignoreAllLogs(true);

const App = require('./app/Entrypoint').default;
// const App = () => (
//   <View>
//     <Text>Hi</Text>
//   </View>
// );

// Register background handler
messaging().setBackgroundMessageHandler(async remoteMessage => {
  console.log('Message handled in the background!', remoteMessage);
  const type = remoteMessage?.data?.type || '';
  store.dispatch(NotificationAction.setNotificationType(type));
});

AppRegistry.registerComponent(appName, () => App);
