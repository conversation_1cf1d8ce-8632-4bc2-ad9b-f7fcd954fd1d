#import "CarPlayEventEmitter.h"

@implementation CarPlayEventEmitter

RCT_EXPORT_MODULE();

+ (instancetype)sharedInstance {
  static CarPlayEventEmitter *shared = nil;
  static dispatch_once_t onceToken;
  dispatch_once(&onceToken, ^{
    shared = [[self alloc] init];
  });
  return shared;
}

- (NSArray<NSString *> *)supportedEvents {
  return @[@"carPlayForeground", @"carPlayBackground"];
}

- (void)sendCarPlayForegroundEvent {
  [self sendEventWithName:@"carPlayForeground" body:@{}];
}

- (void)sendCarPlayBackgroundEvent {
  [self sendEventWithName:@"carPlayBackground" body:@{}];
}

@end
