#import "CarPlaySceneDelegate.h"
#import <React/RCTBridge.h>
#import "RNCarPlay.h"
#import "CarPlayEventEmitter.h"
#import "AppDelegate.h"
#import "MaxRCTCarPlayNotificationManager.h"

@implementation CarPlaySceneDelegate

- (void)templateApplicationScene:(CPTemplateApplicationScene *)templateApplicationScene
      didConnectInterfaceController:(CPInterfaceController *)interfaceController {
    NSLog(@"CarPlay connected - interface controller received");

    self.interfaceController = interfaceController;
    self.isCarPlayConnected = YES;

    [RNCarPlay connectWithInterfaceController:interfaceController
                                       window:templateApplicationScene.carWindow];

    // ✅ Also set interface controller on MaxRCTCarPlayNotificationManager
    AppDelegate *appDelegate = (AppDelegate *)UIApplication.sharedApplication.delegate;
    RCTBridge *bridge = appDelegate.bridge;
    if (bridge != nil) {
        MaxRCTCarPlayNotificationManager *manager =
            [bridge moduleForClass:[MaxRCTCarPlayNotificationManager class]];
        if (manager != nil) {
            [manager setInterfaceController:interfaceController];
            NSLog(@"✅ Interface controller set on MaxRCTCarPlayNotificationManager");
        } else {
            NSLog(@"⚠️ MaxRCTCarPlayNotificationManager not found");
        }
    }
}

- (void)templateApplicationScene:(CPTemplateApplicationScene *)templateApplicationScene
   didDisconnectInterfaceController:(CPInterfaceController *)interfaceController {
    NSLog(@"CarPlay disconnected");

    self.interfaceController = nil;
    self.isCarPlayConnected = NO;

    if (self.isCarPlayActive) {
        [self setCarPlayForeground:NO];
    }

    [RNCarPlay disconnect];

    // ✅ Also clear interface controller from MaxRCTCarPlayNotificationManager
    AppDelegate *appDelegate = (AppDelegate *)UIApplication.sharedApplication.delegate;
    RCTBridge *bridge = appDelegate.bridge;
    if (bridge != nil) {
        MaxRCTCarPlayNotificationManager *manager =
            [bridge moduleForClass:[MaxRCTCarPlayNotificationManager class]];
        if (manager != nil) {
            [manager clearInterfaceController];
            NSLog(@"✅ Interface controller cleared from MaxRCTCarPlayNotificationManager");
        }
    }
}

// MARK: Foreground / Background
- (void)sceneDidBecomeActive:(UIScene *)scene {
    NSLog(@"CarPlay scene became active at %@", [NSDate date]);
    [self setCarPlayForeground:YES];
}

- (void)sceneWillResignActive:(UIScene *)scene {
    NSLog(@"CarPlay scene will resign active at %@", [NSDate date]);
    [self setCarPlayForeground:NO];
}

- (void)setCarPlayForeground:(BOOL)isActive {
    // Only process if CarPlay is connected
    if (!self.isCarPlayConnected) {
        NSLog(@"⚠️ Ignoring CarPlay state change: CarPlay not connected");
        return;
    }

    // Debounce rapid toggles
    NSTimeInterval threshold = 1.5; // Increased to 1.5 seconds
    if (self.lastStateChange &&
        [[NSDate date] timeIntervalSinceDate:self.lastStateChange] < threshold) {
        NSLog(@"⚠️ Ignoring duplicate CarPlay state event");
        return;
    }

    if (self.isCarPlayActive == isActive) {
        NSLog(@"⚠️ No CarPlay state change needed: already %d", isActive);
        return;
    }

    self.lastStateChange = [NSDate date];
    self.isCarPlayActive = isActive;

    NSLog(@"📢 Setting CarPlay state to: %@", isActive ? @"Foreground" : @"Background");
    [self sendCarPlayEvent:isActive ? @"carPlayForeground" : @"carPlayBackground"];
}

- (void)sendCarPlayEvent:(NSString *)name {
    AppDelegate *appDelegate = (AppDelegate *)UIApplication.sharedApplication.delegate;
    RCTBridge *bridge = appDelegate.bridge;
    if (bridge != nil) {
        CarPlayEventEmitter *emitter = [bridge moduleForClass:[CarPlayEventEmitter class]];
        if (emitter != nil) {
            NSLog(@"📢 Sending CarPlay event: %@", name);
            [emitter sendEventWithName:name body:@{}];
        } else {
            NSLog(@"⚠️ CarPlayEventEmitter not found");
        }
    } else {
        NSLog(@"⚠️ RN bridge not ready yet");
    }
}

@end
