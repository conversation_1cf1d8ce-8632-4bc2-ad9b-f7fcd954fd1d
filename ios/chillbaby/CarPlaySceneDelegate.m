#import "CarPlaySceneDelegate.h"
#import <React/RCTBridge.h>
#import "RNCarPlay.h"
#import "CarPlayEventEmitter.h"
#import "AppDelegate.h"
#import "MaxRCTCarPlayNotificationManager.h"

@implementation CarPlaySceneDelegate

- (void)templateApplicationScene:(CPTemplateApplicationScene *)templateApplicationScene
           didConnectInterfaceController:(CPInterfaceController *)interfaceController {
    NSLog(@"CarPlay connected - interface controller received");

    self.interfaceController = interfaceController;
    self.isCarPlayConnected = YES;

    // Initialize state properly on connection
    self.isCarPlayActive = YES; // CarPlay starts in foreground when connected
    self.lastStateChange = [NSDate date];

    [RNCarPlay connectWithInterfaceController:interfaceController
                                       window:templateApplicationScene.carWindow];

    // ✅ Also set interface controller on MaxRCTCarPlayNotificationManager
    AppDelegate *appDelegate = (AppDelegate *)UIApplication.sharedApplication.delegate;
    RCTBridge *bridge = appDelegate.bridge;
    if (bridge != nil) {
        MaxRCTCarPlayNotificationManager *manager =
            [bridge moduleForClass:[MaxRCTCarPlayNotificationManager class]];
        if (manager != nil) {
            [manager setInterfaceController:interfaceController];
            NSLog(@"✅ Interface controller set on MaxRCTCarPlayNotificationManager");
        } else {
            NSLog(@"⚠️ MaxRCTCarPlayNotificationManager not found");
        }
    }

    // Send initial foreground event after connection is established
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        NSLog(@"📢 Sending initial CarPlay foreground event after connection");
        [self sendCarPlayEvent:@"carPlayForeground"];
    });
}

- (void)templateApplicationScene:(CPTemplateApplicationScene *)templateApplicationScene
        didDisconnectInterfaceController:(CPInterfaceController *)interfaceController {
    NSLog(@"CarPlay disconnected");

    self.interfaceController = nil;
    self.isCarPlayConnected = NO;
    self.isCarPlayActive = NO; // Reset active state on disconnect
    self.lastStateChange = [NSDate date];

    [RNCarPlay disconnect];

    // ✅ Also clear interface controller from MaxRCTCarPlayNotificationManager
    AppDelegate *appDelegate = (AppDelegate *)UIApplication.sharedApplication.delegate;
    RCTBridge *bridge = appDelegate.bridge;
    if (bridge != nil) {
        MaxRCTCarPlayNotificationManager *manager =
            [bridge moduleForClass:[MaxRCTCarPlayNotificationManager class]];
        if (manager != nil) {
            [manager clearInterfaceController];
            NSLog(@"✅ Interface controller cleared from MaxRCTCarPlayNotificationManager");
        }
    }
}

// MARK: Foreground / Background
- (void)sceneDidBecomeActive:(UIScene *)scene {
    NSLog(@"CarPlay scene became active at %@", [NSDate date]);
    // Add small delay to ensure proper state transition
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self setCarPlayForeground:YES];
    });
}

- (void)sceneWillResignActive:(UIScene *)scene {
    NSLog(@"CarPlay scene will resign active at %@", [NSDate date]);
    // Add small delay to ensure proper state transition
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self setCarPlayForeground:NO];
    });
}

- (void)setCarPlayForeground:(BOOL)isActive {
    // Only process if CarPlay is connected
    if (!self.isCarPlayConnected) {
        NSLog(@"⚠️ Ignoring CarPlay state change: CarPlay not connected");
        return;
    }

    // Debounce rapid toggles - reduced threshold for better responsiveness
    NSTimeInterval threshold = 0.3; // Reduced to 0.3 seconds for better responsiveness
    if (self.lastStateChange &&
        [[NSDate date] timeIntervalSinceDate:self.lastStateChange] < threshold) {
        NSLog(@"⚠️ Ignoring duplicate CarPlay state event (within %.1f seconds)", threshold);
        return;
    }

    if (self.isCarPlayActive == isActive) {
        NSLog(@"⚠️ No CarPlay state change needed: already %@", isActive ? @"foreground" : @"background");
        return;
    }

    self.lastStateChange = [NSDate date];
    self.isCarPlayActive = isActive;

    NSLog(@"📢 Setting CarPlay state to: %@", isActive ? @"Foreground" : @"Background");
    [self sendCarPlayEvent:isActive ? @"carPlayForeground" : @"carPlayBackground"];
}

- (void)sendCarPlayEvent:(NSString *)name {
    AppDelegate *appDelegate = (AppDelegate *)UIApplication.sharedApplication.delegate;
    RCTBridge *bridge = appDelegate.bridge;
    if (bridge != nil) {
        CarPlayEventEmitter *emitter = [bridge moduleForClass:[CarPlayEventEmitter class]];
        if (emitter != nil) {
            NSLog(@"📢 Sending CarPlay event: %@", name);
            [emitter sendEventWithName:name body:@{}];
        } else {
            NSLog(@"⚠️ CarPlayEventEmitter not found");
        }
    } else {
        NSLog(@"⚠️ RN bridge not ready yet");
    }
}



@end
