#import <UIKit/UIKit.h>
#import <CarPlay/CarPlay.h>

@interface CarPlaySceneDelegate : UIResponder <CPTemplateApplicationSceneDelegate>

@property (strong, nonatomic) CPInterfaceController *interfaceController;
@property (nonatomic, assign) BOOL isCarPlayConnected;
@property (nonatomic, assign) BOOL isCarPlayActive;        // ✅ NEW: Track foreground/background state
@property (nonatomic, strong) NSDate *lastStateChange;     // ✅ NEW: For debounce timing

@end
