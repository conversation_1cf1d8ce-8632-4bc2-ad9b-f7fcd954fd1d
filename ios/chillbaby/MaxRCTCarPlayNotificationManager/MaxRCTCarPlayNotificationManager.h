#import <React/RCTBridgeModule.h>
#import <React/RCTEventEmitter.h>
#import <CarPlay/CarPlay.h>

@interface MaxRCTCarPlayNotificationManager : RCTEventEmitter <RCTBridgeModule>

@property (nonatomic, weak) CPInterfaceController *interfaceController;

- (void)sendCarPlayNotification:(NSString *)title message:(NSString *)message;
- (void)setInterfaceController:(CPInterfaceController *)interfaceController;
- (void)clearInterfaceController;

@end
