
/*
Based on <PERSON> example for IDF: https://github.com/nkolban/esp32-snippets/blob/master/cpp_utils/tests/BLE%20Tests/SampleServer.cpp
Ported to <PERSON><PERSON><PERSON>o ESP32 by <PERSON><PERSON><PERSON>
updates by <PERSON><PERSON><PERSON>
*/



#include <BLEDevice.h>
#include <BLEUtils.h>
#include <BLEServer.h>


// See the following for generating UUIDs:
// https://www.uuidgenerator.net/

#define SERVICE_UUID "4fafc201-1fb5-459e-8fcc-c5c9c331914b"
#define CHARACTERISTIC_UUID "beb5483e-36e1-4688-b7f5-ea07361b26a8"
BLECharacteristic *pCharacteristic;
int i = 0;
//a#include <HX711_ADC.h>
#include <EEPROM.h>
//pins:
const int HX711_dout = 5; //mcu > HX711 dout pin
const int HX711_sck = 2; //mcu > HX711 sck pin

//HX711 constructor:
//HX711_ADC LoadCell(HX711_dout, HX711_sck);
////////////////////////////////////////////////////temperature sensor
//#include "DHT.h"
//#define DHTPIN 4 // Digital pin connected to the DHT sensor
//#define DHTTYPE DHT11 // DHT 11
//#define DHTTYPE DHT22 // DHT 22 (AM2302), AM2321
//#define DHTTYPE DHT21 // DHT 21 (AM2301)
//DHT dht(DHTPIN, DHTTYPE);




const int calVal_eepromAdress = 0;
long t;

long scaleTick, httpTick;

float utt=0;
int lef_sensor=39;
int bat_s=36;
int green=25;
int red=26;
int blue=27;
unsigned int left_sensor_v=0;

int sen1=0;
int sen2=0;
int sen3=0;
int sen4=0;
int sen5=0;
int sen6=0;
int sen7=0;
int sen8=0;
int sen9=0;
float sen10=0;
float sen11=0;
float battery_voltage=0;
int fan=18;
int heater=19;




void setup() {
Serial.begin(115200);
Serial.println("Starting BLE work!");
pinMode(lef_sensor,INPUT);
pinMode(34,INPUT);
pinMode(35,INPUT);
pinMode(32,INPUT);

pinMode(fan,OUTPUT);
pinMode(heater,OUTPUT);

pinMode(red,OUTPUT);
pinMode(green,OUTPUT);
pinMode(blue,OUTPUT);
delay(500);
//LoadCell.begin();
//float calibrationValue; // calibration value (see example file "Calibration.ino")
//calibrationValue = 696.0; // uncomment this if you want to set the calibration value in the sketch
#if defined(ESP8266)|| defined(ESP32)
//EEPROM.begin(512); // uncomment this if you use ESP8266/ESP32 and want to fetch the calibration value from eeprom
#endif
//EEPROM.get(calVal_eepromAdress, calibrationValue); // uncomment this if you want to fetch the calibration value from eeprom

long stabilizingtime = 2000; // preciscion right after power-up can be improved by adding a few seconds of stabilizing time
boolean _tare = true; //set this to false if you don't want tare to be performed in the next step

delay(500);
BLEDevice::init("Reebaby");
BLEServer *pServer = BLEDevice::createServer();
BLEService *pService = pServer->createService(SERVICE_UUID);

/* To notify the connected clients that new update is available - Was missing by Firmware devs */
pCharacteristic = pService->createCharacteristic(
CHARACTERISTIC_UUID,
BLECharacteristic::PROPERTY_READ |
BLECharacteristic::PROPERTY_WRITE |
BLECharacteristic::PROPERTY_NOTIFY          // Added by Krunal to fix
);

pCharacteristic->setValue("{\"s1\":sensor_1,\"s2\":sensor_2,\"s3\":sensor_3,\"s4\":sensor_4,\"s5\":sensor_5,\"s6\":sensor_6,\"s7\":sensor_7,\"s8\":sensor_8,\"s9\":sensor_9,\"TEMP\":int(sensor_10),\"HUMIDITY\":int(sensor_11),\"bv\":battery_voltage}");

pService->start();
// BLEAdvertising *pAdvertising = pServer->getAdvertising(); // this still is working for backward compatibility
BLEAdvertising *pAdvertising = BLEDevice::getAdvertising();
pAdvertising->addServiceUUID(SERVICE_UUID);
pAdvertising->setScanResponse(true);
pAdvertising->setMinPreferred(0x06); // functions that help with iPhone connections issue
pAdvertising->setMinPreferred(0x12);
BLEDevice::startAdvertising();
Serial.println("Characteristic defined! Now you can read it in your phone!");
Serial.println("Starting...");
delay(30);


}

long sc=0;
long us=0;


float temp=0;
float hum=0;
String str = "";
String str2 = "";
float weight=0.001;

void loop() {
send_to_server(random(0,1),random(0,1),random(0,1),random(0,1),random(0,1),random(0,1),random(0,1),random(0,1),random(0,1),random(10,26),random(10,66),random(2,4));
delay(3000);
}


void send_to_server(unsigned int sensor_1,unsigned int sensor_2,unsigned int sensor_3,unsigned int sensor_4,unsigned int sensor_5,unsigned int sensor_6,unsigned int sensor_7,unsigned int sensor_8,unsigned int sensor_9,float sensor_10,float sensor_11,float bv)
{

//str2="{\"s1\":sensor_1,\"s2\":sensor_2,\"s3\":sensor_3,\"s4\":sensor_4,\"s5\":sensor_5,\"s6\":sensor_6,\"s7\":sensor_7,\"s8\":sensor_8,\"s9\":sensor_9,\"TEMP\":int(sensor_10),\"HUMIDITY\":int(sensor_11),\"bv\":battery_voltage}";
str2="{\"s1\":1,\"s2\":1,\"s3\":1,\"s4\":1,\"s5\":1,\"s6\":1,\"s7\":1,\"s8\":1,\"s9\":1,\"TEMP\":";
str2=str2+sensor_10;
str2=str2+",\"HUMIDITY\":";
str2=str2+sensor_11;
str2=str2+",\"bv\":3.7}";
//int(sensor_10),\"HUMIDITY\":int(sensor_11),\"bv\":battery_voltage}"

//str2="{\"s1\":"+sensor_1+",\"s2\":"+sensor_2+",\"s3\":"+sensor_3+",\"s4\":"+sensor_4+",\"s5\":"+sensor_5+",\"s6\":"+sensor_6+",\"s7\":"+sensor_7+",\"s8\":"+sensor_8+",\"s9\":"+sensor_9+",\"TEMP\":"+int(sensor_10)+",\"HUMIDITY\":"+int(sensor_11)+",\"bv\":"+battery_voltage}";

Serial.print("Data Jason String-->");Serial.println(str2);
/*
{
"s1": sensor_1,
"s2": sensor_22,
"s3": sensor_3,
"s4": sensor_4,
"s5": sensor_5,
"s6": sensor_6,
"s7": sensor_7,
"s8": sensor_8,
"s9": sensor_9,
"TEMP": int(sensor_10),
"HUMIDITY": int(sensor_11),
"bv": battery_voltage
}
*/
pCharacteristic->setValue(str2.c_str());
/* To notify the connected clients that new update is available - Was missing by Firmware devs */
pCharacteristic->notify();

}