#include <BLEDevice.h>
#include <BLEUtils.h>
#include <BLEServer.h>
#define SERVICE_UUID        "4fafc201-1fb5-459e-8fcc-c5c9c331914b"
#define CHARACTERISTIC_UUID "beb5483e-36e1-4688-b7f5-ea07361b26a8"
BLECharacteristic *pCharacteristic;
int i = 0;
#include <EEPROM.h>

////#define DHTTYPE DHT11   // DHT 11
//#define DHTTYPE DHT22   // DHT 22  (AM2302), AM2321
//#define DHTTYPE DHT21   // DHT 21 (AM2301)
//DHT dht(DHTPIN, DHTTYPE);
int lef_sensor = 39;
int bat_s = 36;
int green = 25;
int red = 26;
int blue = 27;
const int calVal_eepromAdress = 0;
long t;
long scaleTick, httpTick;
unsigned int left_sensor_v = 0;


int sen1 = 0;
int sen2 = 0;
int sen3 = 0;
int sen4 = 0;
int sen5 = 0;
int sen6 = 0;
int sen7 = 0;
int sen8 = 0;
int sen9 = 0;
float sen10 = 0;
float sen11 = 0;
float battery_voltage = 0;
void setup() {
  Serial.begin(9600);
  Serial.println("Baby_auto_code!#1");



#if defined(ESP8266)|| defined(ESP32)
  //EEPROM.begin(512); // uncomment this if you use ESP8266/ESP32 and want to fetch the calibration value from eeprom
#endif
  Serial2.begin(9600);
  delay(2000);
  pinMode(lef_sensor, INPUT);
  pinMode(red, OUTPUT);
  pinMode(green, OUTPUT);
  pinMode(blue, OUTPUT);

  //  dht.begin();
  BLEDevice::init("Baby_Auto1ASDF"); //this one // Ha kia loader gum raha h
  BLEServer *pServer = BLEDevice::createServer();
  BLEService *pService = pServer->createService(SERVICE_UUID);
  pCharacteristic = pService->createCharacteristic(
                      CHARACTERISTIC_UUID,
//                     BLECharacteristic::PROPERTY_READ |
//                      BLECharacteristic::PROPERTY_WRITE | 
                      BLECharacteristic::PROPERTY_NOTIFY 
                    );
  pCharacteristic->setValue("{\"s1\":sensor_1,\"s2\":sensor_2,\"s3\":sensor_3,\"s4\":sensor_4,\"s5\":sensor_5,\"s6\":sensor_6,\"s7\":sensor_7,\"s8\":sensor_8,\"s9\":sensor_9,\"TEMP\":int(sensor_10),\"HUMIDITY\":int(sensor_11),\"bv\":battery_voltage}");

  pService->start();
  // BLEAdvertising *pAdvertising = pServer->getAdvertising();  // this still is working for backward compatibility
  BLEAdvertising *pAdvertising = BLEDevice::getAdvertising();
  pAdvertising->addServiceUUID(SERVICE_UUID);
  pAdvertising->setScanResponse(true);
  pAdvertising->setMinPreferred(0x06);  // functions that help with iPhone connections issue
  pAdvertising->setMinPreferred(0x12);
  BLEDevice::startAdvertising();
  Serial.println("Characteristic defined! Now you can read it in your phone!");

  ////

  Serial.println("Starting...");
  delay(500);

}

String str = "";
String str2 = "";
long sc = 0;
long us = 0;


float temp = 0;
float hum = 0;

void loop() {

  hum = random(20, 50); //dht.readHumidity();
  temp = random(10, 26); //dht.readTemperature();
  Serial.print(F("\t\tTemperature: ")); Serial.print(temp, 1);
  Serial.print(F("\t\tHumidity: ")); Serial.println(hum, 1);

  battery_voltage = (float)random(10, 12) / 3;
  if (battery_voltage > 3)
  {
    led_control(0, 1, 0);
  } else
  {
    led_control(1, 0, 0);
  }
  scan_sensor_switches();

//  if (millis() - us > 1000)
//  {
//    Serial.println("Sending to Ble Server");
//    send_to_server(sen1, sen2, sen3, sen4, sen5, sen6, sen7, sen8, sen9, temp, hum, battery_voltage);
     send_to_server(random(0,2),random(0,2),random(0,2),random(0,2),random(30,120),random(30,120),random(0,30),random(0,2),random(0,2),temp,hum,battery_voltage);
//    us = millis();
//  }
   delay(5000);

}

void send_to_server(unsigned int sensor_1, unsigned int sensor_2, unsigned int sensor_3, unsigned int sensor_4, unsigned int sensor_5, unsigned int sensor_6, unsigned int sensor_7, unsigned int sensor_8, unsigned int sensor_9, float sensor_10, float sensor_11, float bv)
{

//  str2 = "{\"SW\":sensor_1,\"s2\":sensor_22,\"s3\":sensor_3,\"s4\":sensor_4,\"s5\":sensor_5,\"s6\":sensor_6,\"s7\":sensor_7,\"s8\":sensor_8,\"s9\":sensor_9,\"Temperature\":int(sensor_10),\"Humidity\":int(sensor_11),\"BV\":battery_voltage}";
  str2="{\"SW\":";str2=str2+int(sensor_1)+",\"s2\":";

str2=str2+int(sensor_2)+",\"s3\":";

str2=str2+int(sensor_3)+",\"s4\":";
str2=str2+int(sensor_4)+",\"s5\":";
str2=str2+int(sensor_5)+",\"s6\":";
str2=str2+int(sensor_6)+",\"s7\":";
str2=str2+int(sensor_7)+",\"s8\":";
str2=str2+int(sensor_8)+",\"s9\":";


str2=str2+int(sensor_9)+",\"Humidity\":"+int(sensor_11)+",\"Temp\":"+int(sensor_10)+",\"BV\":"+battery_voltage+"}";
  Serial.print("Data Jason String-->"); Serial.println(str2);Serial.println(str2.c_str());
  pCharacteristic->setValue(str2.c_str());
  pCharacteristic->notify();

}

void led_control(boolean r, boolean g, boolean b)
{
  digitalWrite(red, r);
  digitalWrite(green, g);

}
float check_battery()
{
  unsigned int batt = 0;
  float battery_v = 0;
  int bt_ss = 0;
  for (bt_ss = 0; bt_ss < 10; bt_ss++)
  {
    batt = batt + analogRead(bat_s);
    delay(10);
  }
  batt = batt / 10;
  battery_v = (float)batt / 1241;
  battery_v = battery_v * 2;
  return (battery_v);
}
void scan_sensor_switches()
{
  sen1 = digitalRead(left_sensor_v);
  digitalWrite(blue, sen1);

}
