storeFile=./../keystores/chillbaby_key.keystore
keyAlias=chillbaby_alias
keyPassword=123456
storePassword=123456

# For SHA-1
# keytool -list -v -alias chillbaby_alias -keystore your_key_store_path


# Alias name: chillbaby_alias
# Creation date: Mar 17, 2021
# Entry type: PrivateKeyEntry
# Certificate chain length: 1
# Certificate[1]:
# Owner: CN=<PERSON><PERSON><PERSON>, OU=Groovy Web LLP, O=Groovy Web LLP, L=Nadiad, ST=Gujarat, C=91
# Issuer: CN=<PERSON><PERSON><PERSON>, OU=Groovy Web LLP, O=Groovy Web LLP, L=Nadiad, ST=Gujarat, C=91
# Serial number: 2fcf1cb2
# Valid from: Wed Mar 17 11:21:17 IST 2021 until: Sun Aug 02 11:21:17 IST 2048
# Certificate fingerprints:
#          SHA1: 26:68:D9:DE:58:FC:8C:22:E8:1E:0A:06:84:AF:64:FC:96:1E:B7:B6
#          SHA256: 3E:0A:17:A6:B0:07:E7:0D:80:E5:62:5C:61:DB:B8:79:53:C1:D6:1E:52:68:6D:E0:E6:46:11:32:06:FA:E6:22
# Signature algorithm name: SHA256withRSA
# Subject Public Key Algorithm: 2048-bit RSA key
# Version: 3

# Extensions: 

# #1: ObjectId: 2.5.29.14 Criticality=false
# SubjectKeyIdentifier [
# KeyIdentifier [
# 0000: 0E 3F 0A 64 8F 63 BB 62   25 08 37 89 5D A3 94 84  .?.d.c.b%.7.]...
# 0010: CD 03 41 D8                                        ..A.
# ]
# ]
